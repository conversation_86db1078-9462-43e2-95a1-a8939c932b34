{"name": "project-2024-nlb-gamification-scanner", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@castlenine/svelte-qrcode": "^2.3.0", "@sveltejs/adapter-static": "^3.0.5", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/eslint": "^9.6.0", "@types/node": "^22.7.4", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "chokidar": "^4.0.1", "clsx": "^2.1.1", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "postcss": "^8.4.47", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "socket.io-client": "^4.8.0", "svelte": "^4.2.7", "svelte-check": "^4.0.0", "tailwind-merge": "^2.5.3", "tailwindcss": "^3.4.13", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^5.0.3", "vite-plugin-mkcert": "^1.17.6"}, "type": "module"}