import { PUBLIC_API_KEY, PUBLIC_BACKEND_API_URL, PUBLIC_SOCKET_PORT } from '$env/static/public';
import io from 'socket.io-client';
import { get, writable } from 'svelte/store';
import { isPNG } from './helpers/file';

type ScannerStore = {
	fileBlob: Blob | null;
	status:
		| 'initial'
		| 'initial-error'
		| 'idle'
		| 'file-added'
		| 'file-uploading'
		| 'file-uploaded'
		| 'file-upload-error'
		| 'file-not-found'
		| 'file-type-error';
	userCharacterId: string | null;
};

export const scannerStore = writable<ScannerStore>({
	fileBlob: null,
	status: 'initial',
	userCharacterId: null
});

export const sendFileToRemoteServer = async () => {
	const { fileBlob } = get(scannerStore);

	if (!fileBlob) {
		scannerStore.update((store) => {
			store.status = 'file-not-found';
			return store;
		});

		return;
	}

	try {
		const form = new FormData();
		form.append('file', fileBlob);

		const response = await fetch(`${PUBLIC_BACKEND_API_URL}/coloring/physical/upload`, {
			method: 'POST',
			headers: { 'x-api-key': PUBLIC_API_KEY },
			body: form
		});

		if (response.ok) {
			const data = await response.json();

			scannerStore.update((store) => {
				store.status = 'file-uploaded';
				store.userCharacterId = data.userCharacterId;
				return store;
			});

			console.log(`File uploaded to remote server`);
		} else {
			scannerStore.update((store) => {
				store.status = 'file-upload-error';
				return store;
			});

			console.error(`Error sending file to remote server: ${response.statusText}`);
		}
	} catch (error) {
		scannerStore.update((store) => {
			store.status = 'file-upload-error';
			return store;
		});

		console.error(`Error sending file to remote server: ${error}`);
	}
};

const socketClient = io(`http://localhost:${PUBLIC_SOCKET_PORT}`, {
	autoConnect: true
});

scannerStore.subscribe((store) => {
	console.log(`Scanner store updated: ${store.status} - ${socketClient.connected}`);

	if (store.status === 'idle' && socketClient.connected) {
		socketClient.emit('idle');
	}

	if (store.status === 'file-uploaded' && socketClient.connected) {
		socketClient.emit('file-uploaded', store.userCharacterId);
	}

	if (
		(store.status === 'file-not-found' ||
			store.status === 'file-type-error' ||
			store.status === 'file-upload-error') &&
		socketClient.connected
	) {
		socketClient.emit('file-error');
	}
});

socketClient.on('connect', () => {
	scannerStore.update((store) => {
		store.status = socketClient.connected ? 'idle' : 'initial';
		return store;
	});
});

socketClient.on('disconnect', () => {
	scannerStore.update((store) => {
		store.status = 'initial';
		return store;
	});
});

socketClient.on('connect_error', () => {
	scannerStore.update((store) => {
		store.status = 'initial-error';
		return store;
	});
});

socketClient.on('new-file-added', (buffer: Buffer) => {
	if (isPNG(buffer)) {
		console.log('New file added');

		scannerStore.update((store) => {
			store.fileBlob = new Blob([buffer], { type: 'image/png' });
			store.status = 'file-added';
			return store;
		});
	} else {
		console.log('New file added but not a PNG');

		scannerStore.update((store) => {
			store.status = 'file-type-error';
			return store;
		});
	}
});
