export function isPNG(buffer: Buffer) {
	// The PNG signature in hexadecimal
	const pngSignature = [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a];

	// Ensure the buffer is at least 8 bytes long
	if (buffer.byteLength < 8) {
		return false;
	}

	// Create a Uint8Array from the buffer to access byte data
	const bytes = new Uint8Array(buffer);

	// Check if the first 8 bytes match the PNG signature
	for (let i = 0; i < 8; i++) {
		if (bytes[i] !== pngSignature[i]) {
			return false;
		}
	}

	return true;
}
