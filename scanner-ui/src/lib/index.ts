import { type ClassValue, clsx } from 'clsx';
import { writable } from 'svelte/store';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

type AudioKey = 'error' | 'success' | 'newFile';

export const audioStore = writable<Record<AudioKey, HTMLAudioElement>>({
	error: new Audio('/error.mp3'),
	success: new Audio('/success.mp3'),
	newFile: new Audio('/new-file.mp3')
});

export function hexToString(hex: string) {
	// Remove any spaces from the hex string
	hex = hex.replace(/\s+/g, '');

	let str = '';
	for (let i = 0; i < hex.length; i += 2) {
		// Convert each pair of hex digits (two characters) to a character
		str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
	}
	return str;
}
