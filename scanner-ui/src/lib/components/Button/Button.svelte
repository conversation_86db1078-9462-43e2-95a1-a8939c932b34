<script lang="ts">
	import { cn } from '$lib';
	import buttonVariants from '$lib/assets/btn';

	let className = '';
	let variant: keyof typeof buttonVariants = 'btn_primary_lg';

	export { className as class, variant };
</script>

<button
	class={cn('relative block w-fit select-none transition active:scale-90', className)}
	on:click
	{...$$restProps}
>
	<img src={buttonVariants[variant]} alt="button background" />
	<div
		class={cn('absolute left-1/2 top-[45%] -translate-x-1/2 -translate-y-1/2', {
			'top-1/2': variant.startsWith('btn_secondary')
		})}
	>
		<slot />
	</div>
</button>
