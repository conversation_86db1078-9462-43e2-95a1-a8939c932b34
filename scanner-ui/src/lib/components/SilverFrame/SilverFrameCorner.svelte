<script lang="ts">
	import { cn } from '$lib';

	export let position: 'top-left' | 'top-right' | 'bottom-right' | 'bottom-left';
	export let show = true;
	export let size = 64;
</script>

<svg
	class={cn('absolute z-[2]', {
		hidden: !show,
		'-left-[2px] -top-[2px]': position === 'top-left',
		'-right-[2px] -top-[2px] rotate-90': position === 'top-right',
		'-bottom-[2px] -left-[2px] -rotate-90': position === 'bottom-left',
		'-bottom-[2px] -right-[2px] -rotate-180': position === 'bottom-right'
	})}
	width="{size}"
	viewBox="0 0 74 74"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		d="M71.4415 3.23958C72.4773 1.92808 71.5431 0 69.8719 0L15 0C6.71573 0 0 6.71573 0 15V69.8935C0 71.5609 1.92036 72.4962 3.2331 71.4681L38.9872 43.469C40.1752 42.5386 41.2481 41.4701 42.1833 40.2859L71.4415 3.23958Z"
		fill="#D9DDE4"
	/>
	<path
		d="M12.8623 0.516105C10.3621 0.51616 5.36264 3.01604 2.86189 7.01601V7.01601C1.23621 9.2922 0.362327 12.017 0.362361 14.8141C0.362426 20.143 0.36252 28.985 0.362475 32.7768C0.362464 33.7434 1.14606 34.5162 2.11265 34.5162V34.5162C3.07914 34.5162 3.87534 33.737 3.92292 32.7717C4.10438 29.09 4.65543 21.4597 5.02065 16.5471C5.2419 13.5711 6.15239 10.6543 8.06337 8.36226C9.55049 6.57864 11.3879 4.76193 12.8626 4.51602C15.8623 4.01581 15.3624 0.516049 12.8623 0.516105Z"
		fill="#A0ABD0"
	/>
	<path
		d="M5.8619 10.5424C7.06252 9.94205 8.8619 5.51611 14.3619 4.04236C14.8619 4.01611 15.5144 2.72306 14.8619 1.54235C14.1537 0.260796 12.3619 0.54235 12.3619 0.54235C5.36615 2.01522 2.86534 7.01004 2.36254 8.01429L2.36162 8.01611C1.86094 9.01611 2.68526 9.96412 3.3619 10.5161C3.50386 10.6319 4.66128 11.1427 5.8619 10.5424Z"
		fill="#F1F3F7"
	/>
	<path
		d="M71.4241 3.24376C72.4649 1.93309 71.5315 0 69.8578 0L28.5 0C27.6716 0 27 0.671573 27 1.5V1.5V1.5C27 2.60886 27.8911 3.51188 28.9998 3.52666L58.2104 3.91614C61.1204 3.95494 62.9884 7.02311 61.6869 9.62612V9.62612C61.5627 9.87468 61.4125 10.1094 61.2389 10.3264L58.6264 13.5919C58.0274 14.3408 58.2307 15.4275 59 16V16V16C59.8266 16.6151 60.9807 16.3947 61.6214 15.5878L71.4241 3.24376Z"
		fill="#F1F3F7"
	/>
	<path
		d="M64.3038 6.44954C65.3396 5.13804 64.4054 3.20996 62.7342 3.20996L18.8623 3.20996C10.578 3.20996 3.8623 9.92569 3.8623 18.21V62.1035C3.8623 63.7708 5.78266 64.7061 7.09541 63.6781L36.7882 40.4255C37.9762 39.4952 39.0492 38.4266 39.9844 37.2425L64.3038 6.44954Z"
		fill="url(#paint0_linear_37_745)"
	/>
	<path
		d="M64.3886 6.4287C65.3995 5.11334 64.4617 3.20996 62.8028 3.20996L33.8528 3.20996C31.0309 3.20996 30.5495 7.24718 33.2923 7.91076V7.91076C39.5865 9.43355 43.3239 15.9154 41.4884 22.1257L41.3236 22.6834C41.0908 23.4712 41.6681 24.2656 42.4893 24.2874L42.9059 24.2984C47.6771 24.4251 52.2208 22.261 55.1292 18.4766L64.3886 6.4287Z"
		fill="#BACDE2"
	/>
	<circle
		cx="24"
		cy="24"
		r="24"
		transform="matrix(-0.0372879 0.999305 0.999305 0.0372879 8.77344 6.33154)"
		fill="#E2E8F3"
	/>
	<path
		d="M47.9936 16.2386C48.7998 15.4904 50.0673 15.5327 50.7461 16.3981C53.5658 19.993 55.3098 24.3307 55.751 28.9059C56.2562 34.1439 55.0258 39.4028 52.2492 43.873C49.4726 48.3432 45.3037 51.7769 40.3843 53.6456C36.0876 55.2778 31.4262 55.637 26.954 54.7026C25.8773 54.4777 25.2776 53.3602 25.591 52.3059V52.3059C25.9044 51.2515 27.0124 50.6614 28.0927 50.8685C31.7232 51.5646 35.4899 51.2439 38.9699 49.922C43.0728 48.3634 46.5498 45.4996 48.8655 41.7713C51.1813 38.043 52.2075 33.6569 51.7862 29.2882C51.4288 25.5828 50.0469 22.0642 47.8138 19.1182C47.1494 18.2416 47.1873 16.9868 47.9936 16.2386V16.2386Z"
		fill="#3B496A"
	/>
	<path
		d="M11.3986 39.3107C10.3759 39.7156 9.21003 39.2164 8.89132 38.1636C7.67252 34.1377 7.52996 29.8497 8.49538 25.7317C9.61278 20.9655 12.161 16.6549 15.7983 13.3783C19.4355 10.1017 23.9878 8.01569 28.8444 7.4002C33.0405 6.86843 37.2904 7.4563 41.1677 9.08732C42.1815 9.51382 42.5567 10.7253 42.0477 11.7003V11.7003C41.5386 12.6754 40.3383 13.043 39.3175 12.6333C36.1667 11.3687 32.7349 10.9222 29.3452 11.3518C25.2946 11.8652 21.4979 13.6049 18.4643 16.3377C15.4307 19.0705 13.3054 22.6657 12.3735 26.6409C11.5936 29.9675 11.6806 33.4272 12.6105 36.6924C12.9118 37.7502 12.4213 38.9058 11.3986 39.3107V39.3107Z"
		fill="#7687A7"
	/>
	<circle
		cx="20"
		cy="20"
		r="20"
		transform="matrix(-0.0372879 0.999305 0.999305 0.0372879 12.6211 10.4779)"
		fill="#93A6C7"
	/>
	<path
		d="M14.6306 37.4919C13.7694 37.8058 12.8096 37.3635 12.5681 36.4792C11.6768 33.216 11.6255 29.7704 12.432 26.4666C13.3701 22.6238 15.4267 19.1455 18.3416 16.4715C21.2566 13.7976 24.899 12.048 28.8082 11.4442C32.1692 10.925 35.5976 11.2727 38.772 12.4416C39.6322 12.7583 39.9903 13.7526 39.6034 14.5836V14.5836C39.2164 15.4145 38.2311 15.7659 37.3658 15.4635C34.7914 14.5635 32.0269 14.3057 29.315 14.7246C26.0545 15.2283 23.0166 16.6874 20.5855 18.9176C18.1543 21.1478 16.4391 24.0488 15.6567 27.2538C15.0059 29.9195 15.0248 32.696 15.6998 35.3384C15.9266 36.2265 15.4917 37.1779 14.6306 37.4919V37.4919Z"
		fill="#CBD0D3"
	/>
	<path
		d="M21.0338 46.0128C20.4927 46.7526 19.449 46.9187 18.7565 46.3181C17.5637 45.2834 16.4975 44.111 15.5803 42.8255C15.048 42.0794 15.3121 41.0561 16.0999 40.5874V40.5874C16.8876 40.1187 17.8997 40.3833 18.4443 41.1206C19.1174 42.0318 19.8816 42.8721 20.725 43.6284C21.4074 44.2404 21.5749 45.273 21.0338 46.0128V46.0128Z"
		fill="#CBD0D3"
	/>
	<path
		d="M46.8837 20.6884C47.6345 20.1626 48.676 20.3419 49.1378 21.1337C49.9334 22.4977 50.5646 23.9513 51.0183 25.4638C51.2816 26.3418 50.7016 27.2253 49.8049 27.4149V27.4149C48.9081 27.6046 48.0353 27.0278 47.7575 26.1543C47.4142 25.0747 46.9618 24.0329 46.4073 23.0451C45.9586 22.2457 46.1329 21.2143 46.8837 20.6884V20.6884Z"
		fill="#E8E8E8"
	/>
	<path
		d="M50.189 31.8936C51.105 31.9278 51.8269 32.6996 51.7169 33.6096C51.44 35.901 50.7679 38.1315 49.7266 40.2007C48.436 42.765 46.6086 45.0214 44.3684 46.8166C42.1283 48.6119 39.5279 49.9038 36.744 50.6046C34.4976 51.1701 32.1743 51.3401 29.8776 51.1111C28.9655 51.0202 28.3695 50.1475 28.5357 49.246V49.246C28.702 48.3446 29.5677 47.7574 30.4812 47.8332C32.3071 47.9848 34.1495 47.8348 35.9336 47.3857C38.2555 46.8012 40.4243 45.7237 42.2927 44.2264C44.161 42.7291 45.6852 40.8472 46.7615 38.7085C47.5886 37.065 48.1364 35.2996 48.3863 33.4845C48.5112 32.5764 49.273 31.8594 50.189 31.8936V31.8936Z"
		fill="#E8E8E8"
	/>
	<circle
		cx="17"
		cy="17"
		r="17"
		transform="matrix(-0.0372879 0.999305 0.999305 0.0372879 15.5078 13.5877)"
		fill="url(#paint1_radial_37_745)"
	/>
	<path
		d="M31.2282 48.198C40.6105 48.548 48.5002 41.226 48.8503 31.8437C49.2004 22.4614 41.8783 14.5717 32.496 14.2216C23.1137 13.8715 15.224 21.1936 14.8739 30.5759C14.5238 39.9582 21.8459 47.8479 31.2282 48.198ZM32.3692 17.6192C39.8751 17.8993 45.7327 24.211 45.4526 31.7169C45.1726 39.2227 38.8608 45.0804 31.355 44.8003C23.8491 44.5203 17.9915 38.2085 18.2716 30.7027C18.5516 23.1968 24.8634 17.3392 32.3692 17.6192Z"
		fill="#104A8E"
	/>
	<circle
		cx="8.5"
		cy="8.5"
		r="8.5"
		transform="matrix(-0.0372879 0.999305 0.999305 0.0372879 29.9753 28.1373)"
		fill="#2B70C0"
	/>
	<circle
		cx="3.5"
		cy="3.5"
		r="3.5"
		transform="matrix(-0.0372879 0.999305 0.999305 0.0372879 33.7488 34.2822)"
		fill="#9DC7F9"
	/>
	<path
		d="M23.9209 44.2875C23.4336 45.09 23.6862 46.1442 24.5333 46.5489C27.4472 47.9412 30.7094 48.4798 33.9378 48.0826C37.7309 47.6159 41.2561 45.8849 43.9445 43.1687C46.6329 40.4525 48.3277 36.9097 48.7553 33.1121C49.1193 29.8797 48.5472 26.6232 47.1252 23.7238C46.7117 22.8808 45.655 22.6391 44.8576 23.1346V23.1346C44.0601 23.6302 43.8262 24.6751 44.2184 25.5281C45.249 27.7692 45.6551 30.2592 45.3767 32.7316C45.0346 35.7697 43.6788 38.604 41.528 40.7769C39.3773 42.9498 36.5571 44.3347 33.5227 44.708C31.0533 45.0118 28.5592 44.6313 26.3076 43.6238C25.4506 43.2404 24.4082 43.485 23.9209 44.2875V44.2875Z"
		fill="#6496D0"
	/>
	<path
		d="M31.7576 15.9101C31.7512 14.9713 30.9829 14.2066 30.0494 14.3067C28.0851 14.5173 26.1681 15.0693 24.386 15.9419C22.0938 17.0643 20.0837 18.6884 18.5049 20.6938C16.9261 22.6991 15.8191 25.0342 15.2661 27.5258C14.8361 29.4629 14.7495 31.4559 15.0057 33.4148C15.1275 34.3458 16.0511 34.9131 16.9653 34.699V34.699C17.8794 34.4848 18.4356 33.5699 18.3371 32.6362C18.1833 31.1782 18.2659 29.7011 18.5853 28.2626C19.0277 26.2693 19.9133 24.4013 21.1764 22.797C22.4394 21.1927 24.0475 19.8934 25.8812 18.9955C27.2046 18.3475 28.621 17.9205 30.0744 17.7278C31.0051 17.6044 31.764 16.849 31.7576 15.9101V15.9101Z"
		fill="#213B59"
	/>
	<path
		d="M18.6341 38.8981C17.8223 39.3699 17.5401 40.4164 18.0906 41.177C18.5701 41.8396 19.0969 42.4667 19.6667 43.0535C20.3208 43.727 21.4004 43.6297 22.0053 42.9116V42.9116C22.6101 42.1935 22.5091 41.1276 21.872 40.4379C21.5819 40.1239 21.3068 39.7964 21.0476 39.4565C20.4783 38.7099 19.4458 38.4263 18.6341 38.8981V38.8981Z"
		fill="#6496D0"
	/>
	<path
		d="M44.0315 21.9364C44.7783 21.3673 44.9282 20.2938 44.2874 19.6076C43.0593 18.2923 41.6299 17.1803 40.0531 16.3132C39.2304 15.8608 38.2267 16.2701 37.8587 17.1339V17.1339C37.4907 17.9976 37.8996 18.9873 38.7107 19.4601C39.7576 20.0703 40.7183 20.8177 41.5672 21.6824C42.2249 22.3524 43.2847 22.5054 44.0315 21.9364V21.9364Z"
		fill="#213B59"
	/>
	<path
		d="M42.8382 25.0228C43.5117 24.6431 44.3726 24.8792 44.6832 25.5873C45.6015 27.6813 45.9958 29.9755 45.8216 32.2696C45.612 35.0306 44.5884 37.6675 42.8801 39.8467C41.1719 42.0259 38.8559 43.6497 36.2248 44.5126C34.0387 45.2296 31.7167 45.3946 29.464 45.0029C28.7022 44.8705 28.2674 44.0909 28.4752 43.3462V43.3462C28.683 42.6014 29.4551 42.1755 30.22 42.2888C31.9351 42.543 33.6926 42.3963 35.3522 41.852C37.457 41.1617 39.3099 39.8627 40.6765 38.1193C42.043 36.3759 42.862 34.2665 43.0296 32.0577C43.1619 30.3161 42.8846 28.5744 42.2281 26.9696C41.9354 26.254 42.1646 25.4025 42.8382 25.0228V25.0228Z"
		fill="#104A8E"
	/>
	<path
		d="M19.6836 34.4427C18.9363 34.6411 18.1622 34.1964 18.0394 33.433C17.6881 31.2489 17.8584 29.0055 18.5453 26.8887C19.3767 24.3266 20.9296 22.0587 23.0177 20.357C25.1058 18.6554 27.6405 17.5923 30.3177 17.2952C32.5296 17.0498 34.7612 17.3358 36.8294 18.1208C37.5523 18.3951 37.8316 19.243 37.4864 19.9349V19.9349C37.1413 20.6268 36.3024 20.8987 35.5729 20.6425C33.9929 20.0876 32.3027 19.8921 30.6265 20.0781C28.4848 20.3158 26.457 21.1663 24.7865 22.5276C23.116 23.8889 21.8737 25.7032 21.2086 27.7529C20.6881 29.357 20.5384 31.052 20.7629 32.7114C20.8666 33.4776 20.4309 34.2443 19.6836 34.4427V34.4427Z"
		fill="#2478DB"
	/>
	<circle
		cx="2.5"
		cy="2.5"
		r="2.5"
		transform="matrix(-0.0372879 0.999305 0.999305 0.0372879 23.2412 20.8812)"
		fill="white"
	/>
	<circle cx="2.5" cy="2.5" r="2.5" transform="matrix(-1 0 0 1 55.8623 6.51611)" fill="#F1F3F7" />
	<circle cx="2.5" cy="2.5" r="2.5" transform="matrix(-1 0 0 1 11.8623 51.5161)" fill="#F1F3F7" />
	<defs>
		<linearGradient
			id="paint0_linear_37_745"
			x1="67"
			y1="-3"
			x2="0.500004"
			y2="66"
			gradientUnits="userSpaceOnUse"
		>
			<stop stop-color="#313C5A" stop-opacity="0" />
			<stop offset="1" stop-color="#313C5A" />
		</linearGradient>
		<radialGradient
			id="paint1_radial_37_745"
			cx="0"
			cy="0"
			r="1"
			gradientUnits="userSpaceOnUse"
			gradientTransform="translate(16.785 17.13) rotate(92.1369) scale(23.5 19.4878)"
		>
			<stop stop-color="#0038FF" />
			<stop offset="1" stop-color="#0B131F" />
		</radialGradient>
	</defs>
</svg>
