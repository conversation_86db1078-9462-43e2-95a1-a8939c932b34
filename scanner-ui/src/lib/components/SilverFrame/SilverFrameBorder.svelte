<script lang="ts">
	export let position: 'top' | 'right' | 'bottom' | 'left';
	export let height: number | undefined = undefined;
	export let size = 24;

	let styleByPosition: string = '';

	function getStyleByPosition(_position: typeof position, _size: number, _height?: number) {
		let _style = [
			_height ? `width: ${_height}px` : undefined,
			`height: ${_size}px`,
			`column-gap: ${size}px`
		];

		switch (_position) {
			case 'top':
				_style = _style.concat([
					`left: 50%`,
					`top: 0`,
					`width: 100%`,
					`transform: translateX(-50%)`
				]);
				break;

			case 'right':
				_style = _style.concat([
					`right: ${size / 2}px`,
					`top: calc(100% - ${size / 2}px)`,
					`transform-origin: right`,
					`transform: rotate(90deg)`
				]);
				break;

			case 'bottom':
				_style = _style.concat([
					`bottom: 0`,
					`left: 50%`,
					`width: 100%`,
					`transform: translateX(-50%) rotate(180deg)`
				]);
				break;

			case 'left':
				_style = _style.concat([
					`top: calc(100% - ${size / 2}px)`,
					`left: ${size / 2}px`,
					`transform-origin: left`,
					`transform: rotate(-90deg)`
				]);
				break;
		}

		styleByPosition = _style.join(';');
	}

	$: getStyleByPosition(position, size, height);
</script>

<div
	class="bg-blue-steel absolute flex items-end overflow-hidden rounded-full z-[1]"
	style={styleByPosition}
>
	<div class="flex flex-1">
		<svg height={size * (2 / 3)} viewBox="0 0 78 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M78 16H4.82843C3.04662 16 2.15428 13.8457 3.41421 12.5858L16 0H23L30 7L37 0H78V16Z"
				fill="#AEB7D7"
			/>
			<path
				d="M67 0H37L32 5H69.5858C70.4767 5 70.9229 3.92286 70.2929 3.29289L67 0Z"
				fill="#FEFFFF"
			/>
			<path
				d="M23 0L16 0L12.7071 3.29289C12.0771 3.92286 12.5233 5 13.4142 5H28L23 0Z"
				fill="#FEFFFF"
			/>
		</svg>

		<div class="bg-blue-pastel relative flex-1">
			<div
				class="bg-white-smoke absolute top-0 rounded-full"
				style="right: {size * (2 / 3)}px; height: {Math.floor(
					size / 3
				)}px; width: calc(100% - {size}px)"
			/>
		</div>

		<svg height={size * (2 / 3)} viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 16V0L12.5858 12.5858C13.8457 13.8457 12.9534 16 11.1716 16H0Z" fill="#AEB7D7" />
		</svg>
	</div>

	<div class="flex flex-1" style="column-gap: {size * (2 / 3)}px;">
		<div
			class="bg-gray-light flex-1 rounded-full"
			style="height: {size / 2}px; max-width: {size * (2 / 3)}px; min-width: {size / (24 / 8.67)}px"
		/>
		<div
			class="bg-gray-light flex-1 rounded-full"
			style="height: {size / 2}px; min-width: {size / (24 / 8.67)}px"
		/>
		<div
			class="bg-gray-light flex-1 rounded-full"
			style="height: {size / 2}px; min-width: {size / (24 / 8.67)}px"
		/>
	</div>

	<div class="flex flex-1">
		<svg height={size * (2 / 3)} viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M14 16V0L1.41421 12.5858C0.154281 13.8457 1.04662 16 2.82843 16H14Z"
				fill="#AEB7D7"
			/>
		</svg>

		<div class="bg-blue-pastel relative flex-1">
			<div
				class="absolute top-0 flex"
				style="left: {size * (2 / 3)}px; width: calc(100% - {size}px); column-gap: {size / 3}px"
			>
				<div
					class="bg-white-smoke flex-1 rounded-full"
					style="height: {size / 3}px; max-width: {size * 2.5}px"
				/>
				<div class="bg-white-smoke flex-1 rounded-full" style="height: {size / 3}px;" />
				<div
					class="bg-white-smoke flex-1 rounded-full"
					style="height: {size / 3}px; max-width: {size * (2 / 3)}px"
				/>
			</div>
		</div>

		<svg height={size * (2 / 3)} viewBox="0 0 78 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0 16H73.1716C74.9534 16 75.8457 13.8457 74.5858 12.5858L62 0H55L48 7L41 0H0V16Z"
				fill="#AEB7D7"
			/>
			<path
				d="M11 0H41L46 5H8.41421C7.52331 5 7.07714 3.92286 7.70711 3.29289L11 0Z"
				fill="#FEFFFF"
			/>
			<path
				d="M55 0L62 0L65.2929 3.29289C65.9229 3.92286 65.4767 5 64.5858 5H50L55 0Z"
				fill="#FEFFFF"
			/>
		</svg>
	</div>
</div>
