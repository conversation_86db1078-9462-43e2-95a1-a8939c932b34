<script lang="ts">
	import { cn } from '$lib';
	import SilverFrameBorder from './SilverFrameBorder.svelte';
	import SilverFrameCorner from './SilverFrameCorner.svelte';

	export let className = '';
	export let borderSize: number = 24;
	export let cornerSize: number = 64;

	let frameClientHeight;
</script>

<button
	class={cn('relative h-fit w-fit rounded-xl bg-dark-blue-midnight', className)}
	bind:clientHeight={frameClientHeight}
	on:click|stopPropagation
>
	<SilverFrameBorder position="top" bind:size={borderSize} />
	<SilverFrameBorder position="right" height={frameClientHeight} bind:size={borderSize} />
	<SilverFrameBorder position="bottom" bind:size={borderSize} />
	<SilverFrameBorder position="left" height={frameClientHeight} bind:size={borderSize} />

	<SilverFrameCorner position="top-left" bind:size={cornerSize} />
	<SilverFrameCorner position="top-right" bind:size={cornerSize} />
	<SilverFrameCorner position="bottom-left" bind:size={cornerSize} />
	<SilverFrameCorner position="bottom-right" bind:size={cornerSize} />

	<slot />
</button>
