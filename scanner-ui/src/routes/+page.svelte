<script lang="ts">
	import { audioStore } from '$lib';
	import { onMount } from 'svelte';
	import SilverFrame from '../lib/components/SilverFrame/SilverFrame.svelte';
	import { scannerStore, sendFileToRemoteServer } from '../lib/socket';

	let counter = 0;
	let interval: NodeJS.Timeout;

	onMount(() => {
		const unsub = scannerStore.subscribe((value) => {
			if (interval) clearInterval(interval);

			switch (value.status) {
				case 'file-added': {
					$audioStore.newFile.load();
					$audioStore.newFile.play();

					scannerStore.update((store) => {
						store.status = 'file-uploading';
						return store;
					});

					sendFileToRemoteServer();
					break;
				}

				case 'file-not-found':
				case 'file-type-error':
				case 'file-uploaded':
				case 'file-upload-error': {
					$audioStore[value.status === 'file-uploaded' ? 'success' : 'error'].load();
					$audioStore[value.status === 'file-uploaded' ? 'success' : 'error'].play();
					counter = 1;

					interval = setInterval(() => {
						if (counter > 0) {
							counter--;
							return;
						}

						scannerStore.set({
							fileBlob: null,
							status: 'idle',
							userCharacterId: null
						});
						clearInterval(interval);
					}, 1000);

					break;
				}

				default: {
					counter = 30;
					interval = setInterval(() => {
						if (counter > 0) {
							counter--;
							return;
						}

						scannerStore.set({
							fileBlob: null,
							status: 'idle',
							userCharacterId: null
						});
						clearInterval(interval);
					}, 1000);
					break;
				}
			}
		});

		return () => {
			unsub();
			clearInterval(interval);
		};
	});
</script>

<section class="size-full flex justify-center items-center">
	<SilverFrame
		className="w-2/3 h-2/3 flex flex-col items-center justify-center text-white text-6xl tracking-wider px-10"
		borderSize={12}
		cornerSize={32}
	>
		{#if $scannerStore.status === 'initial'}
			<p>Try to connect with the Scanner...</p>
		{:else if $scannerStore.status === 'initial-error'}
			<p class="text-5xl text-rose-600">Scanner Error</p>
			<br />
			<p>Can't connect with the Scanner</p>
		{:else if $scannerStore.status === 'idle'}
			<p>Welcome to The Woodleigh Mall</p>
		{:else if $scannerStore.status === 'file-added'}
			<p>Scanning...</p>
		{:else if $scannerStore.status === 'file-uploading'}
			<p>Uploading...</p>
		{:else if $scannerStore.status === 'file-uploaded'}
			<p>File uploaded</p>
		{:else if $scannerStore.status === 'file-upload-error'}
			<p class="text-5xl text-rose-600">Scanning Error</p>
			<br />
			<p>Can't upload the file</p>
		{:else if $scannerStore.status === 'file-not-found'}
			<p class="text-5xl text-rose-600">Scanning Error</p>
			<br />
			<p>File not found</p>
		{:else if $scannerStore.status === 'file-type-error'}
			<p class="text-5xl text-rose-600">Scanning Error</p>
			<br />
			<p>File type not supported</p>
		{/if}

		<br />
		{#if $scannerStore.status !== 'idle'}
			{counter}
		{/if}
	</SilverFrame>
</section>
