# Project 2024 Woodleigh Scanner UI

This project is a Svelte-based application designed for the NLB Gamification Scanner UI. It leverages the power of [`create-svelte`](https://github.com/sveltejs/kit/tree/main/packages/create-svelte) to streamline development.

## Developing

Once you've created a project and installed dependencies with `npm install` (or `pnpm install` or `yarn`), start a development server:

```bash
npm run dev

# or start the server and open the app in a new browser tab
npm run dev -- --open
```

## Building

To create a production version of your app:

```bash
npm run build
```

You can preview the production build with `npm run preview`.

> To deploy your app, you may need to install an [adapter](https://kit.svelte.dev/docs/adapters) for your target environment.

## Repository Structure

- `src/`: Contains the source code of the application.
- `public/`: Static files that will be served by the web server.

