require("dotenv").config();

const chokidar = require("chokidar");
const fs = require("fs");
const { join } = require("path");
const { Server } = require("socket.io");

const SOCKET_PORT = process.env.SOCKET_PORT;

let isSending = false;
let detectedFilePath = "";

// Socket.io setup
const io = new Server({ cors: { origin: "*" } });
io.listen(SOCKET_PORT);

// Watcher setup
const folderToWatch = join(process.cwd(), "upload");
const watcher = chokidar.watch(folderToWatch, {
  ignored: /(^|[/\\])\../, // ignore dotfiles
  persistent: true,
});

watcher.on("add", (filePath) => {
  detectedFilePath = filePath;
  if (detectedFilePath.includes(".png") && !isSending) {
    isSending = true;
    console.log(`File ${detectedFilePath} has been added`);
    const file = fs.readFileSync(detectedFilePath);
    io.emit("new-file-added", file);
  }
});

io.on("connection", (socket) => {
  console.log("A user connected");

  socket.on("idle", () => {
    isSending = false;
    detectedFilePath = "";
    console.log("User is idle");
  });

  socket.on("file-uploaded", () => {
    console.log(`[${detectedFilePath}] moved to success folder.`);
    fs.unlinkSync(detectedFilePath);
  });

  socket.on("file-error", () => {
    const filename = `${Date.now()}.png`;

    move(detectedFilePath, join(process.cwd(), "error", filename), () => {
      console.log(`[${filename}] moved to error folder.`);
    });

    detectedFilePath = "";
  });

  socket.on("disconnect", () => {
    isSending = false;
    detectedFilePath = "";
    console.log("User disconnected");
  });
});

const move = (oldPath, newPath, callback) => {
  fs.rename(oldPath, newPath, function (err) {
    if (err) {
      if (err.code === "EXDEV") {
        copy();
      } else {
        callback(err);
      }
      return;
    }
    callback();
  });

  function copy() {
    var readStream = fs.createReadStream(oldPath);
    var writeStream = fs.createWriteStream(newPath);

    readStream.on("error", callback);
    writeStream.on("error", callback);

    readStream.on("close", function () {
      fs.unlinkSync(oldPath, callback);
    });

    readStream.pipe(writeStream);
  }
};

console.log(`[${folderToWatch}] Watching for file changes...`);
