{"name": "project_2024_woodleighmall_colour_and_scan_projection_wall", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@esotericsoftware/spine-core": "^4.2.81", "@esotericsoftware/spine-phaser-v4": "^4.2.81", "@tailwindcss/vite": "^4.1.6", "dayjs": "^1.11.13", "phaser": "^4.0.0-rc.1", "react": "^19.1.0", "react-dom": "^19.1.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.4", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.26.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.1.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "typescript": "~5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5"}}