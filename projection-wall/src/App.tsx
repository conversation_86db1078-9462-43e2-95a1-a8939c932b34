import * as spine from "@esotericsoftware/spine-phaser-v4";
import Phaser from "phaser";
import { useEffect, useRef } from "react";
import { io, Socket } from "socket.io-client";
import { ASSETS_URL, GROUND_LIMIT } from "./utils/constants";
import { date } from "./utils/datetime";
import { Character, NewCharacterPayload } from "./utils/types";

class ProjectionScene extends Phaser.Scene {
  private socket: Socket | undefined;
  private characters: Record<string, spine.SpineGameObject> = {};
  private characterLoaded = false;

  constructor() {
    super({ key: "ProjectionScene" });
  }

  preload() {
    this.load.setBaseURL(ASSETS_URL);
    this.load.setCORS("anonymous");
  }

  create() {
    this.socket = io(import.meta.env.VITE_SOCKET_URL, {
      autoConnect: true,
      transports: [import.meta.env.VITE_SOCKET_TRANSPORTS],
      path: import.meta.env.VITE_SOCKET_PATH,
      auth: { apiKey: import.meta.env.VITE_API_KEY },
      secure: true,
    });

    this.socket?.on("projection:connected", (currentCharacters: NewCharacterPayload[]) => {
      if (this.characterLoaded) {
        return;
      }

      const characterIds = Object.keys(this.characters);
      currentCharacters.forEach((character, index) => {
        const keyIndex = characterIds.findIndex((id) => id === character.id);

        if (keyIndex === -1) {
          const adjustedPayload = {
            ...character,
            ttl: character.ttl + index,
          };
          this.loadPhysicalCharacterAssets(adjustedPayload, (jsonKey, atlasKey) => {
            this.newPhysicalCharacter(adjustedPayload, jsonKey, atlasKey);
          });
        } else {
          characterIds.splice(keyIndex, 1);
        }
      });

      this.characterLoaded = true;
    });

    this.socket?.on("projection:new-character", (characterPayload: NewCharacterPayload) => {
      this.loadPhysicalCharacterAssets(characterPayload, (jsonKey, atlasKey) => {
        this.newPhysicalCharacter(characterPayload, jsonKey, atlasKey);
      });
    });

    this.socket?.on("projection:character-exit", ({ exitId }: { exitId: string }) => {
      const character = this.characters[exitId];
      character?.setData("leaving", true);
      const Exit = character?.animationState.setAnimation(0, "Exit");
      this.time.delayedCall(Exit?.animationEnd * 1000, () => {
        character?.destroy(true);
        this.socket?.emit("projection:character-exited", { exitId });
        delete this.characters[exitId];
      });
    });

    this.socket?.on("projection:character-resize", ({ characterId, size }: { characterId: string; size: number }) => {
      this.characters[characterId].setData("size", size);
      this.characters[characterId].setScale(
        this.characters[characterId].scaleX < 0 ? -size : size,
        this.characters[characterId].scaleY < 0 ? -size : size
      );
    });

    this.socket?.on("projection:character-speed", ({ characterId, speed }: { characterId: string; speed: number }) => {
      this.characters[characterId].setData("speed", speed);
    });
  }

  update() {
    Object.values(this.characters).forEach((character: spine.SpineGameObject) => {
      this.characterMovement(character);

      if (character.getData("leaving")) {
        const characterBody = character.body as Phaser.Physics.Arcade.Body | null;

        characterBody?.setVelocity(0, 0);
      }
    });
  }

  characterMovement(character: spine.SpineGameObject) {
    const dx = character.getData("targetX") - character.x;
    const dy = character.getData("targetY") - character.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const angle = Math.atan2(dy, dx);

    if (distance > 10) {
      (character.body as Phaser.Physics.Arcade.Body)?.setVelocityX(Math.cos(angle) * character.getData("speed"));
      (character.body as Phaser.Physics.Arcade.Body)?.setVelocityY(Math.sin(angle) * character.getData("speed"));
    } else {
      (character.body as Phaser.Physics.Arcade.Body)?.setVelocityX(0);
      (character.body as Phaser.Physics.Arcade.Body)?.setVelocityY(0);
    }
  }

  loadPhysicalCharacterAssets(
    characterPayload: NewCharacterPayload,
    onAssetLoadCompleted: (jsonKey: string, atlasKey: string) => void
  ) {
    const assetFolderPath = `physical/customized/${characterPayload.character.name}/${date(characterPayload.createdAt)
      .tz("Asia/Singapore")
      .format("YYYY-MM-DD")}/${characterPayload.userCharacterId}`;

    const jsonKey = `p-character-json-${characterPayload.userCharacterId}`;
    const atlasKey = `p-character-atlas-${characterPayload.userCharacterId}`;

    if (this.cache.json.has(jsonKey) && this.cache.text.has(atlasKey)) {
      return onAssetLoadCompleted(jsonKey, atlasKey);
    }

    this.load.spineJson(jsonKey, `${assetFolderPath}/${characterPayload.character.name}.json`);
    this.load.spineAtlas(atlasKey, `${assetFolderPath}/${characterPayload.character.name}.atlas`);

    this.load.start();

    this.load.on(`filecomplete-spineSkeletonData-${jsonKey}`, (jsonKey: string) => {
      this.load.on(`filecomplete-spineAtlasData-${atlasKey}`, (atlasKey: string) => {
        onAssetLoadCompleted(jsonKey, atlasKey);
      });
    });
  }

  newPhysicalCharacter(payload: NewCharacterPayload, jsonKey: string, atlasKey: string) {
    const newCharacter = this.add.spine(0, 0, jsonKey, atlasKey).setDepth(2);

    this.physics.add.existing(newCharacter);

    newCharacter.animationState.setAnimation(0, "walk", true);

    this.characters[payload.id] = newCharacter;

    const nextIndex = Object.entries(this.characters).length;
    const isEven = nextIndex % 2 === 0;

    if (isEven) {
      newCharacter.setPosition(1920, 0);
    } else {
      newCharacter.setPosition(0, 0);
    }

    let targetPosX = 0;
    let targetPosY = 0;

    if (isEven) {
      targetPosX = nextIndex <= 10 ? nextIndex * 85 : (nextIndex - 10) * 85;
      targetPosY = nextIndex <= 10 ? GROUND_LIMIT : GROUND_LIMIT - 130;
    } else {
      targetPosX = nextIndex <= 10 ? 1920 - nextIndex * 85 - 85 : 1920 - (nextIndex - 10) * 85;
      targetPosY = nextIndex <= 10 ? GROUND_LIMIT : GROUND_LIMIT - 130;
    }

    newCharacter.setData<Character>({
      ...payload.character,
      targetX: targetPosX,
      targetY: targetPosY,
    });

    // Set the character's size
    newCharacter.setScale(newCharacter.getData("size"));

    // Calculate time to reach destination
    const dx = targetPosX - newCharacter.x;
    const dy = targetPosY - newCharacter.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const timeToReach = distance / newCharacter.getData("speed"); // in seconds
    this.time.delayedCall(timeToReach * 1000, () => {
      newCharacter.animationState.setAnimation(0, "idle", true);
    });

    this.time.delayedCall(payload.ttl * 1000, () => {
      this.socket?.emit("projection:character-timeout", payload.id);
    });
  }
}

export default function App() {
  const gameRef = useRef<Phaser.Game>(undefined);

  useEffect(() => {
    const config: Phaser.Types.Core.GameConfig = {
      type: Phaser.WEBGL,
      scale: {
        width: 1920,
        height: 1080,
        fullscreenTarget: "phaser-container",
        mode: Phaser.Scale.FIT,
      },
      parent: "phaser-container",
      transparent: true,
      plugins: {
        scene: [{ key: "spine.SpinePlugin", plugin: spine.SpinePlugin, mapping: "spine" }],
      },
      physics: {
        default: "arcade",
      },
    };
    gameRef.current = new Phaser.Game(config);
    gameRef.current.scene.add("ProjectionScene", ProjectionScene, true);

    return () => {
      gameRef.current?.destroy(true);
    };
  }, []);

  return (
    <div id="phaser-container" className="w-screen h-screen relative invert">
      <video className="absolute left-0 top-0 w-full h-full -z-50" src="/background/default.mp4" muted autoPlay loop></video>
    </div>
  );
}
