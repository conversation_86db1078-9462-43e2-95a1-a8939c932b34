export type NewCharacterPayload = {
  id: string;
  userCharacterId: string;
  customizeAttackments: Record<string, string>;
  character: Character;
  type: "PHYSICAL";
  ttl: number;
  createdAt: string;
};

export type Character = {
  id: string;
  name: string;
  spawn: string;
  movement: string;
  size: number;
  speed: number;
  faceDirection: string;
  createdAt: string;
  updatedAt: string;

  // runtime data
  targetX: number;
  targetY: number;
};
