services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: colour-scan-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: colour_and_scan_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - '5433:5432'
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - colour-scan-dev-network

  # Redis for Development
  redis-dev:
    image: redis:7-alpine
    container_name: colour-scan-redis-dev
    restart: unless-stopped
    ports:
      - '6380:6379'
    volumes:
      - redis_dev_data:/data
    networks:
      - colour-scan-dev-network

  # Development database only (app runs locally)
  # To use: docker-compose -f docker-compose.dev.yml up -d

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  colour-scan-dev-network:
    driver: bridge
