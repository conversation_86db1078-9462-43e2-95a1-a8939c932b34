{"name": "server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "docker:build": "docker build -t colour-scan-api .", "docker:run": "docker run -p 3000:3000 colour-scan-api", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:prod": "docker-compose up -d", "docker:prod:down": "docker-compose down"}, "dependencies": {"@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.1.3", "@prisma/client": "^6.11.1", "@types/bcryptjs": "^3.0.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.1", "cache-manager": "^7.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "helmet": "^8.1.0", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "prisma": "^6.11.1", "redis": "^5.5.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@types/express": "^5.0.3", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}