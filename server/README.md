# Colour and Scan API - Enterprise NestJS Server

A production-ready, enterprise-grade NestJS API server with comprehensive features including authentication, real-time communication, job queuing, task scheduling, and more.

## 🚀 Features

### Core Technologies
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Caching & Queuing**: Redis with BullMQ
- **Real-time**: WebSockets with Socket.IO
- **Authentication**: JWT with Passport
- **Documentation**: Swagger/OpenAPI
- **Logging**: Structured logging with Pino
- **Containerization**: Docker & Docker Compose

### Enterprise Features
- ✅ **Modular Architecture** - Feature-based modules
- ✅ **Global Validation** - Request/response validation with class-validator
- ✅ **Global Exception Handling** - Consistent error responses
- ✅ **Request/Response Transformation** - Standardized API responses
- ✅ **Security Middleware** - Helmet, CORS, rate limiting
- ✅ **Authentication & Authorization** - JWT with role-based access control
- ✅ **Custom Decorators** - @User(), @Roles(), @Public()
- ✅ **Structured Logging** - Production-ready logging with Pino
- ✅ **Task Scheduling** - Cron jobs and scheduled tasks
- ✅ **Job Queuing** - Background job processing with BullMQ
- ✅ **Health Checks** - Application and service health monitoring
- ✅ **API Documentation** - Auto-generated Swagger documentation

## 📁 Project Structure

```
src/
├── common/                 # Shared utilities
│   ├── decorators/        # Custom decorators (@User, @Roles, @Public)
│   ├── filters/           # Exception filters
│   ├── guards/            # Authentication & authorization guards
│   ├── interceptors/      # Request/response interceptors
│   ├── middleware/        # Custom middleware
│   └── pipes/             # Validation pipes
├── config/                # Configuration management
├── database/              # Database service and utilities
├── modules/               # Feature modules
│   ├── auth/             # Authentication module
│   ├── health/           # Health check module
│   ├── scheduler/        # Task scheduling module
│   ├── users/            # User management module
│   └── websocket/        # WebSocket gateway
├── queue/                 # Job queue system
├── app.module.ts         # Root application module
└── main.ts               # Application entry point
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (optional)

### Local Development

1. **Clone and install dependencies**
```bash
cd server
npm install
```

2. **Environment setup**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Database setup**
```bash
# Start development databases
npm run docker:dev

# Generate Prisma client
npm run prisma:generate

# Run database migrations
npm run prisma:migrate
```

4. **Start development server**
```bash
npm run start:dev
```

The API will be available at:
- **API**: http://localhost:3000/api/v1
- **Swagger Docs**: http://localhost:3000/api/docs
- **Health Check**: http://localhost:3000/api/v1/health

### Docker Development

```bash
# Start all services (PostgreSQL + Redis)
npm run docker:dev

# Stop services
npm run docker:dev:down
```

### Production Deployment

```bash
# Build and start all services
npm run docker:prod

# Stop services
npm run docker:prod:down
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development/production) | `development` |
| `PORT` | Server port | `3000` |
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `REDIS_HOST` | Redis host | `localhost` |
| `REDIS_PORT` | Redis port | `6379` |
| `JWT_SECRET` | JWT signing secret | Required |
| `JWT_EXPIRES_IN` | JWT expiration time | `7d` |
| `CORS_ORIGIN` | Allowed CORS origins | `http://localhost:3000` |
| `LOG_LEVEL` | Logging level | `info` |

See `.env.example` for complete configuration options.