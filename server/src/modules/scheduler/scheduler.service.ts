import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, Interval, Timeout } from '@nestjs/schedule';
import { PrismaService } from 'src/database/prisma.service';
import { QueueService } from 'src/queue/queue.service';

@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly queueService: QueueService,
  ) {}

  // Run every day at midnight
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleDailyCleanup() {
    this.logger.log('Running daily cleanup task');
    
    try {
      // Example: Clean up old logs, temporary files, etc.
      // You can add your specific cleanup logic here
      
      this.logger.log('Daily cleanup completed successfully');
    } catch (error) {
      this.logger.error('Daily cleanup failed:', error);
    }
  }

  // Run every hour
  @Cron(CronExpression.EVERY_HOUR)
  async handleHourlyTasks() {
    this.logger.log('Running hourly tasks');
    
    try {
      // Example: Send reminder emails, update statistics, etc.
      const queueStats = await this.queueService.getEmailQueueStats();
      this.logger.log(`Email queue stats: ${JSON.stringify(queueStats)}`);
      
      this.logger.log('Hourly tasks completed successfully');
    } catch (error) {
      this.logger.error('Hourly tasks failed:', error);
    }
  }

  // Run every 30 minutes
  @Cron('0 */30 * * * *')
  async handleHealthCheck() {
    this.logger.log('Running health check');
    
    try {
      // Check database connection
      await this.prisma.$queryRaw`SELECT 1`;
      
      // Check queue health
      const emailStats = await this.queueService.getEmailQueueStats();
      const fileStats = await this.queueService.getFileProcessingQueueStats();
      
      this.logger.log('Health check passed', {
        database: 'healthy',
        emailQueue: emailStats,
        fileQueue: fileStats,
      });
    } catch (error) {
      this.logger.error('Health check failed:', error);
    }
  }

  // Run every 10 seconds (for demonstration)
  @Interval(10000)
  handleInterval() {
    this.logger.debug('Interval task executed every 10 seconds');
  }

  // Run once after 3 seconds when the application starts
  @Timeout(3000)
  handleTimeout() {
    this.logger.log('Application startup tasks completed');
  }

  // Custom method to schedule a one-time task
  async scheduleOneTimeTask(delay: number, taskData: any) {
    setTimeout(async () => {
      this.logger.log(`Executing scheduled task: ${JSON.stringify(taskData)}`);
      
      try {
        // Add your task logic here
        // For example, send a delayed email
        await this.queueService.addEmailJob({
          to: taskData.email,
          subject: 'Scheduled Notification',
          template: 'scheduled-notification',
          context: taskData,
        });
        
        this.logger.log('Scheduled task completed successfully');
      } catch (error) {
        this.logger.error('Scheduled task failed:', error);
      }
    }, delay);
  }
}
