import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/database/prisma.service';
import { QueueService } from 'src/queue/queue.service';

@Injectable()
export class HealthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queueService: QueueService,
  ) {}

  async check() {
    try {
      // Basic database check
      await this.prisma.$queryRaw`SELECT 1`;

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        error: error.message,
      };
    }
  }

  async detailedCheck() {
    const checks = {
      database: await this.checkDatabase(),
      queues: await this.checkQueues(),
      memory: this.checkMemory(),
      environment: this.checkEnvironment(),
    };

    const isHealthy = Object.values(checks).every(
      (check: any) => check.status === 'ok',
    );

    return {
      status: isHealthy ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks,
    };
  }

  private async checkDatabase() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return { status: 'ok', message: 'Database connection successful' };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  private async checkQueues() {
    try {
      const emailStats = await this.queueService.getEmailQueueStats();
      const fileStats = await this.queueService.getFileProcessingQueueStats();

      return {
        status: 'ok',
        message: 'Queue systems operational',
        details: {
          email: emailStats,
          fileProcessing: fileStats,
        },
      };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  private checkMemory() {
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;
    const memoryUsagePercentage = (usedMemory / totalMemory) * 100;

    return {
      status: memoryUsagePercentage < 90 ? 'ok' : 'warning',
      message: `Memory usage: ${memoryUsagePercentage.toFixed(2)}%`,
      details: {
        heapUsed: `${Math.round(usedMemory / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(totalMemory / 1024 / 1024)} MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`,
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
      },
    };
  }

  private checkEnvironment() {
    return {
      status: 'ok',
      message: 'Environment variables loaded',
      details: {
        nodeEnv: process.env.NODE_ENV,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };
  }
}
