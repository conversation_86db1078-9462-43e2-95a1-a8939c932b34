import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EmailJobData } from '../queue.service';

@Processor('email')
export class EmailProcessor {
  private readonly logger = new Logger(EmailProcessor.name);

  @Process('send-email')
  async handleSendEmail(job: Job<EmailJobData>) {
    this.logger.log(`Processing email job ${job.id}`);
    
    const { to, subject, template, context } = job.data;
    
    try {
      // Here you would integrate with your email service (SendGrid, AWS SES, etc.)
      // For now, we'll just log the email details
      this.logger.log(`Sending email to: ${to}`);
      this.logger.log(`Subject: ${subject}`);
      this.logger.log(`Template: ${template}`);
      this.logger.log(`Context: ${JSON.stringify(context)}`);
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.logger.log(`Email sent successfully to ${to}`);
      
      return { success: true, sentAt: new Date() };
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}:`, error);
      throw error;
    }
  }
}
