import { PartialType, OmitType, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateApplicationDto } from './create-application.dto';
import { IsNumber, IsOptional } from 'class-validator';

export class UpdateApplicationDto extends PartialType(
  OmitType(CreateApplicationDto, ['id']),
) {}

export class UpdateApplicationSettingsDto {
  @ApiPropertyOptional({
    description: 'Min amount of characters on projection wall',
    example: 5,
  })
  @IsNumber()
  @IsOptional()
  minCharacters: number;

  @ApiPropertyOptional({
    description: 'Max amount of characters on projection wall',
    example: 20,
  })
  @IsNumber()
  @IsOptional()
  maxCharacters: number;

  @ApiPropertyOptional({
    description: 'Max amount of characters on projection wall',
    example: 20,
  })
  @IsNumber()
  @IsOptional()
  ttlCharacter: number;

  @ApiPropertyOptional({
    description: 'Size of all character on projection wall',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  size: number;

  @ApiPropertyOptional({
    description: 'Speed of all character on projection wall',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  speed: number;
}
