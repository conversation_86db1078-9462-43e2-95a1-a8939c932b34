import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

export class CreateApplicationDto {
  @ApiProperty({
    description: 'Unique App Id',
    example: 'app-1',
  })
  @IsString()
  @MinLength(3, { message: 'App id must be at least 3 characters' })
  id: string;

  @ApiPropertyOptional({
    description: 'Application name',
    example: 'Library 01',
  })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: 'Application name must be at least 3 characters' })
  @MaxLength(50, { message: 'Application name must be at most 50 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Projection wall background id',
    example: 'This is a library application',
  })
  @IsString()
  @IsOptional()
  backgroundId?: string;
}
