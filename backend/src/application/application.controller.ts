import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/role.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { ApplicationService } from './application.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import {
  UpdateApplicationDto,
  UpdateApplicationSettingsDto,
} from './dto/update-application.dto';

@ApiTags('Application')
@Controller('application')
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  @Post()
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  create(@Body() createApplicationDto: CreateApplicationDto) {
    return this.applicationService.create(createApplicationDto);
  }

  @Get()
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiQuery({ name: 'page', example: 1, required: true })
  @ApiQuery({ name: 'limit', example: 10, required: true })
  findAll(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.applicationService.findAll({ page, limit });
  }

  @Get(':id')
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  findOne(@Param('id') id: string) {
    return this.applicationService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  update(@Param('id') id: string, @Body() dto: UpdateApplicationDto) {
    return this.applicationService.update(id, dto);
  }

  @Get(':id/settings')
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  findOneSettings(@Param('id') appId: string) {
    return this.applicationService.findOneSetting(appId);
  }

  @Patch(':id/settings')
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  updateSettings(
    @Param('id') appId: string,
    @Body() dto: UpdateApplicationSettingsDto,
  ) {
    return this.applicationService.updateSetting(appId, dto);
  }

  @Get(':id/projection/status')
  @Roles('ADMIN')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  projectionStatus(@Param('id') appId: string) {
    return this.applicationService.projectionStatus(appId);
  }

  @Put(':id/characters/flush')
  flushCharacter(@Param('id') appId: string) {
    return this.applicationService.flushCharacter(appId);
  }

  @Put(':id/queue/flush')
  flushQueue(@Param('id') appId: string) {
    return this.applicationService.flushQueue(appId);
  }
}
