import { BullModule } from '@nestjs/bull';
import { CacheModule } from '@nestjs/cache-manager';
import { HttpStatus, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import {
  CustomPrismaModule,
  providePrismaClientExceptionFilter,
} from 'nestjs-prisma';
import { join } from 'path';
import { ApplicationModule } from './application/application.module';
import { AuthModule } from './auth/auth.module';
import { ColoringModule } from './coloring/coloring.module';
import { extendedPrismaClient } from './libs/prisma/prisma.extension';
import { ProjectionModule } from './projection/projection.module';
import { UserModule } from './user/user.module';
import { ScheduleModule } from '@nestjs/schedule';
import { InpaintModule } from 'src/inpaint/inpaint.module';

@Module({
  providers: [
    providePrismaClientExceptionFilter({
      P2000: HttpStatus.BAD_REQUEST,
      P2002: HttpStatus.CONFLICT,
      P2025: HttpStatus.NOT_FOUND,
      P2014: HttpStatus.UNPROCESSABLE_ENTITY,
    }),
  ],
  imports: [
    CustomPrismaModule.forRootAsync({
      isGlobal: true,
      name: 'PrismaService',
      useFactory: () => {
        return extendedPrismaClient;
      },
    }),
    ConfigModule.forRoot({ isGlobal: true }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..'),
      renderPath: 'public',
      serveStaticOptions: {
        setHeaders: (res, path, stat) => {
          if (path.endsWith('.mp4')) {
            res.setHeader('Content-Type', 'video/mp4');
            res.setHeader('Content-Disposition', 'inline');
          }
        },
      },
    }),
    CacheModule.register({
      isGlobal: true,
    }),
    BullModule.forRoot({
      redis: {
        host: 'localhost',
        port: 6379,
      },
    }),
    ScheduleModule.forRoot(),
    InpaintModule,
    ApplicationModule,
    AuthModule,
    UserModule,
    ColoringModule,
    ProjectionModule,
  ],
})
export class AppModule {}
