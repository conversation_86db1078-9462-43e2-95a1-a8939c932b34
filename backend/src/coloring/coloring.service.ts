import { InjectQueue } from '@nestjs/bull';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { UserCharacterType } from '@prisma/client';
import { Queue } from 'bull';
import { Image, createCanvas, loadImage } from 'canvas';
import {
  copyFileSync,
  existsSync,
  mkdirSync,
  readFileSync,
  writeFileSync,
} from 'fs';
import jsQR from 'jsqr';
import { CustomPrismaService } from 'nestjs-prisma';
import { join } from 'path';
import sharp from 'sharp';
import { ApplicationService } from 'src/application/application.service';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { ProjectionGateway } from 'src/projection/projection.gateway';
import { CharacterJobData } from 'src/projection/projection.processor';
import {
  Attachment,
  CharacterJSON,
  CharacterPaperSize,
  Skeleton,
} from './coloring.type';
import { InpaintService } from 'src/inpaint/inpaint.service';

@Injectable()
export class ColoringService {
  private readonly logger = new Logger(ColoringService.name);

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly projectionGateway: ProjectionGateway,
    private readonly applicationService: ApplicationService,
    @InjectQueue('projection-queue')
    private projectionQueue: Queue<CharacterJobData>,
    private readonly inpaintService: InpaintService,
  ) {}

  async createNewPhysicalCustomizeCharacterAnon(
    appId: string,
    file: Express.Multer.File,
  ) {
    try {
      // const headBuffer = readFileSync(
      //   join(process.cwd(), 'public', 'head.png'),
      // );
      // const maskBuffer = readFileSync(
      //   join(process.cwd(), 'public', 'mask-head.png'),
      // );

      // const outputBuffer = await this.inpaintService.inpaintBuffer(
      //   headBuffer,
      //   maskBuffer,
      //   5,
      // );

      // writeFileSync(
      //   join(process.cwd(), 'public', 'head-inpainted.png'),
      //   outputBuffer,
      // );

      const { characterName, fileBuffer, rawFileBuffer, templateRotation } =
        await this.processRawImage(file.buffer);

      const setting = await this.applicationService.findOneSetting(appId);

      const enteredCharacters = await this.prisma.client.projection.findMany({
        where: {
          appId,
          enteredAt: { not: null },
          exitedAt: null,
          exiting: false,
        },
      });

      const isProjectionAvailable =
        enteredCharacters.length < setting.maxCharacters;

      const { id: newUserCharacterId, ...newUserCharacter } =
        await this.prisma.client.userCharacter.create({
          data: {
            user: {
              connectOrCreate: {
                where: { username: 'physical_anonymous' },
                create: { username: 'physical_anonymous' },
              },
            },
            type: UserCharacterType.PHYSICAL,
            character: { connect: { name: characterName } },
          },
          select: {
            id: true,
            character: true,
            type: true,
          },
        });

      const newProjection = await this.prisma.client.projection.create({
        data: {
          app: { connect: { id: appId } },
          userCharacter: { connect: { id: newUserCharacterId } },
          enteredAt: isProjectionAvailable ? dateWithTz().toDate() : null,
        },
        select: {
          id: true,
          createdAt: true,
        },
      });

      await this.generateAssetFromRawFile({
        ...(await this.storeRawFile({
          id: newUserCharacterId,
          characterName: newUserCharacter.character.name,
          rawFileBuffer,
          fileBuffer,
        })),
        templateRotation,
      });

      if (isProjectionAvailable) {
        setTimeout(() => {
          this.projectionGateway.server
            .to(appId)
            .emit('projection:new-character', {
              ttl: setting.ttlCharacter,
              ...newProjection,
              userCharacterId: newUserCharacterId,
              ...newUserCharacter,
            });
        }, 3000);
      } else {
        const hasTimedOutCharacters = enteredCharacters.some(
          ({ enteredAt }) => {
            return (
              dateWithTz().diff(dateWithTz(enteredAt), 'seconds') >=
              setting.ttlCharacter
            );
          },
        );

        if (hasTimedOutCharacters) {
          await this.projectionQueue.resume();
        } else {
          await this.projectionQueue.pause();
        }

        await this.prisma.client.projection.update({
          where: { id: newProjection.id },
          data: { waiting: true },
        });

        await this.projectionQueue.add(
          {
            appId,
            projectionId: newProjection.id,
            userCharacterId: newUserCharacterId,
          },
          { removeOnComplete: true, delay: hasTimedOutCharacters ? 3000 : 0 },
        );
      }

      return {
        userCharacterId: newUserCharacterId,
      };
    } catch (error) {
      throw new InternalServerErrorException({
        error: `Unable to generate asset! Please try again!`,
        message: error.message,
      });
    }
  }

  private async scanQRCode(fileBuffer: Buffer) {
    try {
      const image = sharp(fileBuffer);
      const { width } = await image.metadata();

      const { data, info } = await sharp(fileBuffer)
        .ensureAlpha()
        .raw()
        .extract({
          left: 0,
          top: 0,
          width: Math.round(width * 0.13),
          height: Math.round(width * 0.13),
        })
        .toBuffer({ resolveWithObject: true });

      // Get the image data
      const imageData = {
        data: new Uint8ClampedArray(data.buffer),
        width: info.width,
        height: info.height,
      };

      // Use jsQR to decode the QR code
      const decodedQR = jsQR(
        imageData.data,
        imageData.width,
        imageData.height,
        {
          inversionAttempts: 'dontInvert',
        },
      );

      if (!decodedQR) {
        throw new NotFoundException('QR code not found in the image.');
      }

      return decodedQR;
    } catch (error) {
      this.logger.error('Error when scanning QR code:', error);
      throw new InternalServerErrorException('QR code not found in the image.');
    }
  }

  private async borderDetectionCrop(fileBuffer: Buffer) {
    const image = sharp(fileBuffer);

    // Get image metadata to understand its dimensions
    const { width, height, channels } = await image.metadata();

    // Convert to grayscale and threshold
    const thresholdedBuffer = await image
      .grayscale()
      .threshold(100) // Converts to binary image: 0 (black) and 255 (white)
      .toBuffer();

    // Load the thresholded image data to find borders
    const { data } = await sharp(thresholdedBuffer)
      .raw()
      .toBuffer({ resolveWithObject: true });

    let left = width,
      right = 0,
      top = height,
      bottom = 0;

    const blackThreshold = 80;

    // Detect top border
    for (let y = 0; y < height; y++) {
      let blackPixelCount = 0;
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * channels;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }
      if (blackPixelCount >= 1600) {
        top = y;
        break;
      }
    }

    // Detect bottom border
    for (let y = height - 1; y >= 0; y--) {
      let blackPixelCount = 0;
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * channels;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }
      if (blackPixelCount >= 1600) {
        bottom = y;
        break;
      }
    }

    // Detect left border
    for (let x = 0; x < width; x++) {
      let blackPixelCount = 0;
      for (let y = 0; y < height; y++) {
        const index = (y * width + x) * channels;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }

      if (blackPixelCount >= 2750) {
        left = x;
        break;
      }
    }

    // Detect right border
    for (let x = width; x >= 0; x--) {
      let blackPixelCount = 0;
      for (let y = 0; y < height; y++) {
        const index = (y * width + x) * channels;
        const [r, g, b] = [data[index], data[index + 1], data[index + 2]];
        if (r < blackThreshold && g < blackThreshold && b < blackThreshold)
          blackPixelCount++;
      }
      if (blackPixelCount >= 2750) {
        right = x;
        break;
      }
    }

    // Define the crop area based on detected bounding box
    const cropWidth = right - left;
    const cropHeight = bottom - top;

    if (cropWidth > 0 && cropHeight > 0) {
      console.log('Successfully crop image using border detection.');
      return await sharp(fileBuffer)
        .extract({ left, top, width: cropWidth, height: cropHeight })
        .toBuffer();
    } else {
      console.log(
        'Could not detect a valid character border or image is too small.',
      );
      return null;
    }
  }

  private async processRawImage(fileBuffer: Buffer) {
    try {
      const rawFileBuffer = fileBuffer;
      let scanResult = await this.scanQRCode(fileBuffer);

      if (scanResult.data.split('-')[1] === 'b') {
        fileBuffer = await sharp(fileBuffer).rotate(180).toBuffer();
        scanResult = await this.scanQRCode(fileBuffer);
      }

      const characterName = scanResult.data.split('-')[0];

      const borderDetectionCropped = await this.borderDetectionCrop(fileBuffer);

      if (borderDetectionCropped) {
        fileBuffer = borderDetectionCropped;
      } else {
        // all these number are for 300DPI scanned paper image (2480x3507)
        fileBuffer = await sharp(fileBuffer)
          .extract({
            left: Math.round(scanResult.location.topRightCorner.x + 22), // this magic number is the distance from the topRightCorner of the QR code dot to the left border of the coloring frame (decent number)
            top: Math.round(scanResult.location.topRightCorner.y + 199), // this magic number is the distance from the topRightCorner of the QR code dot to the top border of the coloring frame (decent number)
            width: 1926, // width and height of the frame in the actual paper
            height: 2866,
          })
          .toBuffer();
      }

      // DEBUG cropped frame
      // await sharp(fileBuffer).toFile(
      //   join(process.cwd(), 'public', 'physical', 'test.png'),
      // );

      return {
        characterName,
        fileBuffer,
        rawFileBuffer,
        templateRotation: parseInt(scanResult.data.split('-')[2]),
      };
    } catch (error) {
      this.logger.error('Error while processing raw image:', error);
      throw new InternalServerErrorException();
    }
  }

  private async storeRawFile({
    id,
    characterName,
    rawFileBuffer,
    fileBuffer,
  }: {
    id: string;
    characterName: string;
    rawFileBuffer: Buffer;
    fileBuffer: Buffer;
  }) {
    const storagePath = join(
      process.cwd(),
      'public',
      'physical',
      'customized',
      characterName,
      dateWithTz().format('YYYY-MM-DD'),
      id,
    );

    if (!existsSync(storagePath)) {
      mkdirSync(storagePath, { recursive: true });
    }

    const characterAreaFilePath = join(storagePath, 'cropped.png');
    await sharp(fileBuffer).toFile(characterAreaFilePath);

    const rawFilePath = join(storagePath, 'raw.png');
    await sharp(rawFileBuffer).toFile(rawFilePath);

    return { id, characterName, characterAreaFilePath };
  }

  private async loadCharacterBaseFile(characterName: string) {
    const physicalPath = join(process.cwd(), 'public', 'physical');
    const animationPath = join(physicalPath, 'animation', characterName);
    const extractionPath = join(physicalPath, 'extraction', characterName);

    if (!existsSync(animationPath) || !existsSync(extractionPath)) {
      throw new NotFoundException("Can't find the character base assets.");
    }

    const jsonPath = join(extractionPath, `${characterName}.json`);
    const characterJson: CharacterJSON = JSON.parse(
      readFileSync(jsonPath, 'utf-8'),
    );

    const atlasPath = join(extractionPath, `${characterName}.atlas`);
    const characterAtlas = readFileSync(atlasPath, 'utf8')
      .split('\n')
      .map((line) => line.replace('\r', ''));

    const imagePath = join(extractionPath, `${characterName}.png`);
    const characterBaseImage = await loadImage(imagePath);

    return {
      json: characterJson,
      jsonPath,
      animationJson: join(animationPath, `${characterName}.json`),
      atlas: characterAtlas,
      atlasPath,
      animationAtlas: join(animationPath, `${characterName}.atlas`),
      image: characterBaseImage,
      masksPath: join(extractionPath, 'masks'),
    };
  }

  private async generateAssetFromRawFile({
    id,
    characterName,
    characterAreaFilePath,
    templateRotation,
  }: {
    id: string;
    characterName: string;
    characterAreaFilePath: string;
    templateRotation: number;
  }) {
    return new Promise(async (resolve) => {
      const baseAssets = await this.loadCharacterBaseFile(characterName);

      const characterPaperSize: CharacterPaperSize = {
        width: 1524, //Magic number for 300DPI paper, the width of the character inside the paper
        height: Math.round(
          (1524 / baseAssets.json.skeleton.width) *
            baseAssets.json.skeleton.height,
        ),
      };

      const ratio = characterPaperSize.width / baseAssets.json.skeleton.width;

      const extractableAttachment = Object.keys(
        baseAssets.json.skins[0].attachments,
      ).filter((attachment) => attachment.includes('white'));

      const pngSize = baseAssets.atlas
        .find((item) => item.includes('size'))
        .replace('size:', '')
        .split(',');
      const [pngWidth, pngHeight] = pngSize.map((n) => parseInt(n));

      const attachmentDetails = extractableAttachment.map((attachmentName) => {
        const characterAttachmentIndexInAtlas =
          baseAssets.atlas.indexOf(attachmentName);

        const rawBounds = baseAssets.atlas[characterAttachmentIndexInAtlas + 1];

        if (!rawBounds.includes('bounds')) {
          throw new NotFoundException(
            `Can't find ${attachmentName} bounds info for ${characterName}`,
          );
        }

        const [x, y, width, height] = rawBounds
          .replace('bounds:', '')
          .split(',')
          .map((n) => parseInt(n));

        const rawRotate = baseAssets.atlas[characterAttachmentIndexInAtlas + 2];
        const rotate = rawRotate.includes('rotate')
          ? parseInt(rawRotate.split('rotate:')[1])
          : 0;

        return {
          name: attachmentName,
          x,
          y,
          width,
          height,
          rotate,
        };
      });

      const canvas = createCanvas(pngWidth, pngHeight);
      const ctx = canvas.getContext('2d');
      ctx.drawImage(baseAssets.image, 0, 0, pngWidth, pngHeight);
      ctx.imageSmoothingEnabled = false;

      const rawImage = await loadImage(characterAreaFilePath);

      for await (const {
        name,
        x,
        y,
        width,
        height,
        rotate,
      } of attachmentDetails) {
        const attachment = baseAssets.json.skins[0].attachments[name][name];

        const shiftedToCanvasVertices = this.shiftVerticesToCanvas({
          attachment,
          ratio,
          skeleton: baseAssets.json.skeleton,
        });

        const croppedPartBuffer = this.cropImage({
          rawImage,
          vertices: shiftedToCanvasVertices,
          characterPaperSize,
        });

        const allXVertices = shiftedToCanvasVertices.map((v) => v[0]);
        const allYVertices = shiftedToCanvasVertices.map((v) => v[1]);

        const minX = Math.ceil(Math.min(...allXVertices));
        const minY = Math.ceil(Math.min(...allYVertices));

        const maxX = Math.ceil(Math.max(...allXVertices));
        const maxY = Math.ceil(Math.max(...allYVertices));

        // DEBUG Cropped part
        // console.log(name, {
        //   left: minX,
        //   top: minY,
        //   width: maxX - minX,
        //   height: maxY - minY,
        // });

        const croppedPartWidth = maxX - minX;
        const croppedPartHeight = maxY - minY;
        await sharp(croppedPartBuffer)
          .extract({
            left: minX,
            top: minY,
            width: croppedPartWidth,
            height: croppedPartHeight,
          })
          .rotate(-rotate - templateRotation)
          .toFile(
            join(process.cwd(), 'public', 'physical', 'test', `${name}.png`),
          );

        let croppedPart = await sharp(croppedPartBuffer)
          .extract({
            left: minX,
            top: minY,
            width: croppedPartWidth,
            height: croppedPartHeight,
          })
          .rotate(-rotate - templateRotation) // if paper layout is landscape need to - 90
          .toBuffer();

        if (
          characterName === 'cow' &&
          ['head_white', 'body_white'].includes(name)
        ) {
          console.log('Inpainting', name);

          const maskBuffer = readFileSync(
            join(baseAssets.masksPath, `${name}.png`),
          );

          // resize mask to match the cropped part
          const resizedMaskBuffer = await sharp(maskBuffer)
            .resize(croppedPartWidth, croppedPartHeight)
            .toBuffer();

          croppedPart = await this.inpaintService.inpaintBuffer(
            croppedPart,
            resizedMaskBuffer,
            10,
          );
        }

        ctx.drawImage(
          await loadImage(croppedPart),
          x,
          y,
          rotate === 90 ? height : width,
          rotate === 90 ? width : height,
        );
      }

      const outputFolder = join(
        process.cwd(),
        'public',
        'physical',
        'customized',
        characterName,
        dateWithTz().format('YYYY-MM-DD'),
        id,
      );

      const outputJsonPath = join(outputFolder, `${characterName}.json`);
      const outputAtlasPath = join(outputFolder, `${characterName}.atlas`);
      const outputImagePath = join(outputFolder, `${characterName}.png`);

      copyFileSync(baseAssets.animationJson, outputJsonPath);
      copyFileSync(baseAssets.animationAtlas, outputAtlasPath);
      sharp(canvas.toBuffer('image/png')).toFile(
        outputImagePath,
        (error, info) => {
          if (error) {
            this.logger.error(error);
          }

          resolve({ id, characterName });
        },
      );
    });
  }

  private shiftVerticesToCanvas({
    attachment,
    ratio,
    skeleton,
  }: {
    attachment: Attachment;
    ratio: number;
    skeleton: Skeleton;
  }) {
    const shiftedVertices = [];

    for (let index = 0; index < attachment.vertices.length; index += 2) {
      const x = attachment.vertices[index];
      const y = attachment.vertices[index + 1];

      const shiftedX = (x + skeleton.width / 2) * ratio;
      const shiftedY = (-y + skeleton.height + skeleton.y) * ratio;

      shiftedVertices.push([Math.abs(shiftedX), Math.abs(shiftedY)]);
    }

    return shiftedVertices;
  }

  private cropImage({
    rawImage,
    vertices,
    characterPaperSize: { width, height },
  }: {
    rawImage: Image;
    vertices: number[][];
    characterPaperSize: CharacterPaperSize;
  }) {
    // Create a new canvas for the cropped image
    // buffer 10px for extraction
    const canvas = createCanvas(width + 10, height + 10);
    const ctx = canvas.getContext('2d');

    ctx.save();
    ctx.beginPath();

    ctx.moveTo(vertices[0][0], vertices[0][1]);
    for (const vertex of vertices) {
      ctx.lineTo(vertex[0], vertex[1]);
    }

    ctx.closePath();
    ctx.clip();

    ctx.drawImage(
      rawImage,
      (width - rawImage.width) / 2,
      (height - rawImage.height) / 2,
    );

    ctx.restore();

    return canvas.toBuffer('image/png');
  }
}
