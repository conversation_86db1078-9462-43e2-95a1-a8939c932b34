import {
  Controller,
  HttpStatus,
  ParseFilePipeBuilder,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { ApiKeyGuard } from 'src/auth/guards/apikey.guard';
import { AppId } from 'src/libs/decorators/app_id.decorator';
import { ColoringService } from './coloring.service';

@ApiTags('Coloring')
@Controller('coloring')
export class ColoringController {
  constructor(private readonly coloringService: ColoringService) {}

  @Post('/physical/upload')
  @ApiSecurity('x-api-key')
  @UseGuards(ApiKeyGuard)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async receiveFileFromScanner(
    @AppId() appId: string,
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({ fileType: 'png' })
        .addMaxSizeValidator({ maxSize: 20000000 })
        .build({ errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY }),
    )
    file: Express.Multer.File,
  ) {
    return this.coloringService.createNewPhysicalCustomizeCharacterAnon(
      appId,
      file,
    );
  }
}
