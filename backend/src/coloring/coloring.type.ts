export interface Skeleton {
  hash: string;
  spine: string;
  x: number;
  y: number;
  width: number;
  height: number;
  images: string;
  audio: string;
}

export interface Bone {
  name: string;
}

export interface Slot {
  name: string;
  bone: string;
  attachment: string;
}

export interface Attachment {
  type: string;
  uvs: number[];
  triangles: number[];
  vertices: number[];
  hull: number;
  edges: number[];
  width: number;
  height: number;
}

export interface Attachments {
  [part: string]: {
    [part: string]: Attachment;
  };
}

export interface Skin {
  name: string;
  attachments: Attachments;
}
export type CharacterJSON = {
  skeleton: Skeleton;
  bones: Bone[];
  slots: Slot[];
  skins: Skin[];
};

export interface CharacterPaperSize {
  width: number;
  height: number;
}
