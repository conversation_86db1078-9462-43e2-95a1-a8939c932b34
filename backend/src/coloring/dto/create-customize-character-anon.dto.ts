import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsString } from 'class-validator';

export class CreateCustomizeCharacterAnonDto {
  @ApiProperty({
    description: 'The character name',
    example: 'wintrix',
  })
  @IsString()
  characterName: string;

  @ApiProperty({
    description: 'The character customize attachments object',
    example: {
      r_leg: 'r_leg_white',
      r_arm2: 'r_arm2_white',
      r_arm1: 'r_arm1_white',
      body: 'body_white',
      l_leg: 'l_leg_white',
      l_arm2: 'l_arm2_white',
      l_arm1: 'l_arm1_white',
      head: 'head_white',
    },
  })
  @IsObject()
  customizeAttachments: Record<string, string>;
}
