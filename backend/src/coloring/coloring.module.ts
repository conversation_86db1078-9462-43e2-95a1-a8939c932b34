import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { ApplicationModule } from 'src/application/application.module';
import { AuthModule } from 'src/auth/auth.module';
import { ProjectionModule } from 'src/projection/projection.module';
import { ColoringController } from './coloring.controller';
import { ColoringService } from './coloring.service';
import { InpaintService } from 'src/inpaint/inpaint.service';
import { InpaintModule } from 'src/inpaint/inpaint.module';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'projection-queue',
    }),
    AuthModule,
    ApplicationModule,
    ProjectionModule,
    InpaintModule,
  ],
  controllers: [ColoringController],
  providers: [ColoringService],
})
export class ColoringModule {}
