import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { SanitizeHtmlPipe } from './libs/pipes/SanitizeHTMLPipe';

async function bootstrap() {
  const logger = new Logger('Main (main.ts)');
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  app.getHttpAdapter().getInstance().disable('x-powered-by');

  const configService = app.get(ConfigService);
  const port = parseInt(configService.get('PORT'));

  app.use(
    helmet({
      xXssProtection: true,
      crossOriginResourcePolicy: false,
    }),
  );

  app.enableCors({
    origin: `*`,
  });

  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  app.useGlobalPipes(new SanitizeHtmlPipe());

  const config = new DocumentBuilder()
    .setTitle('Color and Scan API')
    .setDescription('The Color and Scan API description')
    .setVersion('1.0')
    .addApiKey({ type: 'apiKey', name: 'x-api-key', in: 'header' }, 'x-api-key')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(port);
  logger.log(`Server running on port ${port}`);
}
bootstrap();
