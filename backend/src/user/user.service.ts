import { Inject, Injectable } from '@nestjs/common';
import { Role } from '@prisma/client';
import { CustomPrismaService } from 'nestjs-prisma';
import { PageNumberPaginationOptions } from 'prisma-extension-pagination';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UserService {
  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
  ) {}

  async findAll(
    paginateParams: PageNumberPaginationOptions,
    filter: {
      roles?: Role[];
    },
  ) {
    return await this.prisma.client.user
      .paginate({
        where: { role: { in: filter.roles } },
        select: {
          id: true,
          username: true,
          role: true,
          deactivatedAt: true,
          createdAt: true,
          updatedAt: true,
        },
      })
      .withPages({
        ...paginateParams,
        includePageCount: true,
      });
  }

  async findOne(id: string) {
    return await this.prisma.client.user.findUniqueOrThrow({ where: { id } });
  }

  async findOneByUsername(username: string) {
    const user = await this.prisma.client.user.findFirst({
      where: { username },
    });

    return user;
  }

  async update(id: string, dto: UpdateUserDto) {
    return await this.prisma.client.user.update({
      where: { id },
      data: {
        ...dto,
        updatedAt: new Date(),
      },
    });
  }

  async remove(id: string) {
    return await this.prisma.client.user.delete({ where: { id } });
  }
}
