#!/usr/bin/env python3
"""
Buffer-based RGBA inpainting script for NestJS integration.
Reads a JSON object from stdin with base64-encoded 'input' and 'mask', plus an optional inpaint radius arg.
Writes the resulting PNG as base64 to stdout.
Usage (NestJS will handle piping):
    python inpaint_buffer.py <inpaint_radius>
"""
import sys
import json
import cv2
import numpy as np
import base64


def read_stdin_json():
    data = sys.stdin.read()
    try:
        return json.loads(data)
    except json.JSONDecodeError as e:
        print(f"Error: invalid JSON input: {e}", file=sys.stderr)
        sys.exit(1)


def decode_image(b64_str):
    # Decode base64 to bytes, then to numpy array, then imdecode
    img_data = base64.b64decode(b64_str)
    arr = np.frombuffer(img_data, dtype=np.uint8)
    img = cv2.imdecode(arr, cv2.IMREAD_UNCHANGED)
    if img is None:
        print("Error: failed to decode input image", file=sys.stderr)
        sys.exit(2)
    return img


def encode_image_to_base64(img):
    # Encode image (with 4 channels) to PNG in memory, then base64
    success, buf = cv2.imencode('.png', img)
    if not success:
        print("Error: failed to encode output image", file=sys.stderr)
        sys.exit(3)
    return base64.b64encode(buf.tobytes()).decode('utf-8')


def main():
    # Parse radius from arg
    if len(sys.argv) > 2:
        print(__doc__.strip())
        sys.exit(1)
    radius = int(sys.argv[1]) if len(sys.argv) == 2 else 5

    # Read base64 JSON payload
    payload = read_stdin_json()
    if 'input' not in payload or 'mask' not in payload:
        print("Error: JSON must include 'input' and 'mask' fields", file=sys.stderr)
        sys.exit(4)

    # Decode images
    src = decode_image(payload['input'])
    mask = decode_image(payload['mask'])  # grayscale or 4-channel, we'll convert

    # Validate shapes
    if src.ndim != 3 or src.shape[2] != 4:
        print("Error: 'input' image must be RGBA (4 channels)", file=sys.stderr)
        sys.exit(5)
    # Convert mask to single channel
    if mask.ndim == 3:
        mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)
    if mask.shape != src.shape[:2]:
        print("Error: 'mask' dimensions must match input image", file=sys.stderr)
        sys.exit(6)

    # Split BGR and alpha
    bgr = src[:, :, :3]
    alpha = src[:, :, 3]

    # Inpaint BGR
    inpainted_bgr = cv2.inpaint(bgr, mask, radius, cv2.INPAINT_TELEA)

    # Merge back with alpha
    b, g, r = cv2.split(inpainted_bgr)
    out = cv2.merge((b, g, r, alpha))

    # Encode and output
    b64_out = encode_image_to_base64(out)
    sys.stdout.write(b64_out)


if __name__ == '__main__':
    main()
