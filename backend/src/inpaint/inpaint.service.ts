import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { spawn } from 'child_process';

@Injectable()
export class InpaintService {
  private readonly logger = new Logger(InpaintService.name);
  private readonly pythonExe: string;

  constructor(private readonly configService: ConfigService) {
    this.pythonExe = this.configService.get<string>('PYTHON_PATH', 'python3');
  }

  /**
   * Performs RGBA-aware inpainting via a Python subprocess using buffers.
   */
  async inpaintBuffer(
    inputBuffer: Buffer,
    maskBuffer: Buffer,
    inpaintRadius = 5,
  ): Promise<Buffer> {
    const scriptPath = this.configService.get<string>(
      'PYTHON_SCRIPT',
      'src/inpaint/scripts/inpaint.py',
    );
    const pyProc = spawn(
      this.pythonExe,
      [scriptPath, inpaintRadius.toString()],
      {
        stdio: ['pipe', 'pipe', 'pipe'],
      },
    );

    // Handle possible pipe errors to prevent unhandled exceptions
    pyProc.stdin.on('error', (err) => {
      if ('code' in err && err.code !== 'EPIPE') {
        this.logger.error('Error on stdin pipe', err);
      }
    });
    pyProc.stdout.on('error', (err) =>
      this.logger.error('Error on stdout pipe', err),
    );
    pyProc.stderr.on('error', (err) =>
      this.logger.error('Error on stderr pipe', err),
    );

    let stdout = '';
    let stderr = '';
    pyProc.stdout.on('data', (chunk) => {
      stdout += chunk.toString();
    });
    pyProc.stderr.on('data', (chunk) => {
      stderr += chunk.toString();
    });

    const payload = JSON.stringify({
      input: inputBuffer.toString('base64'),
      mask: maskBuffer.toString('base64'),
    });

    // Write and close stdin
    try {
      pyProc.stdin.write(payload);
      pyProc.stdin.end();
    } catch (err: any) {
      if (err.code === 'EPIPE') {
        this.logger.warn(
          'Python process closed stdin before we could write fully',
        );
      } else {
        this.logger.error('Failed to write to stdin', err);
        throw err;
      }
    }

    return new Promise<Buffer>((resolve, reject) => {
      pyProc.on('close', (code) => {
        if (code !== 0) {
          this.logger.error(`Python inpaint error [${code}]: ${stderr.trim()}`);
          return reject(new Error(`Inpainting failed: ${stderr.trim()}`));
        }
        try {
          const result = Buffer.from(stdout.trim(), 'base64');
          resolve(result);
        } catch (err) {
          this.logger.error(`Error decoding output: ${err}`);
          reject(err);
        }
      });
    });
  }
}
