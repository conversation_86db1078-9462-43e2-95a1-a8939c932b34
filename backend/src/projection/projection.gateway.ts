import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { CustomPrismaService } from 'nestjs-prisma';
import { Namespace, Server, Socket } from 'socket.io';
import { ApplicationService } from 'src/application/application.service';
import { AuthService } from 'src/auth/auth.service';
import { dateWithTz } from 'src/libs/datetime';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';

// TODO: update cors
@WebSocketGateway({
  cors: { origin: '*' },
  namespace: 'projection',
})
export class ProjectionGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private readonly logger = new Logger(ProjectionGateway.name);

  @WebSocketServer()
  server: Server;

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly authService: AuthService,
    private readonly applicationService: ApplicationService,
    @InjectQueue('projection-queue') private projectionQueue: Queue,
  ) {}

  @WebSocketServer() io: Namespace;

  afterInit(): void {
    this.logger.log(`Websocket Gateway initialized.`);
  }

  async handleConnection(client: Socket) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);

    if (!appId) return client.disconnect();

    this.logger.debug(`Socket connected with app id: ${appId}`);
    this.logger.debug(`Number of connected sockets: ${this.io.sockets.size}`);

    this.io.to(appId).emit('projection:go-away');

    await client.join(appId);

    this.io
      .to(appId)
      .emit('projection:connected', await this.getCurrentCharacters(appId));
  }

  async handleDisconnect(client: Socket) {
    this.logger.log(`Disconnected socket id: ${client.id}`);
  }

  @SubscribeMessage('projection:characters-assets-loaded')
  async handleCharactersAssetsLoaded(@ConnectedSocket() client: Socket) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);
    if (!appId) return client.disconnect();

    this.io
      .to(appId)
      .emit(
        'projection:load-characters',
        await this.getCurrentCharacters(appId),
      );
  }

  @SubscribeMessage('projection:character-timeout')
  async handleCharacterTimeout(@ConnectedSocket() client: Socket) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);
    if (!appId) return client.disconnect();

    await this.projectionQueue.resume();
  }

  @SubscribeMessage('projection:character-exited')
  async handleCharacterExited(
    @MessageBody() { exitId }: { exitId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const apiKey: string = client.handshake.auth.apiKey.toString();
    const appId = await this.authService.exchangeApiKeyForAppId(apiKey);
    if (!appId) return client.disconnect();

    await this.prisma.client.projection.update({
      where: { id: exitId, appId },
      data: { exitedAt: dateWithTz().toDate(), exiting: false },
    });
  }

  private async getCurrentCharacters(appId: string) {
    const setting = await this.applicationService.findOneSetting(appId);

    const { minCharacters, maxCharacters, ttlCharacter } = setting;

    const currentCharacters = await this.prisma.client.projection.findMany({
      where: { appId, enteredAt: { not: null }, exitedAt: null },
      select: {
        id: true,
        userCharacter: { select: { character: true, type: true } },
        customizeAttachments: true,
        createdAt: true,
      },
      orderBy: { enteredAt: 'asc' },
    });

    if (
      !currentCharacters.some(({ createdAt }) =>
        dateWithTz().isSame(createdAt, 'date'),
      ) &&
      currentCharacters.length > minCharacters
    ) {
      const shouldExit = currentCharacters.splice(minCharacters);

      await this.prisma.client.projection.updateMany({
        where: { id: { in: shouldExit.map(({ id }) => id) } },
        data: { appId: null, exitedAt: dateWithTz().toDate() },
      });
    }

    if (currentCharacters.length > maxCharacters) {
      const shouldExit = currentCharacters.splice(
        0,
        currentCharacters.length - maxCharacters,
      );

      await this.prisma.client.projection.updateMany({
        where: { id: { in: shouldExit.map(({ id }) => id) } },
        data: { appId: null, exitedAt: dateWithTz().toDate() },
      });
    }

    const latestCharacters = await this.prisma.client.projection.findMany({
      where: { appId, enteredAt: { not: null }, exitedAt: null },
      select: {
        id: true,
        userCharacter: { select: { id: true, character: true, type: true } },
        customizeAttachments: true,
        createdAt: true,
      },
      orderBy: { enteredAt: 'asc' },
    });

    return latestCharacters.map(({ userCharacter, ...character }) => ({
      ...character,
      character: userCharacter.character,
      userCharacterId: userCharacter.id,
      type: userCharacter.type,
      ttl: ttlCharacter,
    }));
  }
}
