import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { ProjectionGateway } from './projection.gateway';

export type ProjectionSettingJobData = {
  appId: string;
  characterId?: string;
  size?: number;
  speed?: number;
  backgroundPath?: string;
};

@Processor('projection-setting-queue')
export class ProjectionSettingProcessor {
  constructor(private readonly projectionGateway: ProjectionGateway) {}

  @Process('exit-character')
  async handleExitCharacter(job: Job<ProjectionSettingJobData>) {
    const { appId, characterId } = job.data;

    this.projectionGateway.server.to(appId).emit('projection:character-exit', {
      exitId: characterId,
    });
  }

  @Process('resize-character')
  async handleResize<PERSON>haracter(job: Job<ProjectionSettingJobData>) {
    const { appId, characterId, size } = job.data;

    this.projectionGateway.server
      .to(appId)
      .emit('projection:character-resize', { characterId, size });
  }

  @Process('speed-character')
  async handleSpeed<PERSON>haracter(job: Job<ProjectionSettingJobData>) {
    const { appId, characterId, speed } = job.data;

    this.projectionGateway.server
      .to(appId)
      .emit('projection:character-speed', { characterId, speed });
  }

  @Process('change-background')
  async handleChangeBackground(job: Job<ProjectionSettingJobData>) {
    const { appId, backgroundPath } = job.data;

    this.projectionGateway.server
      .to(appId)
      .emit('projection:background', backgroundPath);
  }

  @Process('refresh-app')
  async handleRefreshApp(job: Job<ProjectionSettingJobData>) {
    const { appId } = job.data;

    this.projectionGateway.server.to(appId).emit('projection:refresh-app');
  }
}
