import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ApplicationModule } from 'src/application/application.module';
import { AuthModule } from 'src/auth/auth.module';
import { ProjectionGateway } from './projection.gateway';
import { ProjectionProcessor } from './projection.processor';
import { ProjectionSettingProcessor } from './projection-setting.processor';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'projection-queue',
    }),
    BullModule.registerQueue({
      name: 'projection-setting-queue',
    }),
    AuthModule,
    ApplicationModule,
  ],
  providers: [
    ProjectionGateway,
    ProjectionProcessor,
    ProjectionSettingProcessor,
  ],
  exports: [ProjectionGateway, ProjectionProcessor, ProjectionSettingProcessor],
})
export class ProjectionModule {}
