import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({ passReqToCallback: true });
  }

  async validate(req, username: string, password: string): Promise<any> {
    const user = await this.authService.validateLocalUser(username, password);

    if (!user) {
      throw new BadRequestException('Bad request!');
    }

    return {
      id: user.id,
      pdvId: user.username,
      role: user.role,
    };
  }
}
