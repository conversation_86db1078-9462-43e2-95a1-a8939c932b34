import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { UserFromJwt, ValidatedUserAfterLocalAuth } from './auth.type';
import { User } from './decorators/params.decorator';
import { Roles } from './decorators/role.decorator';
import { GenerateApiKeyDto } from './dto/generate-api-key.dto';
import { LoginDto } from './dto/login.dto';
import { RevokeApiKeyDto } from './dto/revoke-api-key.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { RolesGuard } from './guards/role.guard';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @Post('generate-api-key')
  async generateApiKey(@Body() dto: GenerateApiKeyDto) {
    return this.authService.generateApiKey(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @Post('revoke-api-key')
  async revokeApiKey(@Body() dto: RevokeApiKeyDto) {
    return this.authService.revokeApiKey(dto);
  }

  @Post('sign-in')
  @UseGuards(LocalAuthGuard)
  @ApiBody({ type: LoginDto })
  async signin(@User() user: ValidatedUserAfterLocalAuth) {
    return this.authService.signin(user);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @Post('sign-out')
  async signout(@User() user: UserFromJwt) {
    return this.authService.signout(user.sub);
  }
}
