import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { AuthService } from '../auth.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private readonly authService: AuthService) {} // made up service for the point of the exmaple

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();

    const key = req.headers['x-api-key'] ?? req.query.api_key; // checks the header, moves to query if null

    const appId = await this.authService.exchangeApiKeyForAppId(key);

    if (appId) {
      req.headers['x-app-id'] = appId;
      return true;
    } else {
      return false;
    }
  }
}
