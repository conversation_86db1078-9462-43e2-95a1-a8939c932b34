import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Role, User } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { randomUUID } from 'crypto';
import { CustomPrismaService } from 'nestjs-prisma';
import { ExtendedPrismaClient } from 'src/libs/prisma/prisma.extension';
import { UserService } from 'src/user/user.service';
import {
  ExternalAuthorizeResponse,
  ExternalJwt,
  JwtPayload,
  ValidatedUserAfterLocalAuth,
} from './auth.type';
import { GenerateApiKeyDto } from './dto/generate-api-key.dto';
import {
  NLBCASAuthorizeDto,
  NLBCASAuthorizeDtoFake,
} from './dto/nlb-cas-authorize.dto';
import { RegisterDto } from './dto/register.dto';
import { RevokeApiKeyDto } from './dto/revoke-api-key.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @Inject('PrismaService')
    private prisma: CustomPrismaService<ExtendedPrismaClient>,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async exchangeApiKeyForAppId(key: string | null | undefined) {
    try {
      const { appId } = await this.prisma.client.apiKey.findUniqueOrThrow({
        where: {
          key,
        },
        select: {
          appId: true,
        },
      });

      return appId;
    } catch (error) {
      this.logger.error(error.message);
      return null;
    }
  }

  async generateApiKey({ appId }: GenerateApiKeyDto) {
    return await this.prisma.client.apiKey.create({
      data: {
        key: bcrypt.hashSync(randomUUID(), 12).slice(9),
        app: {
          connectOrCreate: {
            where: { id: appId },
            create: {
              id: appId,
              setting: { create: {} },
            },
          },
        },
      },
      select: { key: true },
    });
  }

  async revokeApiKey({ appId }: RevokeApiKeyDto) {
    await this.prisma.client.apiKey.delete({
      where: { appId },
    });

    return `ApiKey for ${appId} revoked!`;
  }

  async signin(user: ValidatedUserAfterLocalAuth, name?: string) {
    const tokens = this.generateToken(user, name);

    const decodedAT = this.jwtService.decode(tokens.accessToken);

    await this.userService.update(user.id, {
      lastTokenIat: decodedAT.iat,
    });

    return tokens;
  }

  async validateLocalUser(username: string, password: string): Promise<User> {
    const user = await this.userService.findOneByUsername(username);

    if (!user) {
      throw new BadRequestException('Invalid credentials!');
    }

    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      throw new BadRequestException(`Invalid credentials`);
    }

    user.password = undefined;

    return user;
  }

  async validateJwtToken(jwtUser: JwtPayload) {
    const user = await this.userService.findOne(jwtUser.sub);

    if (user?.lastTokenIat !== jwtUser.iat) {
      throw new UnauthorizedException('Invalid Token!');
    }

    if (user.deactivatedAt) {
      throw new UnauthorizedException('User deactivated!');
    }
  }

  async signout(userId: string) {
    await this.userService.update(userId, {
      lastTokenIat: null,
    });
  }

  parseExternalJwt(token: string): ExternalJwt {
    return JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
  }

  validateExternalJwt(jwt: ExternalJwt) {
    if (
      !jwt.iss.startsWith(process.env.NLB_OIDC_URL) ||
      jwt.aud !== process.env.NLB_OIDC_CLIENT_ID ||
      jwt.client_id !== process.env.NLB_OIDC_CLIENT_ID
    ) {
      throw new UnauthorizedException('Invalid token!');
    }

    if (
      jwt.exp <
      Date.now() / 1000
      // jwt.auth_time < Date.now() / 1000 - 180
    ) {
      throw new UnauthorizedException('Token expired!');
    }
  }

  private generateToken(user: ValidatedUserAfterLocalAuth, name?: string) {
    const payload = {
      sub: user.id,
      pdvId: user.username,
      role: user.role,
      name,
    };

    return {
      accessToken: this.jwtService.sign(payload),
    };
  }
}
