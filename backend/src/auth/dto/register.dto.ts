import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches } from 'class-validator';

export class RegisterDto {
  @ApiProperty()
  @IsString()
  username: string;

  @ApiProperty()
  @Matches(/^(?=.*[A-Z])(?=.*[!@#$&*])(?=.*[0-9])(?=.*[a-z]).{8,}$/, {
    message:
      'Password too weak! Password must contain at least 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character!',
  })
  @IsString()
  password: string;
}
