{"name": "project-2024-woodleighmall-colour-and-scan-backend", "version": "0.0.1", "description": "", "author": "TRINAX", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prisma:push": "npx prisma db push", "prisma:studio": "npx prisma studio", "prisma:seed": "npx prisma db seed"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.7.0", "archiver": "^7.0.1", "archiver-zip-encrypted": "^2.0.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bull": "^4.16.5", "cache-manager": "^6.4.3", "canvas": "^3.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.13", "denque": "^2.1.0", "exceljs": "^4.4.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsqr": "^1.4.0", "nestjs-prisma": "^0.25.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma-extension-pagination": "^0.7.5", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sanitize-html": "^2.16.0", "sharp": "^0.34.1"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/multer": "^1.4.12", "@types/node": "^22.15.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "prettier": "^3.5.3", "prisma": "^6.7.0", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}