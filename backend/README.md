# Woodleigh Mall Colour and Scan Backend

A NestJS backend application that handles character coloring, scanning, and projection management.

## Features

- Authentication with API Key and Local Strategy
- Character customization and projection management
- Redis-based queue system for projection handling
- Application settings management
- Role-based access control
- Health monitoring
- Static file serving

## Tech Stack

- NestJS
- Prisma ORM
- Redis/Bull Queue
- WebSocket (ProjectionGateway)
- Cache Module
- Schedule Module

## API Endpoints

### Application Management
- `POST /application` - Create new application (Admin only)
- `GET /application` - List all applications (Admin only)
- `GET /application/:id` - Get specific application
- `PATCH /application/:id` - Update application
- `GET /application/:id/settings` - Get application settings
- `PATCH /application/:id/settings` - Update application settings
- `GET /application/:id/projection/status` - Get projection status
- `PUT /application/:id/characters/flush` - Flush characters
- `PUT /application/:id/queue/flush` - Flush queue

### Authentication
- Local authentication using username/password
- API Key authentication via `x-api-key` header or `api_key` query parameter

## Configuration

### Application Settings
- `minCharacters` - Minimum characters on projection wall
- `maxCharacters` - Maximum characters on projection wall
- `ttlCharacter` - Time to live for characters
- `size` - Character size (default: 1)
- `speed` - Character movement speed (default: 1)

### Character Properties
- Name
- Spawn behavior
- Movement pattern
- Size
- Speed
- Face direction

## Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables

3. Run Redis server:
```bash
redis-server
```

4. Push Prisma schema to database:
```bash
npx prisma db push
```

5. Start the application (dev mode):
```bash
npm run start:dev
```

### Development
The project uses Prisma as ORM with custom extensions. WebSocket gateway is implemented for real-time projection updates. Bull queues handle asynchronous projection tasks.

### Security
- Role-based access control using @Roles() decorator
- API Key authentication for external services
- Local authentication strategy for user login