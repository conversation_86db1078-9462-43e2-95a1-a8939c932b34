import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

const seedCMSAdmin = async () => {
  await prisma.user.upsert({
    where: {
      username: 'cmsadmin',
    },
    update: {},
    create: {
      username: 'cmsadmin',
      password: bcrypt.hashSync(process.env.ADMIN_PASS, 12),
      role: Role.ADMIN,
    },
  });

  console.log(`CMS Admin Created`);
};

const seedCharacter = async () => {
  await prisma.character.createMany({
    data: [
      {
        name: 'bear',
        spawn: 'random',
        movement: 'random',
        size: 0.3,
        speed: 250,
        faceDirection: 'left',
      },
      {
        name: 'santa',
        spawn: 'random',
        movement: 'random',
        size: 0.3,
        speed: 250,
        faceDirection: 'left',
      },
      {
        name: 'penguin',
        spawn: 'random',
        movement: 'random',
        size: 0.3,
        speed: 250,
        faceDirection: 'left',
      },
      {
        name: 'reindeer',
        spawn: 'random',
        movement: 'random',
        size: 0.3,
        speed: 250,
        faceDirection: 'left',
      }
    ],
    skipDuplicates: true,
  });
};

async function main() {
  await seedCMSAdmin();
  await seedCharacter();
}
main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
