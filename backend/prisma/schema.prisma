// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model Application {
  id          String       @id
  name        String?
  apiKey      ApiKey?
  setting     Setting?
  projections Projection[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("applications")
}

model Setting {
  id            String      @id @default(cuid())
  appId         String      @unique
  app           Application @relation(fields: [appId], references: [id])
  minCharacters Int         @default(5)
  maxCharacters Int         @default(20)
  ttlCharacter  Int         @default(15)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

model ApiKey {
  id    String      @id @default(cuid())
  key   String      @unique
  appId String      @unique
  app   Application @relation(fields: [appId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("api_keys")
}

model Character {
  id             String          @id @default(cuid())
  name           String          @unique
  spawn          String          @default("random")
  movement       String          @default("random")
  size           Float           @default(1)
  speed          Float           @default(1)
  faceDirection  String          @default("left")
  userCharacters UserCharacter[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("characters")
}

enum ColorType {
  REGULAR
  PREMIUM
}

enum Role {
  ADMIN
  ANONYMOUS
}

model User {
  id           String  @id @default(cuid())
  username     String  @unique
  password     String?
  role         Role    @default(ANONYMOUS)
  lastTokenIat Int?

  userCharacters UserCharacter[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deactivatedAt DateTime?

  @@map("users")
}

model Projection {
  id String @id @default(cuid())

  app                  Application?   @relation(fields: [appId], references: [id])
  appId                String?
  customizeAttachments Json?
  waiting              Boolean        @default(false)
  enteredAt            DateTime?
  exitedAt             DateTime?
  exiting              Boolean        @default(false)
  userCharacter        UserCharacter? @relation(fields: [userCharacterId], references: [id])
  userCharacterId      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userCharacterId])
  @@index([appId])
  @@map("projections")
}

enum UserCharacterType {
  KIOSK
  WEBAPP
  PHYSICAL
}

model UserCharacter {
  id          String @id @default(cuid())
  userId      String
  characterId String

  attachments Json?
  user        User              @relation(fields: [userId], references: [id])
  character   Character         @relation(fields: [characterId], references: [id])
  type        UserCharacterType
  projections Projection[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([characterId])
  @@map("user_characters")
}
