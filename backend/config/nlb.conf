Listen 443 https

Header always set Server "NLB"

SSLCipherSuite HIGH:!MEDIUM:!aNULL:!MD5:!RC4
SSLProtocol +TLSv1.1 +TLSv1.2

<VirtualHost *:443>
    ServerName **************
    DocumentRoot /home/<USER>/project-2023-nlb-gamification-backend/dist

    <Directory /home/<USER>/project-2023-nlb-gamification-backend/dist >
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    # SSL configuration
    SSLEngine on
    SSLCertificateFile /etc/apache2/ssl/certificate.crt
    SSLCertificateKeyFile /etc/apache2/ssl/private.key
    SSLCertificateChainFile /etc/apache2/ssl/ca_bundle.crt

    # Proxy settings
    ProxyPass / http://127.0.0.1:51732/
    ProxyPassReverse / http://127.0.0.1:51732/
</VirtualHost>
