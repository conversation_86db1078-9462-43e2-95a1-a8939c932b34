{"skeleton": {"hash": "JpZdqn++9W4", "spine": "4.2.37", "x": -285.9, "y": -1.85, "width": 569.86, "height": 724.88, "images": "../assets/", "audio": "./audio"}, "bones": [{"name": "root", "x": 1.42}, {"name": "body", "parent": "root", "x": -5.02, "y": 282.14}, {"name": "body2", "parent": "body", "length": 132.36, "rotation": 91.85, "x": -1.42, "y": 9.96, "color": "faff11ff"}, {"name": "head", "parent": "body2", "x": 127.27, "y": -9.76}, {"name": "head2", "parent": "head", "length": 144.46, "rotation": 3.19, "x": 7.38, "y": -2.36, "color": "00fff4ff"}, {"name": "head3", "parent": "head2", "length": 151.77, "rotation": 3.04, "x": 147.19, "y": -0.69, "color": "00fff4ff"}, {"name": "ear_r", "parent": "head3", "x": 85.25, "y": -107.29}, {"name": "ear_r2", "parent": "ear_r", "length": 20.42, "rotation": -57.47, "x": 2.39, "y": -4.19, "color": "dfff00ff"}, {"name": "ear_r3", "parent": "ear_r2", "length": 22.45, "rotation": 5.73, "x": 24.02, "y": 0.8, "color": "dfff00ff"}, {"name": "ear_l", "parent": "head3", "x": 105.31, "y": 86.7}, {"name": "ear_l2", "parent": "ear_l", "length": 23.49, "rotation": 38.2, "x": 4.78, "y": 2.18, "color": "dfff00ff"}, {"name": "ear_l3", "parent": "ear_l2", "length": 33.67, "rotation": 5.85, "x": 23.49, "color": "dfff00ff"}, {"name": "hand_r", "parent": "body2", "x": 67.16, "y": -132.88}, {"name": "hand_r2", "parent": "hand_r", "length": 73.79, "rotation": -58.34, "x": 4.14, "y": -6.2, "color": "43ff3cff"}, {"name": "hand_r3", "parent": "hand_r2", "length": 51.53, "rotation": 8.77, "x": 75.47, "y": 0.97, "color": "43ff3cff"}, {"name": "hand_r4", "parent": "hand_r3", "length": 55.85, "rotation": 5.87, "x": 52.12, "y": 0.64, "color": "43ff3cff"}, {"name": "hand_l", "parent": "body2", "x": 77.75, "y": 114.76}, {"name": "hand_l2", "parent": "hand_l", "length": 69.87, "rotation": 58.41, "color": "43ff3cff"}, {"name": "hand_l3", "parent": "hand_l2", "length": 62.06, "rotation": -6.16, "x": 69.87, "color": "43ff3cff"}, {"name": "hand_l4", "parent": "hand_l3", "length": 55.8, "rotation": -10.98, "x": 64.6, "y": -3.51, "color": "43ff3cff"}, {"name": "leg_r", "parent": "body2", "x": -107.36, "y": -72.43}, {"name": "leg_l", "parent": "body2", "x": -107.3, "y": 70.73}, {"name": "leg_l2", "parent": "leg_l", "length": 110.8, "rotation": 162.4, "x": -0.76, "y": 4.58, "color": "ff5454ff"}, {"name": "leg_l3", "parent": "leg_l2", "length": 73.48, "rotation": -30.25, "x": 116.56, "y": -0.27, "color": "ff5454ff"}, {"name": "eyes", "parent": "head3", "x": 26.31, "y": 60.29}, {"name": "eyes2", "parent": "eyes", "length": 14.55, "rotation": 0.46, "color": "cfb3ffff"}, {"name": "eyes3", "parent": "eyes2", "length": 7.73, "rotation": 53.71, "x": 14.86, "y": -0.41, "color": "cfb3ffff"}, {"name": "eyes4", "parent": "eyes3", "length": 16.37, "rotation": 61.1, "x": 8.82, "y": 1.79, "color": "cfb3ffff"}, {"name": "eyes5", "parent": "eyes", "x": -6.21, "y": 4.88}, {"name": "eyes6", "parent": "eyes5", "length": 12.73, "rotation": 124.64, "color": "cfb3ffff"}, {"name": "eyes7", "parent": "eyes6", "length": 11.08, "rotation": -78.46, "x": 12.73, "color": "cfb3ffff"}, {"name": "eyes8", "parent": "eyes7", "length": 9.02, "rotation": -30.75, "x": 10.63, "y": -1.21, "color": "cfb3ffff"}, {"name": "eyes9", "parent": "head3", "x": 12.2, "y": -63.27}, {"name": "eyes10", "parent": "eyes9", "length": 12.88, "rotation": -23.75, "x": 0.59, "y": -0.52, "color": "cfb3ffff"}, {"name": "eyes11", "parent": "eyes10", "length": 10.09, "rotation": -66.89, "x": 13.15, "y": -0.15, "color": "cfb3ffff"}, {"name": "eyes12", "parent": "eyes11", "length": 10.84, "rotation": -45.91, "x": 10.47, "y": -0.49, "color": "cfb3ffff"}, {"name": "head4", "parent": "head3", "length": 0.49, "rotation": 145.36, "x": -3.18, "y": -66.37}, {"name": "head5", "parent": "head4", "length": 12.73, "rotation": 99.68, "x": 0.49, "color": "cfb3ffff"}, {"name": "head6", "parent": "head5", "length": 7.31, "rotation": 53.42, "x": 12.73, "color": "cfb3ffff"}, {"name": "head7", "parent": "head6", "length": 11.99, "rotation": 41.96, "x": 7.44, "y": 0.17, "color": "cfb3ffff"}, {"name": "cheek", "parent": "head3", "x": -11.69, "y": 87.41}, {"name": "cheek2", "parent": "cheek", "length": 25.43, "rotation": 43.33, "x": -0.19, "y": 0.03, "color": "ff0179ff"}, {"name": "cheek3", "parent": "head3", "x": -36.4, "y": -77.54}, {"name": "cheek4", "parent": "cheek3", "length": 28.46, "rotation": -57.97, "x": 1.57, "y": 0.16, "color": "ff0179ff"}, {"name": "body15", "parent": "body", "length": 155.5, "rotation": -89.14, "x": 0.49, "y": -0.75, "color": "faff11ff"}, {"name": "leg_r2", "parent": "leg_r", "length": 138.44, "rotation": -163.55, "x": 2.87, "y": -0.1, "color": "ff5454ff"}, {"name": "leg_r3", "parent": "leg_r2", "length": 66.83, "rotation": 26.71, "x": 135.35, "y": -2.41, "color": "ff5454ff"}, {"name": "nose6", "parent": "head2", "x": 40.88, "y": -12.42}, {"name": "nose7", "parent": "nose6", "length": 68.23, "rotation": 7, "x": 3.38, "y": -0.79, "color": "ffb031ff"}, {"name": "nose8", "parent": "nose7", "length": 93.16, "rotation": -6.6, "x": 69.77, "y": 0.17, "color": "ffb031ff"}], "slots": [{"name": "leg_l_white", "bone": "leg_l", "attachment": "leg_l_white"}, {"name": "leg_r_white", "bone": "leg_r", "attachment": "leg_r_white"}, {"name": "hand_l_white", "bone": "hand_l", "attachment": "hand_l_white"}, {"name": "hand_r_white", "bone": "hand_r", "attachment": "hand_r_white"}, {"name": "body_white", "bone": "body", "attachment": "body_white"}, {"name": "ear_l_white", "bone": "ear_l", "attachment": "ear_l_white"}, {"name": "ear_r_white", "bone": "ear_r", "attachment": "ear_r_white"}, {"name": "head_white", "bone": "head", "attachment": "head_white"}, {"name": "cheek_white", "bone": "cheek3", "attachment": "cheek_white"}, {"name": "eyes_white", "bone": "eyes9", "attachment": "eyes_white"}, {"name": "nose_white", "bone": "nose6", "attachment": "nose_white"}], "skins": [{"name": "default", "attachments": {"body_white": {"body_white": {"type": "mesh", "uvs": [0.88033, 0.11913, 0.93855, 0.23879, 0.98221, 0.3568, 0.99967, 0.45486, 1, 0.55292, 0.98885, 0.72686, 0.88995, 0.88911, 0.81653, 0.95046, 0.69561, 0.98726, 0.50508, 1, 0.38517, 1, 0.21602, 0.96644, 0.0865, 0.86862, 0.03441, 0.78601, 0, 0.64123, 0, 0.4171, 0.01823, 0.31802, 0.03814, 0.22057, 0.0865, 0.11028, 0.16188, 0.0013, 0.27281, 0.05977, 0.34795, 0.06709, 0.42308, 0.0744, 0.4731, 0.07927, 0.54729, 0.07989, 0.6113, 0.06871, 0.69948, 0.05328, 0.80412, 1e-05, 0.42499, 0.42846, 0.61404, 0.43158, 0.40792, 0.6526, 0.18418, 0.61774, 0.08367, 0.36088, 0.39797, 0.84263, 0.13911, 0.74118, 0.02534, 0.53404, 0.86304, 0.76954, 0.83022, 0.62123, 0.66011, 0.65044, 0.24803, 0.40652, 0.67293, 0.82859, 0.80195, 0.38885, 0.14653, 0.15622, 0.26737, 0.19599, 0.4195, 0.21935, 0.57578, 0.22962, 0.76014, 0.20498, 0.05358, 0.44958, 0.22929, 0.49549, 0.41646, 0.54053, 0.63893, 0.5442, 0.81329, 0.4923, 0.11696, 0.27234, 0.25956, 0.31293, 0.42131, 0.34726, 0.59863, 0.34015, 0.78941, 0.30116, 0.34343, 0.20767, 0.34044, 0.3301, 0.33651, 0.41749, 0.32287, 0.51801, 0.29605, 0.63517, 0.26854, 0.7919, 0.48835, 0.22236, 0.50254, 0.34371, 0.51673, 0.42684, 0.51747, 0.53918, 0.51822, 0.65152, 0.51779, 0.84304, 0.66796, 0.2173, 0.69402, 0.32066, 0.70799, 0.41022, 0.72611, 0.51825, 0.74516, 0.63583, 0.76799, 0.79906], "triangles": [42, 18, 19, 42, 19, 20, 43, 42, 20, 43, 20, 21, 46, 26, 27, 46, 27, 0, 57, 43, 21, 57, 21, 22, 69, 25, 26, 69, 26, 46, 44, 57, 22, 44, 22, 23, 17, 18, 42, 63, 23, 24, 44, 23, 63, 45, 24, 25, 45, 25, 69, 63, 24, 45, 46, 0, 1, 52, 17, 42, 56, 46, 1, 43, 52, 42, 53, 43, 57, 53, 52, 43, 16, 17, 52, 70, 69, 46, 70, 46, 56, 58, 53, 57, 58, 57, 44, 55, 45, 69, 55, 69, 70, 64, 63, 45, 64, 45, 55, 54, 44, 63, 54, 63, 64, 58, 44, 54, 32, 16, 52, 41, 56, 1, 41, 1, 2, 70, 56, 41, 39, 52, 53, 39, 53, 58, 32, 52, 39, 71, 70, 41, 55, 70, 71, 15, 16, 32, 59, 39, 58, 59, 58, 54, 65, 64, 55, 28, 54, 64, 28, 64, 65, 59, 54, 28, 29, 55, 71, 65, 55, 29, 47, 15, 32, 71, 41, 51, 48, 32, 39, 47, 32, 48, 60, 39, 59, 48, 39, 60, 29, 71, 72, 35, 15, 47, 66, 65, 29, 28, 65, 66, 28, 60, 59, 49, 60, 28, 31, 47, 48, 35, 47, 31, 61, 48, 60, 14, 15, 35, 3, 51, 41, 3, 41, 2, 72, 71, 51, 49, 28, 66, 50, 29, 72, 66, 29, 50, 51, 3, 4, 37, 51, 4, 72, 51, 37, 31, 48, 61, 73, 72, 37, 38, 50, 72, 14, 35, 31, 73, 38, 72, 67, 66, 50, 67, 50, 38, 49, 66, 67, 49, 61, 60, 30, 49, 67, 30, 61, 49, 5, 37, 4, 34, 14, 31, 36, 37, 5, 13, 14, 34, 62, 31, 61, 34, 31, 62, 36, 74, 73, 36, 73, 37, 74, 40, 38, 74, 38, 73, 30, 62, 61, 33, 62, 30, 68, 30, 67, 33, 30, 68, 67, 38, 40, 68, 67, 40, 12, 13, 34, 6, 36, 5, 74, 36, 6, 7, 74, 6, 62, 12, 34, 11, 12, 62, 7, 8, 40, 7, 40, 74, 9, 33, 68, 8, 9, 68, 8, 68, 40, 33, 11, 62, 10, 33, 9, 10, 11, 33], "vertices": [2, 44, -128.37, 150.64, 0.17548, 2, 114.99, -154.4, 0.82452, 2, 44, -88.43, 172.05, 0.31924, 2, 74.69, -175.12, 0.68076, 2, 44, -49.13, 187.97, 0.47903, 2, 35.12, -190.35, 0.52097, 2, 44, -16.57, 194.08, 0.5997, 2, 2.46, -195.91, 0.4003, 2, 44, 15.88, 193.72, 0.71401, 2, -29.98, -194.98, 0.28599, 2, 44, 73.39, 188.64, 0.8704, 2, -87.39, -188.91, 0.1296, 2, 44, 126.52, 150.45, 0.96667, 2, -139.86, -149.82, 0.03333, 2, 44, 146.41, 122.4, 0.98959, 2, -159.26, -121.42, 0.01041, 2, 44, 157.91, 76.51, 0.99993, 2, -169.97, -75.35, 7e-05, 1, 44, 161.04, 4.44, 1, 2, 44, 160.36, -40.88, 0.99129, 2, -170.4, 42.07, 0.00871, 2, 44, 148.3, -104.65, 0.91769, 2, -157.23, 105.62, 0.08231, 2, 44, 115.19, -153.12, 0.79514, 2, -123.29, 153.51, 0.20486, 2, 44, 87.55, -172.39, 0.71204, 2, -95.33, 172.31, 0.28796, 2, 44, 39.44, -184.68, 0.54909, 2, -47.01, 183.76, 0.45091, 2, 44, -34.74, -183.57, 0.25666, 2, 27.14, 181.37, 0.74334, 2, 44, -67.43, -176.18, 0.14981, 2, 59.69, 173.43, 0.85019, 2, 44, -99.57, -168.18, 0.07318, 2, 91.69, 164.86, 0.92682, 2, 44, -135.79, -149.35, 0.02005, 2, 127.59, 145.42, 0.97995, 2, 44, -171.43, -120.32, 0.00026, 2, 162.72, 115.78, 0.99974, 1, 2, 142.03, 74.49, 1, 1, 2, 138.69, 46.18, 1, 2, 44, -145.76, -21.96, 0, 2, 135.36, 17.87, 1, 2, 44, -143.87, -3.08, 2e-05, 2, 133.14, -0.97, 0.99998, 2, 44, -143.24, 24.96, 0.00784, 2, 132.03, -28.99, 0.99216, 2, 44, -146.58, 49.21, 0.02956, 2, 134.95, -53.3, 0.97044, 2, 44, -151.18, 82.61, 0.07312, 2, 138.98, -86.78, 0.92688, 2, 44, -168.25, 122.43, 0.10392, 2, 155.35, -126.88, 0.89608, 2, 44, -28.57, -23, 0.03564, 2, 18.2, 20.93, 0.96436, 2, 44, -26.46, 48.44, 0.369, 2, 14.86, -50.46, 0.631, 2, 44, 45.52, -30.56, 0.92227, 2, -55.74, 29.77, 0.07773, 2, 44, 32.71, -114.95, 0.58199, 2, -41.48, 113.93, 0.41801, 2, 44, -52.87, -151.67, 0.17683, 2, 44.72, 149.16, 0.82317, 2, 44, 108.35, -35.27, 0.98364, 2, -118.49, 35.56, 0.01636, 2, 44, 73.31, -132.6, 0.72643, 2, -81.77, 132.28, 0.27357, 2, 44, 4.11, -174.57, 0.40887, 2, -11.86, 173.05, 0.59113, 2, 44, 86.8, 140.88, 0.92982, 2, -99.98, -140.93, 0.07018, 2, 44, 37.53, 129.21, 0.81666, 2, -50.51, -130.11, 0.18334, 2, 44, 46.23, 64.77, 0.93406, 2, -58.1, -65.53, 0.06594, 2, 44, -36.83, -89.77, 0.17679, 2, 27.61, 87.55, 0.82321, 2, 44, 105.26, 68.73, 0.99522, 2, -117.19, -68.47, 0.00478, 2, 44, -39.54, 119.68, 0.44129, 2, 26.71, -121.91, 0.55871, 2, 44, -120.25, -126.89, 0.02213, 2, 111.66, 123.23, 0.97787, 2, 44, -106.4, -81.41, 0.01103, 2, 97.03, 78, 0.98897, 1, 2, 87.45, 20.77, 1, 2, 44, -93.52, 34.99, 0.0327, 2, 82.14, -38.16, 0.9673, 2, 44, -100.63, 104.79, 0.16615, 2, 88.05, -108.07, 0.83385, 2, 44, -23.69, -163.48, 0.29062, 2, 15.74, 161.48, 0.70938, 2, 44, -7.49, -97.3, 0.35312, 2, -1.59, 95.58, 0.64688, 2, 44, 8.47, -26.78, 0.65873, 2, -18.77, 25.35, 0.34127, 2, 44, 10.95, 57.29, 0.77357, 2, -22.7, -58.66, 0.22643, 2, 44, -5.24, 123.45, 0.62736, 2, -7.65, -125.09, 0.37264, 2, 44, -81.99, -138.64, 0.08673, 2, 73.6, 135.64, 0.91327, 2, 44, -67.74, -84.95, 0.06341, 2, 58.44, 82.2, 0.93659, 2, 44, -55.47, -23.98, 0.00223, 2, 45.11, 21.45, 0.99777, 2, 44, -56.81, 43.07, 0.13224, 2, 45.3, -45.61, 0.86776, 2, 44, -68.63, 115.37, 0.29599, 2, 55.87, -118.11, 0.70401, 2, 44, -102.11, -52.72, 0.00222, 2, 92.24, 49.39, 0.99778, 2, 44, -61.6, -54.46, 0.03099, 2, 51.77, 51.83, 0.96901, 2, 44, -32.7, -56.38, 0.13195, 2, 22.91, 54.24, 0.86805, 2, 44, 0.49, -62.04, 0.42503, 2, -10.18, 60.47, 0.57497, 2, 44, 39.11, -72.75, 0.71479, 2, -48.61, 71.85, 0.28521, 2, 44, 90.83, -83.93, 0.87728, 2, -100.13, 83.92, 0.12272, 2, 44, -96.42, 1.97, 0.00077, 2, 85.61, -5.2, 0.99923, 2, 44, -56.18, 6.73, 0.00826, 2, 45.29, -9.27, 0.99174, 2, 44, -28.59, 11.68, 0.07431, 2, 17.62, -13.74, 0.92569, 2, 44, 8.6, 11.41, 0.97014, 2, -19.56, -12.83, 0.02986, 1, 44, 45.78, 11.13, 1, 1, 44, 109.17, 10.02, 1, 2, 44, -97.08, 69.89, 0.09993, 2, 85.1, -73.12, 0.90007, 2, 44, -62.72, 79.22, 0.23485, 2, 50.59, -81.86, 0.76515, 2, 44, -33, 84.06, 0.41984, 2, 20.79, -86.18, 0.58016, 2, 44, 2.86, 90.37, 0.67757, 2, -15.17, -91.88, 0.32243, 2, 44, 41.88, 96.99, 0.86724, 2, -54.31, -97.82, 0.13276, 2, 44, 96.03, 104.8, 0.97027, 2, -108.59, -104.7, 0.02973], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 52, 54, 60, 66, 62, 68, 70, 28, 68, 24, 66, 20, 62, 70, 28, 68, 48, 50, 50, 52, 40, 42, 42, 44, 44, 46, 46, 48, 16, 18, 18, 20, 72, 10, 74, 8, 34, 36, 38, 84, 36, 84, 40, 86, 44, 88, 48, 90, 52, 92, 92, 0, 38, 40, 64, 78, 76, 80, 74, 72, 84, 86, 32, 64, 80, 16, 72, 12, 14, 16, 12, 14, 10, 12, 64, 94, 94, 70, 30, 94, 62, 96, 96, 78, 94, 96, 56, 98, 98, 60, 58, 100, 100, 76, 74, 102, 102, 82, 102, 6, 82, 4, 64, 104, 104, 84, 34, 104, 78, 106, 106, 86, 104, 106, 56, 108, 108, 88, 58, 110, 110, 90, 82, 112, 112, 92, 112, 2, 36, 38, 24, 26, 22, 24, 86, 114, 114, 88, 42, 114, 106, 116, 116, 108, 114, 116, 56, 118, 118, 78, 116, 118, 96, 120, 120, 98, 118, 120, 60, 122, 122, 62, 120, 122, 66, 124, 124, 68, 122, 124, 124, 22, 88, 126, 126, 90, 46, 126, 108, 128, 128, 110, 126, 128, 56, 130, 130, 58, 128, 130, 98, 132, 132, 100, 130, 132, 60, 134, 134, 76, 132, 134, 66, 136, 136, 80, 134, 136, 136, 18, 90, 138, 138, 92, 50, 138, 110, 140, 140, 112, 138, 140, 58, 142, 142, 82, 140, 142, 100, 144, 144, 102, 142, 144, 74, 146, 146, 76, 144, 146, 72, 148, 148, 80, 146, 148, 148, 14, 54, 0], "width": 378, "height": 331}}, "cheek_white": {"cheek_white": {"type": "mesh", "uvs": [0.131, 0.39074, 0.86579, 0.40314, 0.90299, 0, 0.9867, 0, 1, 0.53956, 0.9774, 0.8496, 0.90609, 1, 0.87199, 0.862, 0.85959, 0.60156, 0.1372, 0.60156, 0.131, 0.94881, 0.10154, 1, 0.05814, 0.96121, 0.01008, 0.68837, 0, 0.37834, 0.00698, 0.1055, 0.05504, 0, 0.10929, 0.14271, 0.02506, 0.22844, 0.04571, 0.36885, 0.07752, 0.58517, 0.10426, 0.76699, 0.8836, 0.77475, 0.91194, 0.56175, 0.94932, 0.28088], "triangles": [24, 2, 3, 23, 1, 2, 24, 3, 4, 24, 23, 2, 1, 8, 0, 8, 1, 23, 22, 8, 23, 5, 24, 4, 23, 24, 5, 7, 8, 22, 6, 22, 23, 6, 23, 5, 7, 22, 6, 18, 15, 16, 19, 18, 16, 19, 16, 17, 14, 15, 18, 20, 19, 17, 20, 17, 0, 8, 9, 0, 21, 20, 0, 19, 13, 14, 19, 14, 18, 9, 21, 0, 10, 21, 9, 20, 13, 19, 12, 20, 21, 12, 13, 20, 11, 12, 21, 11, 21, 10], "vertices": [2, 41, 4.31, -11.38, 0.991, 43, -117.24, 113.32, 0.009, 2, 41, -119.96, -110.12, 0.00863, 43, 3.94, 10.83, 0.99137, 2, 41, -119.45, -123.64, 4e-05, 43, 17.1, 13.98, 0.99996, 1, 43, 30.93, 2.34, 1, 1, 43, 23.75, -10.66, 1, 1, 43, 14.62, -13.92, 1, 1, 43, 0.22, -7.1, 1, 2, 41, -128.73, -101.27, 0.00016, 43, -3.01, 0.49, 0.99984, 2, 41, -122.25, -105.1, 0.01072, 43, -0.53, 7.6, 0.98928, 2, 41, -0.29, -7.77, 0.98961, 43, -119.88, 108.11, 0.01039, 1, 41, -5.09, 0.4, 1, 1, 41, -0.98, 5.44, 1, 1, 41, 7, 10.47, 1, 1, 41, 19.71, 11.19, 1, 1, 41, 26.63, 6.01, 1, 1, 41, 30.05, -0.69, 1, 1, 41, 23.71, -9.39, 1, 2, 41, 12.15, -13.69, 0.99895, 43, -116.51, 121.47, 0.00105, 1, 41, 24.92, -0.53, 1, 1, 41, 19.07, -0.35, 1, 2, 41, 10.06, -0.07, 0.99999, 43, -129.46, 116.75, 1e-05, 1, 41, 2.48, 0.16, 1, 2, 41, -129.22, -104.68, 0.00019, 43, 0.42, 0.68, 0.99981, 2, 41, -130.42, -112.99, 0.0001, 43, 8.81, 1.14, 0.9999, 1, 43, 19.87, 1.74, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 30, 32, 36, 36, 28, 38, 36, 34, 38, 38, 26, 40, 38, 0, 40, 40, 24, 20, 42, 42, 40, 18, 42, 42, 22, 44, 14, 16, 44, 44, 12, 46, 44, 2, 46, 46, 10, 6, 48, 48, 46, 4, 48, 48, 8], "width": 216, "height": 27}}, "ear_l_white": {"ear_l_white": {"type": "mesh", "uvs": [0.31945, 0.00732, 0.58344, 0, 0.81273, 0.07421, 1, 0.24868, 0.64227, 0.61363, 0.3979, 1, 0.22894, 0.94223, 0.03284, 0.69505, 0, 0.49877, 0.01624, 0.22687, 0.11963, 0.29074, 0.30005, 0.40221, 0.47116, 0.50792], "triangles": [10, 9, 0, 11, 10, 0, 11, 0, 1, 8, 9, 10, 8, 10, 11, 12, 11, 1, 7, 8, 11, 12, 7, 11, 12, 1, 2, 4, 12, 2, 3, 4, 2, 12, 6, 7, 5, 6, 12, 4, 5, 12], "vertices": [2, 10, 52.65, -19.04, 0.05399, 11, 27.06, -21.92, 0.94601, 2, 10, 37.81, -34.08, 0.39224, 11, 10.76, -35.36, 0.60776, 2, 10, 20.29, -42.31, 0.74695, 11, -7.5, -41.76, 0.25305, 2, 10, -0.54, -42.2, 0.91619, 11, -28.21, -39.53, 0.08381, 1, 10, -0.8, -0.53, 1, 2, 10, -8.84, 36.16, 0.85689, 11, -28.48, 39.27, 0.14311, 2, 10, 4.24, 42.04, 0.78274, 11, -14.86, 43.78, 0.21726, 2, 10, 29.76, 38.05, 0.40051, 11, 10.11, 37.22, 0.59949, 2, 10, 42.92, 28.1, 0.14141, 11, 22.19, 25.97, 0.85859, 2, 10, 57.58, 10.89, 9e-05, 11, 35.02, 7.36, 0.99991, 2, 10, 47.94, 9.01, 0.00303, 11, 25.24, 6.47, 0.99697, 2, 10, 31.12, 5.71, 0.04604, 11, 8.16, 4.91, 0.95396, 2, 10, 15.16, 2.59, 0.97454, 11, -8.03, 3.43, 0.02546], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0, 20, 18, 0, 20, 20, 16, 22, 20, 2, 22, 22, 14, 8, 24, 24, 22, 4, 24, 24, 12], "width": 80, "height": 83}}, "ear_r_white": {"ear_r_white": {"type": "mesh", "uvs": [0.3877, 0.64085, 0.73212, 1, 0.8374, 0.92488, 0.94398, 0.74736, 1, 0.55892, 1, 0.35545, 0.88029, 0.12468, 0.68533, 0, 0.49817, 0, 0.32661, 0.02772, 0.12255, 0.17384, 0, 0.33497, 0.82758, 0.17991, 0.73972, 0.27197, 0.6239, 0.39334, 0.48659, 0.53723], "triangles": [12, 7, 6, 13, 7, 12, 8, 7, 13, 12, 6, 5, 14, 8, 13, 13, 12, 5, 4, 13, 5, 9, 8, 14, 15, 9, 14, 10, 9, 15, 0, 11, 10, 14, 13, 4, 15, 0, 10, 3, 14, 4, 15, 14, 3, 2, 15, 3, 1, 0, 15, 2, 1, 15], "vertices": [1, 7, -8.46, -2.57, 1, 2, 7, -5.22, -42.72, 0.99885, 8, -33.44, -40.38, 0.00115, 2, 7, 5.28, -43.9, 0.97639, 8, -23.11, -42.6, 0.02361, 2, 7, 21.12, -39.01, 0.83793, 8, -6.86, -39.32, 0.16207, 2, 7, 34.34, -30.73, 0.562, 8, 7.12, -32.4, 0.438, 2, 7, 44.8, -18.53, 0.21977, 8, 18.75, -21.31, 0.78023, 2, 7, 49.12, 1.78, 0.0009, 8, 25.07, -1.53, 0.9991, 2, 7, 43.25, 19.79, 0.03433, 8, 21.03, 16.98, 0.96567, 2, 7, 31.45, 29.9, 0.26129, 8, 10.3, 28.21, 0.73871, 2, 7, 19.21, 37.51, 0.53327, 8, -1.12, 37, 0.46673, 2, 7, -1.16, 39.76, 0.82594, 8, -21.16, 41.28, 0.17406, 2, 7, -17.17, 36.72, 0.90653, 8, -37.39, 39.85, 0.09347, 2, 7, 42.96, 1.32, 0.00194, 8, 18.9, -1.38, 0.99806, 2, 7, 32.69, 0.54, 0.00476, 8, 8.6, -1.12, 0.99524, 1, 7, 19.15, -0.48, 1, 1, 7, 3.1, -1.7, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 24, 12, 14, 24, 24, 10, 26, 24, 16, 26, 26, 8, 28, 26, 18, 28, 28, 6, 0, 30, 30, 28, 20, 30, 30, 4], "width": 83, "height": 79}}, "eyes_white": {"eyes_white": {"type": "mesh", "uvs": [0.15492, 0.12394, 0.16574, 0.45564, 0.833, 0.52998, 0.83781, 0.14682, 0.87989, 0, 0.96164, 0.01528, 0.98082, 0.15826, 1, 0.30123, 1, 0.50997, 1, 0.71871, 0.97006, 1, 0.91355, 1, 0.85704, 0.84452, 0.84142, 0.65008, 0.16935, 0.5643, 0.1417, 0.80449, 0.10082, 1, 0.04431, 1, 0, 0.76446, 0, 0.46136, 0.02027, 0.1411, 0.05453, 0.01622, 0.09841, 0, 0.05233, 0.85175, 0.06181, 0.67656, 0.08011, 0.33828, 0.08926, 0.16914, 0.95548, 0.8383, 0.94303, 0.70027, 0.92897, 0.54432, 0.91522, 0.3918, 0.89755, 0.1959], "triangles": [15, 24, 14, 15, 16, 24, 23, 18, 24, 16, 23, 24, 17, 18, 23, 17, 23, 16, 24, 19, 25, 18, 19, 24, 26, 21, 22, 26, 20, 21, 25, 20, 26, 19, 20, 25, 25, 26, 0, 26, 22, 0, 14, 1, 2, 1, 25, 0, 25, 1, 14, 24, 25, 14, 8, 29, 7, 28, 29, 8, 28, 8, 9, 27, 28, 9, 10, 27, 9, 11, 27, 10, 12, 13, 29, 12, 29, 28, 11, 12, 28, 11, 28, 27, 31, 4, 5, 6, 30, 31, 6, 31, 5, 30, 6, 7, 29, 30, 7, 3, 4, 31, 2, 3, 31, 2, 31, 30, 13, 2, 30, 13, 30, 29, 14, 2, 13], "vertices": [1, 26, -2.57, -2.28, 1, 3, 33, -33.49, 116.59, 0.00492, 25, 2.75, -3.89, 0.99446, 26, -9.97, 7.69, 0.00062, 4, 33, -4.4, 2.78, 0.89196, 37, -11.68, 9.41, 0.09635, 39, 6.01, 28.27, 0.00014, 25, -17.39, -119.63, 0.01156, 1, 33, 9.48, 5.8, 1, 1, 34, 1.13, 3.39, 1, 1, 35, 2.34, 4.5, 1, 1, 35, 8.27, 2.46, 1, 2, 35, 14.2, 0.42, 0.84349, 39, 20.17, 1.16, 0.15651, 2, 35, 19.01, -5.63, 0.01559, 39, 12.6, -0.39, 0.98441, 1, 39, 5.03, -1.93, 1, 3, 37, 16.46, -0.22, 0.01103, 38, 2.04, -3.12, 0.98895, 39, -6.22, 1.16, 2e-05, 1, 37, 6.94, -3.11, 1, 4, 33, -14.46, -4.44, 0.11668, 37, -4.25, -0.49, 0.88283, 39, -4.55, 21.8, 0.00014, 25, -29.53, -122.08, 0.00034, 5, 33, -8.28, 0.16, 0.6992, 35, 0.39, -27.05, 0.00075, 37, -8.97, 5.59, 0.28757, 39, 1.95, 25.93, 0.0043, 25, -22, -120.43, 0.00818, 3, 33, -37.19, 114.9, 0.0071, 25, -1.32, -3.93, 0.96938, 28, 4.93, -8.82, 0.02352, 3, 25, -9.38, 2.2, 0.05928, 30, -6.74, -12.08, 0.05216, 28, -3.19, -2.75, 0.88856, 2, 30, -5.13, -2, 0.67601, 28, -9.34, 5.39, 0.32399, 1, 30, 2.95, 3.81, 1, 2, 31, 1.93, 4.06, 0.2389, 30, 14.37, 1.29, 0.7611, 4, 25, 6.87, 24.98, 0.00151, 27, 20.57, 3.44, 0.33945, 31, 12.22, -0.41, 0.65769, 28, 12.88, 20.16, 0.00135, 1, 27, 11.08, -4.49, 1, 1, 27, 3.5, -5.04, 1, 1, 26, 8.36, -1.71, 1, 5, 25, -8.78, 18.02, 0.0014, 27, 20.81, 20.57, 0.00249, 31, -4.7, -3.09, 0.11504, 30, 5, -1.47, 0.8485, 28, -2.71, 13.07, 0.03256, 5, 25, -2.62, 15.41, 0.05823, 27, 15.86, 16.07, 0.06716, 31, 0.58, -7.21, 0.56101, 30, 7.44, -7.7, 0.12111, 28, 3.47, 10.51, 0.1925, 4, 25, 9.28, 10.36, 0.34222, 27, 6.29, 7.39, 0.50498, 31, 10.77, -15.15, 0.10625, 28, 15.41, 5.56, 0.04655, 5, 25, 15.23, 7.84, 0.20552, 26, 6.88, 4.58, 0.11743, 27, 1.5, 3.05, 0.6648, 31, 15.87, -19.13, 0.00853, 28, 21.38, 3.09, 0.00373, 5, 33, -9.56, -21.05, 0.00997, 35, 20.44, -20.01, 0.00293, 37, 12.26, 4.76, 0.25197, 38, 3.54, 3.21, 0.5167, 39, -0.87, 4.87, 0.21843, 5, 33, -5.23, -17.56, 0.08408, 35, 15.54, -17.38, 0.04247, 37, 8.68, 9.01, 0.33064, 38, 4.82, 8.62, 0.09609, 39, 3.7, 8.03, 0.44672, 5, 33, -0.35, -13.62, 0.26195, 35, 10.01, -14.4, 0.17647, 37, 4.64, 13.81, 0.21428, 38, 6.27, 14.73, 0.00677, 39, 8.86, 11.61, 0.34053, 4, 33, 4.43, -9.77, 0.46724, 35, 4.61, -11.49, 0.30954, 37, 0.68, 18.51, 0.07691, 39, 13.91, 15.11, 0.14632, 5, 33, 10.57, -4.81, 0.64876, 34, 3.28, -4.2, 0.15343, 35, -2.34, -7.75, 0.17921, 37, -4.4, 24.54, 0.0043, 39, 20.39, 19.6, 0.01431], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 34, 46, 36, 46, 46, 32, 48, 46, 38, 48, 48, 30, 50, 48, 28, 50, 50, 40, 40, 42, 42, 44, 44, 52, 52, 50, 42, 52, 52, 0, 44, 0, 54, 20, 18, 54, 54, 22, 14, 16, 16, 18, 56, 54, 16, 56, 56, 24, 58, 56, 14, 58, 58, 26, 10, 12, 12, 14, 60, 58, 12, 60, 60, 4, 8, 62, 62, 60, 10, 62, 62, 6], "width": 176, "height": 37}}, "hand_l_white": {"hand_l_white": {"type": "mesh", "uvs": [0.0575, 0.0615, 0.12845, 0.01201, 0.23165, 0, 0.35743, 0.01731, 0.48321, 0.111, 0.5977, 0.24003, 0.71542, 0.35669, 0.82669, 0.44684, 1, 0.56881, 0.90086, 0.80213, 0.83475, 1, 0.63479, 0.91173, 0.45741, 0.82158, 0.28164, 0.70491, 0.13006, 0.5529, 0.02363, 0.39558, 0, 0.23296, 0.00751, 0.13398, 0.08206, 0.08307, 0.13277, 0.1276, 0.20786, 0.19355, 0.33711, 0.30455, 0.47008, 0.42063, 0.60763, 0.54284, 0.75425, 0.67248], "triangles": [21, 15, 20, 21, 14, 15, 20, 16, 19, 20, 15, 16, 21, 4, 5, 21, 3, 4, 21, 20, 3, 19, 17, 18, 19, 16, 17, 20, 2, 3, 20, 19, 2, 17, 0, 18, 18, 1, 19, 19, 1, 2, 18, 0, 1, 12, 13, 23, 13, 22, 23, 22, 14, 21, 22, 13, 14, 23, 6, 7, 23, 22, 6, 22, 5, 6, 22, 21, 5, 10, 11, 9, 11, 24, 9, 11, 12, 24, 12, 23, 24, 9, 24, 8, 24, 7, 8, 24, 23, 7], "vertices": [2, 18, 124.26, -12.75, 0.00033, 19, 60.33, 2.29, 0.99967, 1, 19, 57.67, -11.84, 1, 1, 19, 46.98, -26.01, 1, 2, 18, 86.76, -48.41, 0.00668, 19, 30.31, -39.86, 0.99332, 2, 18, 60.77, -49.19, 0.17747, 19, 4.94, -45.57, 0.82253, 3, 17, 98.02, -47.67, 0.03275, 18, 33.11, -44.37, 0.60908, 19, -23.14, -46.11, 0.35817, 3, 17, 71.52, -41.85, 0.34605, 18, 6.13, -41.44, 0.58837, 19, -50.18, -48.37, 0.06558, 3, 17, 48.02, -39.08, 0.8059, 18, -17.53, -41.2, 0.18904, 19, -73.45, -52.65, 0.00506, 2, 17, 12.85, -37.27, 0.99836, 18, -52.69, -43.18, 0.00164, 1, 17, 9.51, 2.75, 1, 1, 17, 4.01, 35.16, 1, 2, 17, 40.53, 40.16, 0.96381, 18, -33.49, 36.78, 0.03619, 2, 17, 73.84, 43, 0.48746, 18, -0.67, 43.18, 0.51254, 2, 17, 108.97, 42.11, 0.04397, 18, 34.35, 46.07, 0.95603, 2, 18, 69.25, 42.06, 0.8691, 19, -4.12, 45.62, 0.1309, 2, 18, 98.38, 32.86, 0.39285, 19, 26.24, 42.14, 0.60715, 2, 18, 116.54, 14.68, 0.08548, 19, 47.52, 27.75, 0.91452, 2, 18, 124.55, 1.42, 0.01578, 19, 57.92, 16.26, 0.98422, 2, 18, 118.89, -12.49, 0.00029, 19, 55, 1.53, 0.99971, 1, 19, 44, -0.06, 1, 1, 19, 27.72, -2.4, 1, 2, 18, 63.3, -10.08, 0.08208, 19, -0.03, -6.7, 0.91792, 3, 17, 102.99, -12.38, 0.00045, 18, 34.26, -8.75, 0.93391, 19, -28.79, -10.92, 0.06564, 3, 17, 73.11, -7.5, 0.24629, 18, 4.03, -7.11, 0.74971, 19, -58.78, -15.07, 0.004, 2, 17, 41.31, -2.38, 0.99834, 18, -28.14, -5.43, 0.00166], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 2, 36, 36, 34, 38, 36, 4, 38, 38, 32, 40, 38, 6, 40, 40, 30, 42, 40, 8, 42, 42, 28, 44, 42, 10, 44, 44, 26, 46, 44, 12, 46, 46, 24, 18, 48, 48, 46, 14, 48, 48, 22], "width": 171, "height": 156}}, "hand_r_white": {"hand_r_white": {"type": "mesh", "uvs": [0.95112, 0.05448, 0.99471, 0.13916, 0.99974, 0.30851, 0.91257, 0.4986, 0.75667, 0.6645, 0.57227, 0.80793, 0.37949, 0.92199, 0.20179, 1, 0.11294, 0.82348, 0, 0.60229, 0.15821, 0.48132, 0.3074, 0.34134, 0.43816, 0.19273, 0.56724, 0.05448, 0.71308, 0, 0.85892, 0, 0.91879, 0.08415, 0.84849, 0.14864, 0.72684, 0.25427, 0.58009, 0.39812, 0.41765, 0.53906, 0.2653, 0.68127], "triangles": [7, 8, 6, 8, 21, 6, 6, 21, 5, 8, 9, 21, 21, 20, 5, 5, 20, 4, 9, 10, 21, 21, 10, 20, 20, 19, 4, 4, 19, 3, 10, 11, 20, 20, 11, 19, 19, 18, 3, 3, 18, 2, 2, 18, 17, 11, 12, 19, 19, 12, 18, 2, 17, 1, 17, 16, 1, 12, 13, 18, 13, 14, 18, 18, 14, 17, 17, 15, 16, 17, 14, 15, 16, 0, 1, 16, 15, 0], "vertices": [1, 13, 187.37, 21.9, 1, 1, 13, 185.87, 6.44, 1, 1, 13, 171.42, -16.9, 1, 1, 13, 142.29, -34.54, 1, 1, 13, 105.74, -42.58, 1, 1, 13, 67.24, -44.96, 1, 1, 13, 30.19, -42.59, 1, 1, 13, -1.53, -36.75, 1, 1, 13, 1.88, -4.72, 1, 1, 13, 5.94, 35.57, 1, 1, 13, 38.78, 37.33, 1, 1, 13, 72.08, 42.49, 1, 1, 13, 103.57, 50.51, 1, 1, 13, 133.91, 57.29, 1, 1, 13, 159.09, 51.2, 1, 1, 13, 179.4, 37.76, 1, 1, 13, 180.21, 20.87, 1, 1, 13, 164.66, 18.64, 1, 1, 13, 138.27, 15.58, 1, 1, 13, 104.97, 9.68, 1, 1, 13, 69.75, 5.61, 1, 1, 13, 35.82, 0.45, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 30, 32, 32, 2, 34, 32, 28, 34, 34, 4, 36, 34, 26, 36, 36, 6, 38, 36, 24, 38, 38, 8, 40, 38, 22, 40, 40, 10, 16, 42, 42, 40, 20, 42, 42, 12], "width": 167, "height": 162}}, "head_white": {"head_white": {"type": "mesh", "uvs": [0.41666, 0, 0.55513, 0.01004, 0.6911, 0.05712, 0.79588, 0.13691, 0.8807, 0.23109, 0.94931, 0.34749, 0.99173, 0.47306, 1, 0.58816, 0.99921, 0.72027, 0.95056, 0.84322, 0.88197, 0.91569, 0.72903, 0.99092, 0.54538, 1, 0.35759, 0.99658, 0.22233, 0.97134, 0.10963, 0.90339, 0.03156, 0.80587, 0, 0.70089, 0, 0.54386, 0.0215, 0.43156, 0.05826, 0.32312, 0.1263, 0.17885, 0.22246, 0.06284, 0.33047, 0.00759, 0.41917, 0.0209, 0.42858, 0.09919, 0.4392, 0.18768, 0.45443, 0.31448, 0.46884, 0.43454, 0.48263, 0.54941, 0.45933, 0.54813, 0.49646, 0.65403, 0.50712, 0.75207, 0.52083, 0.85845, 0.5277, 0.91789, 0.53816, 0.96461], "triangles": [24, 0, 1, 23, 0, 24, 25, 24, 1, 25, 1, 2, 23, 24, 25, 22, 23, 25, 26, 25, 2, 26, 2, 3, 22, 25, 26, 21, 22, 26, 27, 26, 3, 27, 3, 4, 27, 20, 21, 27, 21, 26, 28, 27, 4, 20, 27, 28, 19, 20, 28, 28, 4, 5, 29, 28, 5, 30, 19, 28, 18, 19, 30, 29, 5, 6, 30, 28, 29, 29, 6, 7, 31, 29, 7, 17, 18, 30, 31, 17, 30, 31, 30, 29, 8, 31, 7, 32, 31, 8, 16, 17, 31, 16, 31, 32, 9, 32, 8, 33, 32, 9, 15, 16, 32, 15, 32, 33, 10, 33, 9, 34, 33, 10, 11, 35, 34, 14, 15, 33, 14, 33, 34, 10, 11, 34, 13, 14, 34, 13, 34, 35, 12, 35, 11, 13, 35, 12], "vertices": [2, 4, 298.16, 12.03, 0.00022, 5, 151.43, 4.7, 0.99978, 1, 5, 142.06, -39.29, 1, 2, 4, 272.78, -74.99, 0.00131, 5, 121.47, -80.86, 0.99869, 2, 4, 245.24, -106.65, 0.03201, 5, 92.29, -111.01, 0.96799, 2, 4, 213.84, -131.47, 0.11986, 5, 59.62, -134.14, 0.88014, 2, 4, 176.06, -150.46, 0.27048, 5, 20.88, -151.09, 0.72952, 2, 4, 136.2, -160.74, 0.44543, 5, -19.46, -159.25, 0.55457, 2, 4, 100.54, -160.29, 0.60021, 5, -55.05, -156.91, 0.39979, 2, 4, 59.9, -156.46, 0.74846, 5, -95.43, -150.92, 0.25154, 2, 4, 23.43, -137.42, 0.85143, 5, -130.83, -129.98, 0.14857, 2, 4, 3.08, -113.32, 0.90793, 5, -149.89, -104.83, 0.09207, 2, 4, -15.73, -61.92, 0.97875, 5, -165.94, -52.51, 0.02125, 2, 4, -13.31, -2.4, 0.99999, 5, -160.36, 6.8, 1e-05, 1, 4, -6.91, 58.12, 1, 2, 4, 4.7, 101.09, 0.99986, 5, -136.9, 109.19, 0.00014, 2, 4, 28.82, 135.62, 0.98874, 5, -110.98, 142.39, 0.01126, 2, 4, 61.06, 158.17, 0.95485, 5, -77.59, 163.2, 0.04515, 2, 4, 94.27, 165.51, 0.8999, 5, -44.04, 168.77, 0.1001, 2, 4, 142.6, 161.26, 0.75144, 5, 4, 161.96, 0.24856, 2, 4, 176.56, 151.27, 0.59582, 5, 37.38, 150.19, 0.40418, 2, 4, 208.89, 136.47, 0.4127, 5, 68.89, 133.69, 0.5873, 2, 4, 251.36, 110.6, 0.18098, 5, 109.92, 105.6, 0.81902, 2, 4, 284.34, 76.42, 0.05168, 5, 141.04, 69.72, 0.94832, 2, 4, 298.28, 40.06, 0.00834, 5, 153.03, 32.67, 0.99166, 2, 4, 291.66, 11.79, 0.00021, 5, 144.92, 4.8, 0.99979, 2, 4, 267.29, 10.88, 0.00069, 5, 120.54, 5.18, 0.99931, 2, 4, 239.75, 9.85, 0.00199, 5, 92.99, 5.61, 0.99801, 2, 4, 200.29, 8.37, 0.00849, 5, 53.5, 6.23, 0.99151, 2, 4, 162.92, 6.97, 0.07141, 5, 16.11, 6.82, 0.92859, 1, 4, 127.17, 5.64, 1, 1, 4, 128.23, 13.12, 1, 1, 4, 94.58, 4.01, 1, 1, 4, 64.1, 3.23, 1, 1, 4, 30.96, 1.69, 1, 1, 4, 12.47, 1.08, 1, 2, 4, -2.21, -1.03, 0.99999, 5, -149.21, 7.58, 1e-05], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 48, 2, 48, 48, 46, 50, 48, 4, 50, 50, 44, 52, 50, 6, 52, 52, 42, 54, 52, 8, 54, 54, 40, 56, 54, 38, 56, 56, 10, 58, 56, 12, 58, 60, 36, 62, 58, 34, 62, 62, 14, 64, 62, 16, 64, 64, 32, 66, 64, 18, 66, 66, 30, 68, 66, 28, 68, 68, 20, 24, 70, 70, 68, 26, 70, 70, 22, 22, 24], "width": 324, "height": 309}}, "leg_l_white": {"leg_l_white": {"type": "mesh", "uvs": [0.66242, 0.1134, 1, 0.16787, 1, 0.3872, 0.94476, 0.61536, 0.80746, 0.82145, 0.60441, 0.96276, 0.41496, 1, 0.28309, 1, 0.15123, 0.96995, 0.01928, 0.87284, 0, 0.74999, 0.0332, 0.61067, 0.1836, 0.5213, 0.25367, 0.43378, 0.28721, 0.2403, 0.31836, 1e-05, 0.5565, 0.30248, 0.44363, 0.48851, 0.36667, 0.62045, 0.29622, 0.74124, 0.23077, 0.85345, 0.191, 0.9117], "triangles": [19, 5, 6, 8, 21, 7, 21, 20, 7, 6, 7, 20, 8, 9, 21, 6, 20, 19, 5, 19, 4, 21, 9, 20, 9, 10, 20, 20, 10, 19, 19, 18, 4, 10, 11, 19, 11, 12, 19, 19, 12, 18, 18, 17, 4, 4, 17, 3, 18, 12, 17, 17, 12, 13, 17, 16, 3, 3, 16, 2, 17, 13, 16, 13, 14, 16, 2, 16, 1, 16, 0, 1, 16, 14, 0, 14, 15, 0], "vertices": [1, 22, 4.94, 0.48, 1, 1, 22, 1.45, 53.17, 1, 2, 22, 43.88, 65.13, 0.99436, 23, -95.73, 19.88, 0.00564, 2, 22, 90.32, 69.45, 0.82672, 23, -57.79, 47, 0.17328, 2, 22, 135.89, 60.47, 0.32535, 23, -13.91, 62.21, 0.67465, 2, 22, 171.66, 38.28, 0.02465, 23, 28.17, 61.07, 0.97535, 1, 23, 54.22, 46.32, 1, 1, 23, 68.74, 32.3, 1, 1, 23, 79.06, 13.94, 1, 2, 22, 178.56, -52.79, 0.00041, 23, 80.02, -14.12, 0.99959, 2, 22, 155.6, -62.33, 0.02861, 23, 64.99, -33.93, 0.97139, 2, 22, 127.27, -65.04, 0.15283, 23, 41.89, -50.55, 0.84717, 2, 22, 103.73, -47.77, 0.51456, 23, 12.85, -47.49, 0.48544, 2, 22, 83.89, -42.23, 0.87004, 23, -7.08, -52.7, 0.12996, 2, 22, 45.07, -47.84, 0.99997, 23, -37.78, -77.11, 3e-05, 1, 22, -2.71, -56.37, 1, 1, 22, 45.91, -4.8, 1, 2, 22, 86.59, -11.27, 0.97939, 23, -20.34, -24.6, 0.02061, 2, 22, 115.31, -15.4, 0.4094, 23, 6.55, -13.7, 0.5906, 2, 22, 141.6, -19.19, 0.00847, 23, 31.17, -3.72, 0.99153, 1, 23, 54.04, 5.55, 1, 1, 23, 66.55, 9.75, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 28, 32, 32, 4, 34, 32, 26, 34, 34, 6, 36, 34, 24, 36, 36, 8, 38, 36, 22, 38, 38, 10, 40, 38, 20, 40, 40, 12, 16, 18, 18, 20, 16, 42, 42, 40, 12, 14, 14, 16, 14, 42, 42, 18], "width": 153, "height": 201}}, "leg_r_white": {"leg_r_white": {"type": "mesh", "uvs": [0.31933, 0.12616, 0.68929, 0, 0.72044, 0.18457, 0.73796, 0.40654, 0.78275, 0.48978, 0.847, 0.53651, 0.95799, 0.60661, 0.99999, 0.71906, 0.9961, 0.84903, 0.83045, 0.97709, 0.58219, 0.99999, 0.42837, 0.97608, 0.28234, 0.9016, 0.20445, 0.83296, 0.12851, 0.74534, 0.05452, 0.59055, 0, 0.35543, 0, 0.18019, 0.80761, 0.93906, 0.74256, 0.83075, 0.69238, 0.74722, 0.63243, 0.64741, 0.59319, 0.58208, 0.52734, 0.47245, 0.40889, 0.27526], "triangles": [10, 18, 9, 18, 10, 19, 9, 18, 8, 10, 11, 19, 19, 11, 20, 18, 19, 8, 11, 12, 20, 20, 12, 21, 8, 19, 7, 12, 13, 21, 19, 20, 7, 20, 6, 7, 13, 14, 22, 21, 13, 22, 20, 5, 6, 20, 21, 5, 14, 23, 22, 14, 15, 23, 21, 4, 5, 21, 22, 4, 15, 24, 23, 15, 16, 24, 22, 23, 4, 23, 3, 4, 23, 24, 3, 24, 2, 3, 24, 17, 0, 24, 16, 17, 24, 0, 2, 0, 1, 2], "vertices": [1, 45, 15.13, 2.45, 1, 1, 45, 8.46, 64.27, 1, 1, 45, 45.71, 56.98, 1, 2, 45, 89.54, 45.31, 0.99846, 46, -19.47, 63.22, 0.00154, 2, 45, 107.82, 46.49, 0.94687, 46, -2.62, 56.06, 0.05313, 2, 45, 119.95, 52.83, 0.81185, 46, 11.07, 56.27, 0.18815, 2, 45, 138.86, 64.46, 0.55102, 46, 33.19, 58.16, 0.44898, 2, 45, 162.66, 63.37, 0.28623, 46, 53.96, 46.49, 0.71377, 2, 45, 187.64, 54.48, 0.07385, 46, 72.28, 27.32, 0.92615, 1, 46, 72.83, -9.07, 1, 1, 46, 49.28, -39.24, 1, 1, 46, 29.19, -52.43, 1, 2, 45, 163.55, -52.57, 0.00138, 46, 2.65, -57.48, 0.99862, 2, 45, 146.52, -59.49, 0.04232, 46, -15.68, -56.01, 0.95768, 2, 45, 125.9, -64.91, 0.19959, 46, -36.53, -51.59, 0.80041, 2, 45, 92.36, -65.75, 0.59084, 46, -66.87, -37.26, 0.40916, 2, 45, 44.2, -58.62, 0.94323, 46, -106.68, -9.25, 0.05677, 2, 45, 10.26, -47.4, 0.99647, 46, -131.96, 16.03, 0.00353, 1, 46, 64.88, -6.06, 1, 2, 45, 171.93, 18.82, 0.00753, 46, 42.22, 2.52, 0.99247, 2, 45, 153.34, 16.88, 0.16554, 46, 24.74, 9.15, 0.83446, 2, 45, 131.13, 14.56, 0.9638, 46, 3.86, 17.06, 0.0362, 1, 45, 116.59, 13.04, 1, 1, 45, 92.19, 10.49, 1, 1, 45, 48.31, 5.91, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 0, 34, 18, 20, 16, 18, 36, 18, 16, 36, 36, 20, 38, 36, 14, 38, 38, 22, 40, 38, 12, 40, 40, 24, 42, 40, 10, 42, 42, 26, 44, 42, 8, 44, 44, 28, 46, 44, 6, 46, 46, 30, 0, 48, 48, 46, 4, 48, 48, 32, 28, 30], "width": 153, "height": 204}}, "nose_white": {"nose_white": {"type": "mesh", "uvs": [0.51676, 0, 0.66693, 0.00472, 0.83246, 0.08918, 0.93485, 0.21413, 1, 0.38914, 1, 0.66328, 0.93794, 0.85801, 0.78457, 0.96955, 0.55869, 0.9998, 0.30492, 0.97144, 0.09299, 0.86746, 0, 0.71432, 0.00375, 0.54417, 0.07626, 0.32675, 0.19338, 0.12068, 0.29656, 0.02615, 0.55725, 0.96548, 0.55362, 0.87901, 0.54796, 0.74408, 0.53958, 0.54422, 0.53101, 0.33987, 0.52388, 0.16994, 0.52032, 0.08497], "triangles": [9, 17, 16, 9, 10, 17, 10, 18, 17, 10, 11, 18, 18, 11, 19, 11, 12, 19, 12, 13, 19, 13, 20, 19, 19, 20, 4, 20, 3, 4, 13, 14, 20, 14, 21, 20, 3, 21, 2, 3, 20, 21, 14, 15, 21, 15, 22, 21, 2, 22, 1, 2, 21, 22, 15, 0, 22, 22, 0, 1, 8, 16, 7, 9, 16, 8, 7, 16, 17, 18, 19, 5, 19, 4, 5, 17, 18, 6, 6, 7, 17, 6, 18, 5], "vertices": [1, 49, 94.67, -0.47, 1, 2, 48, 159.19, -28.62, 0.00217, 49, 92.13, -18.33, 0.99783, 2, 48, 140.42, -44.93, 0.03781, 49, 75.36, -36.68, 0.96219, 2, 48, 116.23, -52.33, 0.16647, 49, 52.18, -46.82, 0.83353, 2, 48, 84.31, -53.52, 0.54178, 49, 20.6, -51.66, 0.45822, 2, 48, 36.85, -43.4, 0.99715, 49, -27.7, -47.06, 0.00285, 1, 48, 4.69, -28.93, 1, 1, 48, -10.78, -6.81, 1, 1, 48, -10.36, 20.82, 1, 1, 49, -74.09, 41.14, 1, 1, 49, -53.36, 64.71, 1, 1, 49, -25.32, 73.25, 1, 1, 49, 4.62, 69.95, 1, 1, 49, 42.11, 57.64, 1, 1, 49, 77.08, 40.19, 1, 1, 49, 92.57, 26.28, 1, 1, 48, -4.38, 19.72, 1, 1, 49, -60.63, 9.88, 1, 2, 48, 34.18, 12.64, 0.93709, 49, -36.8, 8.29, 0.06291, 1, 49, -1.48, 5.94, 1, 1, 49, 34.62, 3.53, 1, 1, 49, 64.64, 1.53, 1, 1, 49, 79.66, 0.53, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 32, 16, 18, 32, 32, 14, 34, 32, 12, 34, 34, 20, 36, 34, 22, 36, 36, 10, 38, 36, 8, 38, 38, 24, 40, 38, 26, 40, 40, 6, 42, 40, 4, 42, 28, 42, 0, 44, 44, 42, 30, 44, 2, 44], "width": 120, "height": 177}}}}], "animations": {"dance": {"bones": {"body15": {"rotate": [{"curve": [0.122, -5.58, 0.244, -13.87]}, {"time": 0.3667, "value": -13.87, "curve": [0.5, -13.87, 0.633, -5.82]}, {"time": 0.7667, "curve": [0.889, 5.34, 1.011, 19.62]}, {"time": 1.1333, "value": 19.62, "curve": [1.256, 19.62, 1.378, 5.58]}, {"time": 1.5}], "translate": [{"y": 60.67, "curve": [0.122, 0, 0.244, 0, 0.122, 60.67, 0.244, -22.92]}, {"time": 0.3667, "y": -22.92, "curve": [0.5, 0, 0.633, 0, 0.5, -22.92, 0.633, 60.67]}, {"time": 0.7667, "y": 60.67, "curve": [0.889, 0, 1.011, 0, 0.889, 60.67, 1.011, -22.92]}, {"time": 1.1333, "y": -22.92, "curve": [1.256, 0, 1.378, 0, 1.256, -22.92, 1.378, 60.67]}, {"time": 1.5, "y": 60.67}]}, "body2": {"rotate": [{"curve": [0.122, -5.8, 0.244, -16.56]}, {"time": 0.3667, "value": -16.56, "curve": [0.5, -16.56, 0.633, -6.06]}, {"time": 0.7667, "curve": [0.889, 5.55, 1.011, 18.27]}, {"time": 1.1333, "value": 18.27, "curve": [1.256, 18.27, 1.378, 5.8]}, {"time": 1.5}], "translate": [{"y": 42.45, "curve": [0.122, 0, 0.244, 0, 0.122, 42.45, 0.244, -51.57]}, {"time": 0.3667, "y": -51.57, "curve": [0.5, 0, 0.633, 0, 0.5, -51.57, 0.633, 42.45]}, {"time": 0.7667, "y": 42.45, "curve": [0.889, 0, 1.011, 0, 0.889, 42.45, 1.011, -75.31]}, {"time": 1.1333, "y": -75.31, "curve": [1.256, 0, 1.378, 0, 1.256, -75.31, 1.378, 42.45]}, {"time": 1.5, "y": 42.45}]}, "leg_l2": {"translate": [{"x": 29.96, "y": -0.97, "curve": [0.122, 29.96, 0.244, 72.4, 0.122, 4.95, 0.244, 9.62]}, {"time": 0.3667, "x": 72.4, "y": 9.62, "curve": [0.5, 72.4, 0.633, 30.02, 0.5, 9.62, 0.633, 5.21]}, {"time": 0.7667, "x": 30.02, "y": -0.97, "curve": [0.889, 30.02, 1.011, 99.72, 0.889, -6.63, 1.011, -25.88]}, {"time": 1.1333, "x": 99.72, "y": -25.88, "curve": [1.256, 99.72, 1.378, 29.96, 1.256, -25.88, 1.378, -6.88]}, {"time": 1.5, "x": 29.96, "y": -0.97}]}, "leg_r2": {"rotate": [{"time": 0.3667, "value": 1.2}], "translate": [{"x": 35.88, "y": -1.16, "curve": [0.122, 35.88, 0.244, 76.79, 0.122, 4.15, 0.244, 7.39]}, {"time": 0.3667, "x": 76.79, "y": 7.39, "curve": [0.489, 76.79, 0.611, 22.77, 0.489, 7.39, 0.611, 4.34]}, {"time": 0.7333, "x": 22.77, "y": -0.73, "curve": [0.867, 22.77, 1, 95.97, 0.867, -6.27, 1, -24.44]}, {"time": 1.1333, "x": 95.97, "y": -24.44, "curve": [1.256, 95.97, 1.378, 35.88, 1.256, -24.44, 1.378, -6.46]}, {"time": 1.5, "x": 35.88, "y": -1.16}]}, "leg_l3": {"rotate": [{"time": 0.8333, "curve": [0.933, 0, 1.033, -15.45]}, {"time": 1.1333, "value": -15.45, "curve": [1.256, -15.45, 1.378, 0]}, {"time": 1.5}], "translate": [{"time": 0.7333, "x": -0.99, "y": -0.31, "curve": [0.767, -0.99, 0.8, -0.7, 0.767, -0.31, 0.8, 0.03]}, {"time": 0.8333, "curve": [0.933, 2.1, 1.033, 6.31, 0.933, -0.09, 1.033, -0.28]}, {"time": 1.1333, "x": 6.31, "y": -0.28, "curve": [1.256, 6.31, 1.378, 2.1, 1.256, -0.28, 1.378, -0.09]}, {"time": 1.5}]}, "leg_r3": {"rotate": [{"curve": [0.122, 0, 0.244, 22.85]}, {"time": 0.3667, "value": 22.85, "curve": [0.489, 22.85, 0.611, 0]}, {"time": 0.7333}], "translate": [{"curve": [0.122, 0.59, 0.244, 1.77, 0.122, -0.03, 0.244, -0.09]}, {"time": 0.3667, "x": 1.77, "y": -0.09, "curve": [0.489, 1.77, 0.611, 0.59, 0.489, -0.09, 0.611, -0.03]}, {"time": 0.7333}]}, "hand_l2": {"rotate": [{"curve": [0.122, 8.17, 0.244, 36.15]}, {"time": 0.3667, "value": 36.15, "curve": [0.5, 36.15, 0.633, 8.53]}, {"time": 0.7667, "curve": [0.889, -7.82, 1.011, -12.88]}, {"time": 1.1333, "value": -12.88, "curve": [1.256, -12.88, 1.378, -8.17]}, {"time": 1.5}]}, "hand_r2": {"rotate": [{"curve": [0.122, 8.36, 0.244, 21.44]}, {"time": 0.3667, "value": 21.44, "curve": [0.5, 21.44, 0.633, 8.73]}, {"time": 0.7667, "curve": [0.889, -8, 1.011, -28.74]}, {"time": 1.1333, "value": -28.74, "curve": [1.256, -28.74, 1.378, -8.36]}, {"time": 1.5}]}, "ear_l": {"translate": [{"x": 2.3, "y": 0.62, "curve": [0.122, 2.3, 0.244, -7.68, 0.122, 0.62, 0.244, -5.72]}, {"time": 0.3667, "x": -7.68, "y": -5.72, "curve": [0.5, -7.68, 0.633, 0, 0.5, -5.72, 0.633, 0]}, {"time": 0.7667}]}, "ear_r2": {"translate": [{"time": 0.7667, "curve": [0.889, 0, 1.011, -8.45, 0.889, 0, 1.011, 10.59]}, {"time": 1.1333, "x": -8.45, "y": 10.59, "curve": [1.256, -8.45, 1.378, 0, 1.256, 10.59, 1.378, 0]}, {"time": 1.5}]}}, "attachments": {"default": {"hand_r_white": {"hand_r_white": {"deform": [{"time": 1.1333, "vertices": [0.12826, -0.72449, 0.01231, -0.71274, -0.16245, -0.60373, -0.294, -0.38455, -0.35331, -0.11011, -0.37006, 0.17892, -0.35127, 0.45685, -0.30653, 0.6947, -0.06625, 0.66818, 0.236, 0.63669, 0.24825, 0.39011, 0.28601, 0.14011, 0.34533, -0.09647, 0.39532, -0.32431, 0.34895, -0.51309, 0.2475, -0.66513, 0.12077, -0.67072, 0.10445, -0.55389, 0.08227, -0.35587, 0.03889, -0.1058, 0.00938, 0.1586, -0.02844, 0.41339]}]}}}}}, "entry": {"slots": {"body_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "cheek_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "ear_l_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "ear_r_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "eyes_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "hand_l_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3363, "color": "ffffffff"}]}, "hand_r_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "head_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "leg_l_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "leg_r_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "nose_white": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}}}, "walk": {"bones": {"leg_l2": {"rotate": [{"value": 2.46, "curve": [0.061, 2.46, 0.139, 1.33]}, {"time": 0.2, "value": 2.29, "curve": [0.261, 3.24, 0.306, 5.74]}, {"time": 0.3667, "value": 5.74, "curve": [0.428, 5.74, 0.472, 4.59]}, {"time": 0.5333, "value": 4.59, "curve": [0.594, 4.59, 0.672, 6.27]}, {"time": 0.7333, "value": 6.83, "curve": [0.794, 7.4, 0.839, 7.58]}, {"time": 0.9, "value": 7.98, "curve": [0.961, 8.37, 1.039, 8.8]}, {"time": 1.1, "value": 9.22, "curve": [1.161, 9.64, 1.239, 10.08]}, {"time": 1.3, "value": 10.49, "curve": [1.361, 10.9, 1.406, 11.68]}, {"time": 1.4667, "value": 11.68, "curve": [1.528, 11.68, 1.6, 2.46]}, {"time": 1.6667, "value": 2.46}], "translate": [{"time": 0.9667, "x": -3.07, "y": 0.1}], "scale": [{"x": 0.96, "curve": [0.061, 0.96, 0.139, 1, 0.061, 1, 0.139, 1]}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": [0.428, 1, 0.472, 0.913, 0.428, 1, 0.472, 0.947]}, {"time": 0.5333, "x": 0.88, "y": 0.92, "curve": [0.594, 0.847, 0.672, 0.8, 0.594, 0.893, 0.672, 0.84]}, {"time": 0.7333, "x": 0.8, "y": 0.84, "curve": [0.794, 0.8, 0.839, 0.83, 0.794, 0.84, 0.839, 0.9]}, {"time": 0.9, "x": 0.84, "y": 0.9, "curve": [0.961, 0.85, 1.039, 0.85, 0.961, 0.9, 1.039, 0.83]}, {"time": 1.1, "x": 0.86, "y": 0.83, "curve": [1.161, 0.87, 1.239, 0.877, 1.161, 0.83, 1.239, 0.84]}, {"time": 1.3, "x": 0.9, "y": 0.87, "curve": [1.361, 0.923, 1.406, 0.967, 1.361, 0.9, 1.406, 0.972]}, {"time": 1.4667, "y": 1.01, "curve": [1.528, 1.033, 1.6, 0.96, 1.528, 1.048, 1.6, 1]}, {"time": 1.6667, "x": 0.96}]}, "leg_l3": {"rotate": [{"value": 6.24, "curve": [0.062, 2.95, 0.138, -8.88]}, {"time": 0.2, "value": -8.88, "curve": [0.262, -8.88, 0.305, -11.64]}, {"time": 0.3667, "value": -7.76, "curve": [0.429, -3.87, 0.471, 2.42]}, {"time": 0.5333, "value": 2.42, "curve": [0.595, 2.42, 0.671, 0.08]}, {"time": 0.7333, "value": 0.08, "curve": [0.795, 0.08, 0.871, 4.26]}, {"time": 0.9333, "value": 7, "curve": [0.995, 9.75, 1.071, 13.24]}, {"time": 1.1333, "value": 16.56, "curve": [1.195, 19.88, 1.238, 4.75]}, {"time": 1.3, "value": 4.75, "curve": [1.362, 4.75, 1.405, 4.66]}, {"time": 1.4667, "value": -1.14, "curve": [1.529, -6.94, 1.6, 9.78]}, {"time": 1.6667, "value": 6.24}]}, "leg_r2": {"translate": [{"time": 0.7333, "x": -0.03, "y": -1.08}], "scale": [{"x": 0.9, "y": 0.8, "curve": [0.062, 0.877, 0.138, 0.833, 0.062, 0.75, 0.138, 0.833]}, {"time": 0.2, "x": 0.8, "y": 0.8, "curve": [0.254, 0.771, 0.359, 0.8, 0.254, 0.771, 0.359, 0.78]}, {"time": 0.3667, "x": 0.8, "y": 0.78, "curve": [0.428, 0.8, 0.472, 0.76, 0.428, 0.78, 0.472, 0.7]}, {"time": 0.5333, "x": 0.82, "y": 0.78, "curve": [0.595, 0.88, 0.672, 0.99, 0.595, 0.86, 0.672, 1]}, {"time": 0.7333}, {"time": 0.9333, "x": 1.06, "y": 1.08, "curve": [0.995, 1.07, 1.038, 1.12, 0.995, 1.08, 1.038, 1.1]}, {"time": 1.1, "x": 1.12, "y": 1.1, "curve": [1.162, 1.12, 1.238, 1.07, 1.162, 1.1, 1.238, 1.077]}, {"time": 1.3, "x": 1.04, "y": 1.06, "curve": [1.362, 1.01, 1.405, 0.963, 1.362, 1.043, 1.405, 1.043]}, {"time": 1.4667, "x": 0.94, "curve": [1.528, 0.917, 1.605, 0.923, 1.528, 0.957, 1.605, 0.85]}, {"time": 1.6667, "x": 0.9, "y": 0.8}]}, "leg_r3": {"rotate": [{"value": 20.35, "curve": [0.062, 20.35, 0.138, -1.36]}, {"time": 0.2, "value": -7.88, "curve": [0.262, -14.41, 0.305, -25.37]}, {"time": 0.3667, "value": -25.37, "curve": [0.428, -25.37, 0.472, -10.97]}, {"time": 0.5333, "value": -5.68, "curve": [0.595, -0.38, 0.672, 12.98]}, {"time": 0.7333, "value": 12.98, "curve": [0.795, 12.98, 0.872, 11.69]}, {"time": 0.9333, "value": 10.58, "curve": [0.995, 9.47, 1.072, 8.22]}, {"time": 1.1333, "value": 6.31, "curve": [1.195, 4.4, 1.238, 1.46]}, {"time": 1.3, "value": -0.89, "curve": [1.362, -3.24, 1.405, -7.79]}, {"time": 1.4667, "value": -7.79, "curve": [1.528, -7.79, 1.605, 20.35]}, {"time": 1.6667, "value": 20.35}]}, "body2": {"translate": [{"y": -13.56, "curve": [0.061, 0, 0.139, 0, 0.061, -13.56, 0.139, -10.1]}, {"time": 0.2, "y": -7.32, "curve": [0.261, 0, 0.305, 0, 0.261, -4.55, 0.305, 3.09]}, {"time": 0.3667, "y": 3.09, "curve": [0.428, 0, 0.472, 0, 0.428, 3.09, 0.472, -1.07]}, {"time": 0.5333, "y": -3.46, "curve": [0.595, 0, 0.672, 0, 0.595, -5.86, 0.672, -11.27]}, {"time": 0.7333, "y": -11.27, "curve": [0.795, 0, 0.872, 0, 0.795, -11.27, 0.872, -3.69]}, {"time": 0.9333, "y": 1.14, "curve": [0.995, 0, 1.039, 0, 0.995, 5.97, 1.039, 17.71]}, {"time": 1.1, "y": 17.71, "curve": [1.161, 0, 1.205, 0, 1.161, 17.71, 1.205, 15.88]}, {"time": 1.2667, "y": 12.62, "curve": [1.328, 0, 1.405, 0, 1.328, 9.36, 1.405, 2.52]}, {"time": 1.4667, "y": -1.84, "curve": [1.528, 0, 1.605, 0, 1.528, -6.2, 1.605, -13.56]}, {"time": 1.6667, "y": -13.56}]}, "hand_r2": {"rotate": [{"value": -65.66}, {"time": 0.8333, "value": -85.09}, {"time": 1.6667, "value": -65.68}], "translate": [{"x": 32.51, "y": 22.85}]}, "hand_l2": {"rotate": [{"value": 83.21}, {"time": 0.8333, "value": 67}, {"time": 1.6667, "value": 83.21}], "translate": [{"x": 39.59, "y": -14.17}]}, "body15": {"translate": [{"curve": [0.122, 0, 0.244, 0, 0.122, 0, 0.244, 7.28]}, {"time": 0.3667, "y": 7.28, "curve": [0.489, 0, 0.611, 0, 0.489, 7.28, 0.611, 0]}, {"time": 0.7333, "curve": [0.889, 0, 1.044, 0, 0.889, 0, 1.044, 24.14]}, {"time": 1.2, "y": 24.14, "curve": [1.356, 0, 1.511, 0, 1.356, 24.14, 1.511, 0]}, {"time": 1.6667}]}}, "attachments": {"default": {"hand_r_white": {"hand_r_white": {"deform": [{"vertices": [-13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263, -13.30273, 1.60263]}]}}}}}}}