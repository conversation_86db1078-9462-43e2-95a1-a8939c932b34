{"skeleton": {"hash": "F8Llu3B+gF8", "spine": "4.2.40", "x": -387.05, "y": -19.19, "width": 733.12, "height": 747.6, "referenceScale": 21, "images": "../images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "Body_White", "parent": "root", "x": 1.11, "y": 4.51}, {"name": "Body_White2", "parent": "Body_White", "x": -9.53, "y": 303.38}, {"name": "Body_White3", "parent": "Body_White2", "length": 147.96, "rotation": 10.49, "x": 9.07, "y": 1.97}, {"name": "Body_White4", "parent": "Body_White2", "length": 116.39, "rotation": 179.47, "x": -9.79, "y": 0.36}, {"name": "Body_White5", "parent": "Body_White4", "length": 74.46, "rotation": -81.98, "x": 116.39}, {"name": "Body_White6", "parent": "Body_White5", "length": 80.83, "rotation": -6.72, "x": 74.46}, {"name": "Ear_<PERSON>", "parent": "Body_White6", "length": 126.87, "rotation": 0.24, "x": 79.73, "y": 0.11}, {"name": "Ear_White2", "parent": "Ear_<PERSON>", "length": 58.53, "rotation": -11.12, "x": 140.4, "y": -3.36}, {"name": "Ear_White3", "parent": "Ear_White2", "length": 64.89, "rotation": -3.83, "x": 58.53}, {"name": "Nane2_White", "parent": "Ear_<PERSON>", "length": 55.59, "rotation": 73.62, "x": 210.51, "y": -4.6}, {"name": "Nane2_White2", "parent": "Nane2_White", "length": 58.17, "rotation": 18.9, "x": 55.59}, {"name": "Nane2_White3", "parent": "Nane2_White2", "length": 46.44, "rotation": 42.65, "x": 58.17}, {"name": "Nane2_White4", "parent": "Nane2_White3", "length": 44.22, "rotation": -1.99, "x": 46.44}, {"name": "Nane2_White5", "parent": "Nane2_White4", "length": 55.46, "rotation": -52.05, "x": 44.22}, {"name": "Nane1_White", "parent": "Ear_<PERSON>", "length": 77.56, "rotation": -139.98, "x": 193.37, "y": -23.03}, {"name": "Nane1_White2", "parent": "Nane1_White", "length": 60.9, "rotation": -28.32, "x": 77.56}, {"name": "Nane1_White3", "parent": "Nane1_White2", "length": 53.6, "rotation": -13.19, "x": 60.9}, {"name": "Nane1_White4", "parent": "Nane1_White3", "length": 46.56, "rotation": 4.33, "x": 53.6}, {"name": "Nane1_White5", "parent": "Nane1_White4", "length": 68.41, "rotation": 12.45, "x": 46.56}, {"name": "Nane1_White6", "parent": "Nane1_White5", "length": 62.17, "rotation": 20.81, "x": 68.41}, {"name": "Leg2_r_White", "parent": "Body_White3", "length": 113.78, "rotation": -118.11, "x": 153.74, "y": 26.24}, {"name": "Leg2_r_White2", "parent": "Leg2_r_White", "length": 111.07, "rotation": 52.63, "x": 113.94, "y": -0.49}, {"name": "Leg2_r_White3", "parent": "Leg2_r_White2", "length": 123.7, "rotation": -45.06, "x": 111.07}, {"name": "Leg2_r_White4", "parent": "Body_White", "length": 51.4, "rotation": -106.26, "x": 153.13, "y": 37.94, "color": "ff3f00ff", "icon": "ik"}, {"name": "Leg2_l_White", "parent": "Body_White3", "length": 108.38, "rotation": -118.93, "x": 79.04, "y": 38.49}, {"name": "Leg2_l_White2", "parent": "Leg2_l_White", "length": 85.94, "rotation": 49.94, "x": 108.38}, {"name": "Leg2_l_White3", "parent": "Leg2_l_White2", "length": 124.48, "rotation": -48.91, "x": 85.94}, {"name": "Leg2_l_White4", "parent": "Body_White", "length": 50.7, "rotation": -111.18, "x": 43.66, "y": 62.72, "color": "ff3f00ff", "icon": "ik"}, {"name": "Leg1_r_White", "parent": "Body_White4", "length": 79.72, "rotation": 71.7, "x": 58.85, "y": -17.2}, {"name": "Leg1_r_White2", "parent": "Leg1_r_White", "length": 101.83, "rotation": 40.81, "x": 80.22, "y": -0.99}, {"name": "Leg1_r_White3", "parent": "Leg1_r_White2", "length": 126.86, "rotation": -33.56, "x": 101.58, "y": -0.36}, {"name": "Leg1_r_White4", "parent": "Body_White", "length": 48.41, "rotation": -101.85, "x": -92.64, "y": 27.27, "color": "ff3f00ff", "icon": "ik"}, {"name": "Leg1_l_White", "parent": "Body_White4", "length": 71.27, "rotation": 69.86, "x": 144.82, "y": -15.24}, {"name": "Leg1_l_White2", "parent": "Leg1_l_White", "length": 111.41, "rotation": 37.43, "x": 71.77, "y": 0.81}, {"name": "Leg1_l_White3", "parent": "Leg1_l_White2", "length": 101.43, "rotation": -37.92, "x": 110.75, "y": 0.63}, {"name": "Leg1_l_White4", "parent": "Body_White", "length": 49, "rotation": -113.2, "x": -192.66, "y": 52.42, "color": "ff3f00ff", "icon": "ik"}, {"name": "Eye2_White", "parent": "Ear_<PERSON>", "rotation": -16.63, "x": 70.75, "y": 97.93}, {"name": "<PERSON>l_<PERSON>", "parent": "Body_White3", "length": 50.91, "rotation": 16.97, "x": 153.48, "y": 56.87}, {"name": "Tail_White2", "parent": "<PERSON>l_<PERSON>", "length": 46.33, "rotation": -35.4, "x": 51.53, "y": 0.5}, {"name": "Tail_White3", "parent": "Tail_White2", "length": 48.33, "rotation": -43.33, "x": 46.33}, {"name": "<PERSON>l_White4", "parent": "Tail_White3", "length": 47.34, "rotation": -31.39, "x": 48.1, "y": -0.28}, {"name": "Tail_White5", "parent": "<PERSON>l_White4", "length": 57.09, "rotation": -15.22, "x": 47.34}, {"name": "Tail_White6", "parent": "Tail_White5", "length": 51.35, "rotation": 18.25, "x": 57.09}, {"name": "Tail_White7", "parent": "Tail_White6", "length": 34.16, "rotation": 40.96, "x": 51.7, "y": -0.07}, {"name": "Tail_White8", "parent": "Tail_White7", "length": 43.21, "rotation": 47.66, "x": 34.16}, {"name": "Head_White2", "parent": "Ear_<PERSON>", "length": 105.4, "rotation": 131.21, "x": 0.14, "y": 13.8}], "slots": [{"name": "leg1_l_white", "bone": "Leg1_l_White", "attachment": "leg1_l_white"}, {"name": "leg2_l_white", "bone": "Leg2_l_White", "attachment": "leg2_l_white"}, {"name": "tail_white", "bone": "<PERSON>l_<PERSON>", "attachment": "tail_white"}, {"name": "body_white", "bone": "Body_White2", "attachment": "body_white"}, {"name": "head2_white", "bone": "Head_White2", "attachment": "head2_white"}, {"name": "head_white", "bone": "Ear_<PERSON>", "attachment": "head_white"}, {"name": "leg1_r_white", "bone": "Leg1_r_White", "attachment": "leg1_r_white"}, {"name": "eye1_white", "bone": "Eye2_White", "attachment": "eye1_white"}, {"name": "nane1_white", "bone": "Nane1_White", "attachment": "nane1_white"}, {"name": "nane2_white", "bone": "Nane2_White", "attachment": "nane2_white"}, {"name": "ear_white", "bone": "Ear_White2", "attachment": "ear_white"}, {"name": "leg2_r_white", "bone": "Leg2_r_White", "attachment": "leg2_r_white"}], "ik": [{"name": "Leg1_l_White4", "order": 3, "bones": ["Leg1_l_White2", "Leg1_l_White3"], "target": "Leg1_l_White4", "bendPositive": false}, {"name": "Leg1_r_White4", "order": 2, "bones": ["Leg1_r_White2", "Leg1_r_White3"], "target": "Leg1_r_White4", "bendPositive": false}, {"name": "Leg2_l_White4", "order": 1, "bones": ["Leg2_l_White2", "Leg2_l_White3"], "target": "Leg2_l_White4", "bendPositive": false}, {"name": "Leg2_r_White4", "bones": ["Leg2_r_White2", "Leg2_r_White3"], "target": "Leg2_r_White4", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"body_white": {"body_white": {"type": "mesh", "uvs": [0.28, 1e-05, 0.32485, 0.01447, 0.34272, 0.06414, 0.34598, 0.20404, 0.37008, 0.26596, 0.41662, 0.2865, 0.50235, 0.26815, 0.6129, 0.238, 0.72344, 0.20786, 0.85558, 0.23756, 0.92383, 0.31395, 0.97847, 0.41665, 1, 0.55489, 1, 0.62141, 0.98583, 0.72934, 0.84374, 0.83437, 0.67044, 0.92948, 0.5393, 0.97906, 0.40899, 0.99996, 0.29433, 0.99903, 0.16358, 0.94731, 0.08624, 0.87984, 0.03646, 0.80188, 0, 0.672, 1e-05, 0.55343, 0, 0.43775, 0.0136, 0.29957, 0.07064, 0.1865, 0.12372, 0.09653, 0.20008, 0.01515, 0.52314, 0.60513, 0.22983, 0.60513, 0.20775, 0.37815, 0.73903, 0.54441], "triangles": [22, 23, 31, 23, 24, 31, 24, 32, 31, 24, 25, 32, 25, 26, 32, 17, 18, 30, 20, 31, 19, 30, 18, 31, 18, 19, 31, 20, 21, 31, 21, 22, 31, 31, 5, 30, 17, 30, 16, 16, 33, 15, 16, 30, 33, 15, 33, 14, 14, 33, 13, 33, 12, 13, 5, 6, 30, 30, 7, 33, 30, 6, 7, 33, 11, 12, 7, 8, 33, 33, 10, 11, 33, 9, 10, 33, 8, 9, 2, 0, 1, 0, 2, 29, 27, 28, 32, 28, 29, 3, 5, 32, 4, 4, 32, 3, 32, 5, 31, 2, 3, 29, 26, 27, 32, 3, 32, 28], "vertices": [2, 6, 127.04, -34.31, 0.28763, 7, 47.16, -34.62, 0.71237, 4, 6, 121.95, -54, 0.44358, 7, 41.99, -54.28, 0.55559, 3, -55.35, 209.53, 2e-05, 4, 75.55, -196.87, 0.00081, 4, 6, 105.29, -61.64, 0.5858, 7, 25.29, -61.86, 0.40937, 3, -50.64, 191.81, 0.00061, 4, 67.53, -180.38, 0.00423, 5, 6, 58.62, -62.46, 0.86249, 7, -21.38, -62.48, 0.0206, 3, -57.71, 145.67, 0.02131, 4, 65.66, -133.74, 0.07334, 5, 125.36, -68.89, 0.02226, 4, 6, 37.83, -72.79, 0.63057, 3, -51.04, 123.44, 0.08004, 4, 54.86, -113.19, 0.20504, 5, 103.5, -76.72, 0.08435, 4, 6, 30.71, -93.2, 0.36929, 3, -32.13, 112.97, 0.21401, 4, 34.3, -106.53, 0.32659, 5, 94.04, -96.15, 0.0901, 4, 6, 36.32, -131.03, 0.15493, 3, 6.1, 112.11, 0.53172, 4, -3.4, -113, 0.2775, 5, 95.19, -134.38, 0.03586, 4, 6, 45.73, -179.84, 0.04971, 3, 55.8, 113.13, 0.84712, 4, -51.98, -123.51, 0.09822, 5, 98.82, -183.95, 0.00496, 4, 6, 55.13, -228.65, 0.01361, 3, 105.5, 114.15, 0.96861, 4, -100.57, -134.01, 0.01776, 5, 102.44, -233.53, 2e-05, 3, 6, 44.45, -286.7, 0.00141, 3, 160.91, 93.82, 0.99851, 4, -158.85, -124.64, 8e-05, 2, 6, 18.58, -316.41, 3e-05, 3, 185.82, 63.3, 0.99997, 1, 3, 203.24, 25.24, 1, 1, 3, 204.17, -21.81, 1, 1, 3, 200.14, -43.63, 1, 1, 3, 187.45, -77.88, 1, 2, 3, 119.54, -100.93, 0.9972, 4, -155.48, 74.42, 0.0028, 2, 3, 38.73, -118.22, 0.8181, 4, -79.46, 106.84, 0.1819, 2, 3, -21.07, -123.96, 0.44868, 4, -21.86, 123.91, 0.55132, 3, 3, -78.76, -120.37, 0.14223, 4, 35.45, 131.41, 0.85754, 5, -141.42, -61.83, 0.00022, 3, 3, -128.36, -110.87, 0.02817, 4, 85.95, 131.57, 0.94051, 5, -134.53, -11.8, 0.03133, 3, 3, -181.83, -83.42, 0.00014, 4, 143.68, 114.86, 0.7848, 5, -109.93, 43.04, 0.21506, 2, 4, 177.95, 92.67, 0.5695, 5, -83.18, 73.88, 0.4305, 3, 7, -218.32, 77.32, 0.00083, 4, 200.11, 66.88, 0.37268, 5, -54.55, 92.22, 0.62649, 4, 6, -95.39, 91.97, 0.002, 7, -174.73, 92.61, 0.0119, 4, 216.57, 23.72, 0.13234, 5, -9.52, 102.5, 0.85375, 4, 6, -55.85, 91.44, 0.03634, 7, -135.2, 91.91, 0.04767, 4, 216.93, -15.82, 0.01959, 5, 29.69, 97.35, 0.89641, 3, 6, -17.28, 90.93, 0.14791, 7, -96.63, 91.23, 0.1381, 5, 67.93, 92.32, 0.71399, 3, 6, 28.72, 84.33, 0.29386, 7, -50.66, 84.43, 0.35564, 5, 112.84, 80.39, 0.3505, 3, 6, 66.08, 58.71, 0.18269, 7, -13.4, 58.66, 0.74326, 5, 146.96, 50.57, 0.07406, 2, 7, 16.19, 34.75, 0.99844, 5, 173.66, 23.48, 0.00156, 1, 7, 42.73, 0.65, 1, 3, 6, -76.17, -138.68, 1e-05, 3, -5.36, -0.05, 0.78109, 4, -13.59, -0.71, 0.2189, 2, 4, 115.57, 0.48, 0.59062, 5, -0.59, -0.75, 0.40938, 4, 6, 1.37, -0.82, 0.62579, 3, -128.14, 99.66, 7e-05, 4, 125.99, -75.12, 0.00093, 5, 75.72, -0.97, 0.37321, 2, 6, -57.19, -234.02, 9e-05, 3, 91.81, 2.55, 0.99991], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 12, 14, 14, 16, 58, 0], "width": 441, "height": 334}}, "ear_white": {"ear_white": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 0.5, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25], "triangles": [10, 13, 9, 13, 14, 9, 9, 14, 8, 14, 7, 8, 4, 5, 14, 14, 5, 7, 5, 6, 7, 0, 1, 11, 1, 12, 11, 11, 12, 10, 12, 13, 10, 1, 2, 12, 2, 3, 12, 12, 3, 13, 3, 4, 13, 13, 4, 14], "vertices": [2, 8, -2.02, -60.83, 0.99997, 9, -56.34, -64.74, 3e-05, 1, 8, -10.29, -14.42, 1, 1, 8, -18.57, 32, 1, 1, 8, 15.34, 38.04, 1, 2, 8, 49.24, 44.09, 0.7687, 9, -12.21, 43.37, 0.2313, 2, 8, 83.15, 50.13, 0.16925, 9, 21.22, 51.67, 0.83075, 2, 8, 117.05, 56.18, 0.00678, 9, 54.64, 59.96, 0.99322, 1, 9, 66, 14.21, 1, 2, 8, 133.6, -36.65, 5e-05, 9, 77.36, -31.55, 0.99995, 2, 8, 99.7, -42.69, 0.09341, 9, 43.93, -39.85, 0.90659, 2, 8, 65.8, -48.74, 0.55826, 9, 10.51, -48.14, 0.44174, 2, 8, 31.89, -54.78, 0.95009, 9, -22.92, -56.44, 0.04991, 1, 8, 23.61, -8.37, 1, 2, 8, 57.52, -2.32, 0.76204, 9, -0.85, -2.39, 0.23796, 1, 9, 32.58, 5.91, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 95, "height": 138}}, "eye1_white": {"eye1_white": {"x": -4.4, "y": -5.46, "rotation": -74.38, "width": 161, "height": 90}}, "head2_white": {"head2_white": {"x": 72.45, "y": -1.41, "rotation": 137.78, "width": 216, "height": 109}}, "head_white": {"head_white": {"x": 84.28, "y": 65.35, "rotation": -91.01, "width": 304, "height": 332}}, "leg1_l_white": {"leg1_l_white": {"type": "mesh", "uvs": [0.5582, 0, 0.72022, 0.00563, 0.93, 0.04539, 1, 0.09713, 1, 0.19582, 0.95768, 0.38956, 0.92552, 0.48135, 0.87732, 0.57502, 0.85408, 0.67561, 0.83065, 0.79853, 0.79133, 0.85762, 0.71416, 0.97361, 0.47381, 1, 0.33351, 1, 0.15436, 0.99465, 0, 0.95703, 0, 0.93816, 0.00454, 0.90617, 0.12694, 0.79012, 0.19727, 0.72345, 0.24992, 0.64198, 0.26945, 0.5391, 0.25097, 0.39249, 0.20535, 0.20849, 0.1885, 0.14054, 0.26741, 0.02843, 0.45058, 0, 0.75, 0.47654, 0.47234, 0.20274, 0.44618, 0.84412, 0.65227, 0.84909, 0.27934, 0.82547, 0.73916, 0.54587, 0.65903, 0.63587, 0.4952, 0.54723, 0.4489, 0.47753, 0.47377, 0.62928], "triangles": [17, 18, 31, 14, 15, 16, 11, 30, 10, 14, 17, 31, 13, 14, 31, 14, 16, 17, 29, 13, 31, 29, 12, 13, 30, 12, 29, 30, 11, 12, 27, 34, 35, 32, 27, 6, 32, 34, 27, 21, 35, 34, 7, 32, 6, 36, 21, 34, 33, 34, 32, 33, 32, 7, 36, 34, 33, 20, 21, 36, 8, 33, 7, 9, 33, 8, 36, 19, 20, 29, 31, 19, 18, 19, 31, 36, 29, 19, 29, 36, 33, 30, 29, 33, 9, 30, 33, 10, 30, 9, 22, 23, 28, 5, 28, 4, 5, 22, 28, 27, 22, 5, 35, 22, 27, 6, 27, 5, 21, 22, 35, 28, 2, 3, 28, 26, 0, 25, 26, 28, 24, 25, 28, 28, 0, 1, 23, 24, 28, 3, 4, 28, 28, 1, 2], "vertices": [1, 33, 7.82, -12.3, 1, 2, 33, 2.68, 6.29, 0.99676, 34, -46.77, 50.56, 0.00324, 2, 33, 5.28, 33.57, 0.92993, 34, -26.56, 69.04, 0.07007, 2, 33, 17, 46.69, 0.8419, 34, -9.08, 70.86, 0.1581, 2, 33, 43.98, 57.15, 0.50727, 34, 17.82, 60.35, 0.49273, 3, 33, 98.95, 77.41, 0.00951, 34, 71.42, 38.6, 0.98735, 35, -39.54, 18.18, 0.00315, 2, 34, 87.73, 26.63, 0.49364, 35, -18.71, 18.54, 0.50636, 1, 35, 2.96, 17.81, 1, 1, 35, 33.2, 26.75, 1, 2, 35, 70.79, 38.04, 0.93277, 36, -47.5, 37.22, 0.06723, 2, 35, 93.06, 39.68, 0.68626, 36, -25.55, 40.13, 0.31374, 2, 35, 144.87, 43.03, 0.09754, 36, 20.53, 45.61, 0.90246, 2, 35, 165.31, 18.53, 0.00064, 36, 41.78, 22.68, 0.99936, 1, 36, 48.36, 7.38, 1, 1, 36, 55.2, -12.82, 1, 1, 36, 51.61, -34.28, 1, 2, 35, 163.9, -40.88, 0.0018, 36, 46.16, -36.6, 0.9982, 2, 35, 153.72, -43.27, 0.01559, 36, 36.47, -40.02, 0.98441, 2, 35, 102.78, -39.25, 0.51646, 36, -9.03, -42.7, 0.48354, 3, 34, 129.08, -87.26, 4e-05, 35, 76.43, -39.69, 0.89059, 36, -33.57, -47.07, 0.10937, 3, 34, 104.13, -72.92, 0.02134, 35, 49.92, -43.38, 0.97653, 36, -61.62, -54.16, 0.00213, 2, 34, 76.5, -57.16, 0.24764, 35, 22.25, -49.08, 0.75236, 2, 34, 45.42, -41.9, 0.87381, 35, -8.83, -60.79, 0.12619, 2, 33, 84.54, -28.54, 0.44018, 34, -9.15, -29.88, 0.55982, 2, 33, 64.67, -38.04, 0.87555, 34, -30.31, -23.82, 0.12445, 1, 33, 28.31, -41.45, 1, 1, 33, 12.33, -24.25, 1, 2, 34, 81.07, 6.8, 0.54536, 35, -11.73, -0.26, 0.45464, 2, 33, 70.43, 0.13, 0.56727, 34, -0.53, 0.66, 0.43273, 2, 35, 102.18, 0.86, 0.74918, 36, -11.17, -0.37, 0.25082, 2, 35, 96.11, 23.71, 0.69113, 36, -20.61, 23.39, 0.30887, 2, 35, 108.49, -19.16, 0.43598, 36, -6, -20.92, 0.56402, 2, 34, 110.66, -0.3, 0.17012, 35, 0.5, -0.78, 0.82988, 3, 34, 134.8, -17.54, 0.04578, 35, 30.13, 0.67, 0.95363, 36, -71.37, -1.92, 0.00059, 3, 34, 102.68, -28.12, 0.20304, 35, 11.5, -27.56, 0.79693, 36, -89.16, -30.69, 3e-05, 3, 34, 80.25, -27.08, 0.52447, 35, -6.73, -40.64, 0.47551, 36, -106.99, -44.31, 2e-05, 3, 34, 126.46, -37.98, 0.05187, 35, 36.27, -20.53, 0.94697, 36, -64.61, -22.93, 0.00117], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 8, 10, 10, 12, 44, 46, 46, 48, 34, 36, 36, 38, 18, 20, 20, 22], "width": 119, "height": 313}}, "leg1_r_white": {"leg1_r_white": {"type": "mesh", "uvs": [0.52721, 0, 0.56373, 0, 0.6975, 0.00829, 0.88159, 0.04344, 0.90359, 0.05399, 1, 0.12001, 1, 0.15274, 0.99217, 0.18366, 0.95607, 0.27779, 0.91998, 0.37192, 0.89912, 0.42634, 0.87826, 0.48075, 0.87903, 0.5928, 0.88031, 0.77852, 0.83986, 0.84879, 0.7994, 0.91905, 0.69934, 0.97766, 0.46894, 1, 0.38938, 0.99934, 0.23538, 0.9994, 0.12638, 0.98611, 0.05916, 0.97295, 0.0184, 0.94847, 0.01456, 0.92113, 0.02321, 0.88912, 0.10156, 0.79043, 0.17991, 0.69175, 0.21696, 0.58307, 0.19196, 0.47067, 0.16696, 0.35826, 0.00178, 0.13988, 0.00095, 0.0778, 0.08496, 0.04646, 0.25054, 0.0174, 0.37839, 0.00303, 0.65152, 0.37216, 0.53037, 0.27858, 0.46825, 0.83651, 0.68903, 0.42004, 0.63813, 0.51486], "triangles": [24, 25, 37, 15, 37, 14, 20, 21, 22, 24, 20, 23, 23, 20, 22, 16, 37, 15, 37, 20, 24, 20, 37, 19, 37, 17, 18, 18, 19, 37, 16, 17, 37, 14, 37, 13, 38, 35, 9, 10, 38, 9, 28, 35, 38, 11, 38, 10, 39, 28, 38, 39, 38, 11, 27, 28, 39, 39, 11, 12, 27, 12, 26, 12, 27, 39, 13, 26, 12, 13, 37, 26, 25, 26, 37, 30, 31, 32, 4, 5, 6, 6, 36, 4, 6, 7, 36, 4, 36, 2, 8, 36, 7, 36, 30, 33, 29, 30, 36, 8, 35, 36, 9, 35, 8, 29, 36, 35, 28, 29, 35, 36, 0, 1, 36, 1, 2, 4, 2, 3, 34, 0, 36, 33, 34, 36, 30, 32, 33], "vertices": [2, 29, 43.78, 6.14, 0.96325, 30, -23.54, 27.82, 0.03675, 2, 29, 42.41, 10.16, 0.93043, 30, -21.99, 31.76, 0.06957, 2, 29, 39.74, 25.76, 0.77242, 30, -13.97, 45.3, 0.22758, 2, 29, 42.86, 49.68, 0.53003, 30, 3.75, 61.32, 0.46997, 2, 29, 45.06, 53.17, 0.50159, 30, 7.64, 62.53, 0.49841, 2, 29, 60.41, 70.55, 0.31463, 30, 30.31, 65.76, 0.68537, 2, 29, 69.84, 73.88, 0.25099, 30, 39.53, 62.21, 0.74901, 2, 29, 79.06, 76.18, 0.18582, 30, 47.93, 58.04, 0.81418, 3, 29, 103.14, 77.37, 0.12695, 30, 67.13, 43.8, 0.55781, 31, -37.9, 27.9, 0.31524, 3, 29, 127.06, 80.04, 0.06807, 30, 87.32, 30.7, 0.30145, 31, -14.11, 25.65, 0.63048, 3, 29, 151.4, 88.45, 0.03403, 30, 112.33, 21.17, 0.15327, 31, -2.95, 23.88, 0.81269, 2, 30, 113.4, 16.26, 0.0051, 31, 13.73, 24.92, 0.9949, 3, 30, 148.17, 4.84, 0.00166, 31, 47.09, 31.7, 0.97825, 32, -90.3, 29.07, 0.02009, 2, 31, 101.95, 42.56, 0.85464, 32, -32.54, 44.1, 0.14536, 2, 31, 125.6, 41.28, 0.57788, 32, -10.05, 42.64, 0.42212, 2, 31, 153.84, 40.65, 0.19396, 32, 14.83, 42.05, 0.80604, 2, 31, 177.81, 32.28, 0.03039, 32, 37.3, 34.57, 0.96961, 1, 32, 49.98, 10.02, 1, 1, 32, 51.67, 1.02, 1, 1, 32, 55.32, -16.32, 1, 2, 31, 191.1, -32.55, 0.00053, 32, 53.96, -29.42, 0.99947, 2, 31, 188.16, -40.75, 0.00519, 32, 51.58, -37.79, 0.99481, 2, 31, 181.22, -46.39, 0.01581, 32, 45.14, -43.88, 0.98419, 2, 31, 172.47, -47.87, 0.03883, 32, 36.78, -45.98, 0.96117, 2, 31, 161.39, -48, 0.22316, 32, 26.37, -46.98, 0.77684, 2, 31, 123.44, -43.2, 0.56817, 32, -7.24, -45.71, 0.43183, 2, 31, 92.01, -41.04, 0.98571, 32, -37.12, -46, 0.01429, 2, 30, 119.69, -72.01, 0.11714, 31, 59.15, -43.45, 0.88286, 2, 30, 87.52, -60.67, 0.37039, 31, 29.48, -50.12, 0.62961, 2, 30, 58.62, -49.91, 0.72908, 31, 0.8, -56.49, 0.27092, 2, 29, 102.88, -37.21, 0.27868, 30, -6.16, -42.93, 0.72132, 2, 29, 85.2, -43.35, 0.5267, 30, -23.54, -36.44, 0.4733, 2, 29, 73.25, -37.29, 0.69366, 30, -28.82, -24.25, 0.30634, 2, 29, 59.02, -22.31, 0.96754, 30, -30.19, -3.75, 0.03246, 1, 29, 50.19, -9.82, 1, 3, 29, 141.98, 53.49, 1e-05, 30, 81.23, 0.91, 0.65385, 31, -4.77, -1.78, 0.34615, 2, 30, 55.14, -1.67, 0.99829, 31, -26.3, -16.99, 0.00171, 2, 31, 134.88, -0.24, 0.10273, 32, -1.64, -0.5, 0.89727, 3, 29, 157.41, 64.99, 0.01317, 30, 101.55, -0.51, 0.35183, 31, 0.06, -0.14, 0.635, 3, 30, 125.86, -16.65, 0.14524, 31, 29.23, -0.16, 0.84325, 32, -97.63, -0.62, 0.01151], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 48, 50, 50, 52, 26, 28, 28, 30, 14, 16, 16, 18, 54, 56, 56, 58, 22, 24, 24, 26, 18, 20, 20, 22], "width": 116, "height": 302}}, "leg2_l_white": {"leg2_l_white": {"type": "mesh", "uvs": [0.8393, 0, 0.93765, 0, 1, 0.05459, 1, 0.13484, 0.9702, 0.26694, 0.89884, 0.38194, 0.82748, 0.49694, 0.80838, 0.54479, 0.68273, 0.85247, 0.63206, 0.97652, 0.39707, 0.99935, 0.23841, 1, 0.05866, 0.98239, 0.0548, 0.91418, 0.14866, 0.80036, 0.24252, 0.68654, 0.29414, 0.59652, 0.29526, 0.51595, 0.24392, 0.45541, 0.10345, 0.30716, 0, 0.19798, 0.08415, 0.15338, 0.30325, 0.08389, 0.67149, 0.00418, 0.37708, 0.21864, 0.67192, 0.46697, 0.58768, 0.39623, 0.61475, 0.55505, 0.42521, 0.85247, 0.26919, 0.82832, 0.5548, 0.85573], "triangles": [13, 14, 29, 9, 30, 8, 13, 11, 12, 28, 11, 29, 29, 11, 13, 10, 28, 30, 10, 30, 9, 11, 28, 10, 6, 25, 5, 7, 25, 6, 27, 17, 25, 27, 25, 7, 16, 17, 27, 15, 28, 29, 14, 15, 29, 8, 27, 7, 15, 16, 27, 27, 8, 15, 15, 30, 28, 8, 30, 15, 20, 21, 19, 4, 26, 24, 18, 19, 24, 5, 26, 4, 26, 18, 24, 25, 26, 5, 17, 18, 26, 17, 26, 25, 0, 1, 2, 24, 22, 23, 21, 22, 24, 2, 3, 0, 19, 21, 24, 0, 4, 23, 3, 4, 0, 23, 4, 24], "vertices": [2, 25, 21.05, 44.22, 0.9045, 26, -22.36, 95.3, 0.0955, 2, 25, 16.46, 58.01, 0.8855, 26, -14.76, 107.7, 0.1145, 2, 25, 29.49, 72.08, 0.84338, 26, 4.39, 106.77, 0.15662, 2, 25, 52.95, 79.9, 0.71289, 26, 25.47, 93.85, 0.28711, 2, 25, 92.95, 88.59, 0.34732, 26, 57.87, 68.83, 0.65268, 3, 25, 129.9, 89.78, 0.08331, 26, 82.56, 41.32, 0.89363, 27, -33.36, 24.61, 0.02307, 3, 25, 166.84, 90.98, 0.00115, 26, 107.25, 13.81, 0.1877, 27, 3.6, 25.14, 0.81115, 2, 26, 118.35, 3.7, 0.00516, 27, 18.51, 26.85, 0.99484, 2, 27, 114.51, 37.47, 0.73439, 28, -12.41, 36.74, 0.26561, 2, 27, 153.22, 41.76, 0.11415, 28, 25.93, 43.56, 0.88585, 1, 28, 45.04, 13.71, 1, 1, 28, 53.71, -8.09, 1, 1, 28, 58.25, -34.83, 1, 2, 27, 160.42, -45.42, 0.03234, 28, 38.86, -42.96, 0.96766, 2, 27, 122.81, -42.67, 0.5145, 28, 1.15, -42.68, 0.4855, 3, 26, 111.86, -90.44, 0.00208, 27, 85.2, -39.91, 0.97838, 28, -36.56, -42.42, 0.01954, 2, 26, 92.2, -69.45, 0.07751, 27, 56.45, -40.93, 0.92249, 2, 26, 71.13, -56.33, 0.38798, 27, 32.72, -48.19, 0.61202, 2, 26, 51.26, -53.06, 0.71812, 27, 17.19, -61.01, 0.28188, 3, 25, 145.23, -29.06, 0.12346, 26, 1.47, -46.9, 0.86874, 27, -20.18, -94.49, 0.00779, 2, 25, 118.15, -54.21, 0.5169, 26, -35.2, -42.36, 0.4831, 2, 25, 101.18, -46.75, 0.66523, 26, -40.41, -24.58, 0.33477, 2, 25, 70.63, -22.79, 0.99727, 26, -41.74, 14.23, 0.00273, 2, 25, 30.12, 21.09, 0.95569, 26, -34.23, 73.47, 0.04431, 2, 25, 106.56, 0.69, 0.74885, 26, -0.64, 1.84, 0.25115, 2, 26, 87.36, -0.97, 0.26271, 27, 1.67, 0.43, 0.73729, 1, 26, 62.27, -0.2, 1, 1, 27, 30.09, 0.48, 1, 2, 27, 125.9, 1.15, 0.27315, 28, 1.35, 1.24, 0.72685, 2, 27, 125.7, -23.09, 0.46219, 28, 2.74, -22.96, 0.53781, 2, 27, 121.13, 19.73, 0.65043, 28, -4.64, 19.46, 0.34957], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 42, 44, 44, 46, 8, 10, 10, 12, 26, 28, 28, 30, 14, 16, 16, 18, 36, 38, 38, 40, 40, 42], "width": 148, "height": 309}}, "leg2_r_white": {"leg2_r_white": {"type": "mesh", "uvs": [0.44765, 0, 0.54059, 0, 0.64315, 0.00462, 0.78624, 0.03125, 0.86574, 0.06069, 0.93073, 0.10732, 0.988, 0.17445, 0.96802, 0.24011, 0.91437, 0.39657, 0.94361, 0.47028, 0.99717, 0.52868, 0.99999, 0.58835, 0.91551, 0.78032, 0.89633, 0.85963, 0.85601, 0.9377, 0.81572, 0.96346, 0.7614, 0.98247, 0.66077, 0.99765, 0.60801, 1, 0.48664, 1, 0.32102, 0.99182, 0.25189, 0.95673, 0.2673, 0.92252, 0.34196, 0.83896, 0.45438, 0.71313, 0.46419, 0.60103, 0.29218, 0.53384, 0.14468, 0.46398, 0.04248, 0.38707, 0.0092, 0.34549, 0, 0.27082, 0, 0.21613, 0.0679, 0.11506, 0.10476, 0.09263, 0.19144, 0.04444, 0.27469, 0.01633, 0.77913, 0.55859, 0.70403, 0.51396, 0.76071, 0.60538, 0.63574, 0.87183, 0.4654, 0.86301, 0.79214, 0.87183, 0.35172, 0.32519, 0.43011, 0.24238, 0.50425, 0.40961, 0.69863, 0.73998], "triangles": [22, 23, 40, 14, 39, 41, 15, 16, 39, 14, 15, 39, 19, 20, 22, 21, 22, 20, 17, 39, 16, 19, 40, 39, 18, 19, 39, 19, 22, 40, 17, 18, 39, 36, 10, 11, 38, 25, 36, 38, 36, 11, 24, 25, 38, 45, 24, 38, 12, 38, 11, 45, 38, 12, 45, 41, 39, 40, 24, 45, 39, 40, 45, 23, 24, 40, 41, 45, 12, 13, 41, 12, 14, 41, 13, 28, 29, 42, 44, 42, 43, 8, 44, 43, 27, 28, 42, 27, 42, 44, 37, 44, 8, 37, 8, 9, 36, 37, 9, 26, 27, 44, 26, 44, 37, 10, 36, 9, 25, 26, 37, 25, 37, 36, 6, 43, 5, 0, 34, 35, 43, 34, 0, 33, 34, 43, 32, 33, 43, 31, 32, 43, 43, 0, 1, 2, 43, 1, 43, 2, 3, 43, 3, 4, 30, 31, 43, 42, 30, 43, 43, 4, 5, 29, 30, 42, 7, 8, 43, 6, 7, 43], "vertices": [1, 21, -11.09, -24.68, 1, 1, 21, -15.24, -11.58, 1, 1, 21, -18.12, 3.41, 1, 2, 21, -14.63, 26.71, 0.99795, 22, -56.41, 118.69, 0.00205, 2, 21, -7.26, 41.39, 0.98936, 22, -40.28, 121.73, 0.01064, 2, 21, 7.14, 56.04, 0.96112, 22, -19.89, 119.18, 0.03888, 2, 21, 29.51, 72.03, 0.88142, 22, 6.39, 111.11, 0.11858, 2, 21, 54.78, 76.95, 0.73672, 22, 25.64, 94.01, 0.26328, 2, 21, 115.27, 87.84, 0.14026, 22, 71.01, 52.55, 0.85974, 3, 21, 141.33, 100.65, 0.01673, 22, 97.01, 39.61, 0.91926, 23, -37.98, 18.02, 0.06401, 3, 21, 160.62, 115.08, 0.00033, 22, 120.18, 33.05, 0.56449, 23, -16.96, 29.79, 0.43518, 2, 22, 139.46, 20.05, 0.12087, 23, 5.86, 34.26, 0.87913, 2, 23, 81.67, 35.01, 0.99821, 24, -45.58, 30.25, 0.00179, 2, 23, 112.59, 37.61, 0.77396, 24, -15.12, 36.18, 0.22604, 2, 23, 143.57, 37.04, 0.16557, 24, 15.74, 38.98, 0.83443, 2, 23, 154.49, 32.93, 0.06106, 24, 27.05, 36.07, 0.93894, 2, 23, 163.19, 26.32, 0.01428, 24, 36.4, 30.43, 0.98572, 1, 24, 46.24, 17.81, 1, 1, 24, 49.31, 10.57, 1, 1, 24, 54.33, -6.65, 1, 2, 23, 178.13, -37.16, 0.00061, 24, 58.13, -31.05, 0.99939, 2, 23, 166.46, -49.61, 0.01787, 24, 47.87, -44.69, 0.98213, 2, 23, 152.93, -49.69, 0.06345, 24, 34.43, -46.23, 0.93655, 2, 23, 118.96, -44.5, 0.53608, 24, 0.1, -44.75, 0.46392, 3, 22, 132.99, -73.9, 0.00616, 23, 67.79, -36.69, 0.99323, 24, -51.61, -42.52, 0.00061, 2, 22, 98.06, -47.66, 0.41802, 23, 24.54, -42.88, 0.58198, 2, 22, 62.03, -53.47, 0.91803, 23, 3.21, -72.49, 0.08197, 3, 21, 174.74, -12.67, 0.0283, 22, 27.22, -55.72, 0.96876, 23, -19.78, -98.71, 0.00294, 2, 21, 150.75, -36.14, 0.3141, 22, -5.98, -50.9, 0.6859, 2, 21, 136.81, -45.73, 0.55477, 22, -22.07, -45.64, 0.44523, 2, 21, 109.5, -55.83, 0.91055, 22, -46.67, -30.06, 0.08945, 2, 21, 89.19, -62.28, 0.99267, 22, -64.12, -17.84, 0.00733, 1, 21, 48.63, -64.62, 1, 1, 21, 38.65, -62.07, 1, 1, 21, 16.88, -55.54, 1, 1, 21, 2.72, -47.13, 1, 2, 22, 111.23, -0.04, 0.48271, 23, 0.14, 0.08, 0.51729, 2, 21, 168.27, 72.04, 6e-05, 22, 90.62, 0.84, 0.99994, 1, 23, 18.56, 0.58, 1, 2, 23, 123.99, 0.5, 0.46492, 24, 0.23, 0.53, 0.53508, 2, 23, 125, -24.9, 0.44074, 24, 3.98, -24.61, 0.55926, 2, 23, 119.95, 23.27, 0.65096, 24, -6.25, 22.73, 0.34904, 2, 21, 113.94, 0.14, 0.46146, 22, 0.51, 0.38, 0.53854, 2, 21, 79.69, 1.42, 0.99573, 22, -19.27, 28.38, 0.00427, 1, 22, 40.38, -0.02, 1, 1, 23, 71.79, 0.7, 1], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 14, 16, 16, 18, 44, 46, 46, 48, 40, 42], "width": 148, "height": 390}}, "nane1_white": {"nane1_white": {"type": "mesh", "uvs": [0.17583, 0, 0.33907, 0.02955, 0.51447, 0.09961, 0.60062, 0.17394, 0.66763, 0.31417, 0.73464, 0.45441, 0.71848, 0.54378, 0.74259, 0.56766, 0.81422, 0.73363, 0.85119, 0.78989, 1, 0.83529, 1, 0.84267, 0.9638, 0.87475, 0.84804, 0.90933, 0.68511, 0.89663, 0.73056, 0.92429, 0.85687, 0.96431, 0.81749, 0.98339, 0.73215, 1, 0.65746, 1, 0.54138, 0.99173, 0.4068, 0.92622, 0.39079, 0.90993, 0.17652, 0.85329, 0.07312, 0.73136, 0.08985, 0.57321, 0.13507, 0.47448, 0.18029, 0.37576, 0.19285, 0.32793, 0.18458, 0.27109, 0.16663, 0.24257, 0, 0.11254, 0, 0.0643, 0.01171, 0.01703, 0.09158, 0, 0.3668, 0.20246, 0.44073, 0.37155, 0.44073, 0.52233, 0.44895, 0.64774, 0.56122, 0.82952], "triangles": [39, 8, 9, 12, 9, 10, 11, 12, 10, 14, 39, 9, 13, 14, 9, 12, 13, 9, 21, 22, 39, 21, 39, 14, 17, 15, 16, 20, 21, 14, 19, 20, 14, 15, 19, 14, 18, 19, 15, 17, 18, 15, 38, 7, 8, 39, 38, 8, 23, 24, 38, 39, 23, 38, 22, 23, 39, 6, 37, 5, 25, 26, 37, 38, 37, 6, 38, 6, 7, 25, 37, 38, 24, 25, 38, 36, 4, 5, 37, 36, 5, 26, 27, 36, 37, 26, 36, 29, 30, 35, 36, 28, 29, 4, 36, 35, 4, 35, 3, 36, 29, 35, 27, 28, 36, 0, 31, 32, 35, 1, 2, 35, 2, 3, 34, 32, 33, 1, 31, 0, 35, 31, 1, 32, 34, 0, 31, 35, 30], "vertices": [1, 15, -0.56, 21.7, 1, 1, 15, 27.28, 37.51, 1, 2, 15, 67.58, 45.45, 0.82999, 16, -30.35, 35.28, 0.17001, 2, 15, 98.2, 39.94, 0.32365, 16, -0.77, 44.95, 0.67635, 3, 16, 51.13, 45.95, 0.71149, 17, -19.99, 42.51, 0.28792, 18, -70.17, 47.94, 0.00059, 4, 16, 103.04, 46.95, 0.03919, 17, 30.33, 55.33, 0.76575, 18, -19.03, 56.92, 0.18418, 19, -51.77, 69.73, 0.01088, 4, 17, 62.48, 52.6, 0.32834, 18, 12.82, 51.78, 0.51336, 19, -21.77, 57.84, 0.15816, 20, -63.75, 86.11, 0.00014, 4, 17, 71.03, 57.13, 0.19596, 18, 21.69, 55.66, 0.53225, 19, -12.28, 59.71, 0.26967, 20, -54.21, 84.48, 0.00212, 4, 17, 130.59, 70.89, 4e-05, 18, 82.12, 64.87, 0.0601, 19, 48.71, 55.68, 0.5982, 20, 1.37, 59.04, 0.34167, 3, 18, 102.76, 70.34, 0.00743, 19, 70.05, 56.56, 0.2537, 20, 21.63, 52.29, 0.73887, 2, 19, 93.44, 78.41, 0.01287, 20, 51.26, 64.39, 0.98713, 2, 19, 95.99, 77.66, 0.01225, 20, 53.37, 62.79, 0.98775, 2, 19, 105.18, 67.99, 0.00609, 20, 58.53, 50.49, 0.99391, 1, 20, 55.52, 25.91, 1, 2, 19, 98.25, 16.3, 0.00223, 20, 33.69, 4.63, 0.99777, 1, 20, 46.69, 5.34, 1, 1, 20, 72.27, 15.29, 1, 1, 20, 73.34, 5.34, 1, 1, 20, 68.57, -10.85, 1, 1, 20, 60.23, -21.87, 1, 2, 19, 123.6, -18.82, 0.00266, 20, 44.9, -37.2, 0.99734, 3, 18, 146.13, -14.98, 4e-05, 19, 94, -36.11, 0.26619, 20, 11.1, -42.84, 0.73377, 3, 18, 140.1, -17.55, 0.00068, 19, 87.55, -37.3, 0.40304, 20, 4.64, -41.67, 0.59628, 3, 18, 117.11, -55.73, 0.05864, 19, 56.88, -69.64, 0.89112, 20, -35.52, -61, 0.05024, 3, 17, 130.91, -66.23, 0.00311, 18, 72.09, -71.87, 0.31049, 19, 9.43, -75.69, 0.6864, 3, 17, 74.03, -63.61, 0.21798, 18, 15.57, -64.97, 0.65225, 19, -44.27, -56.75, 0.12977, 4, 16, 85.68, -62.85, 0.0438, 17, 38.47, -55.54, 0.63961, 18, -19.28, -54.24, 0.31184, 19, -75.98, -38.76, 0.00475, 4, 15, 101.89, -66.36, 0.00737, 16, 52.89, -46.88, 0.41834, 17, 2.9, -47.47, 0.551, 18, -54.13, -43.51, 0.02328, 4, 15, 90.44, -53.32, 0.0638, 16, 36.63, -40.83, 0.68352, 17, -14.31, -45.29, 0.25158, 18, -71.13, -40.03, 0.0011, 3, 15, 74.02, -41.06, 0.33471, 16, 16.36, -37.82, 0.61999, 17, -34.73, -46.99, 0.0453, 3, 15, 64.11, -36.83, 0.60282, 16, 5.63, -38.81, 0.38722, 17, -44.96, -50.39, 0.00996, 1, 15, 8.61, -29.4, 1, 1, 15, -4.48, -18.01, 1, 1, 15, -15.87, -5.22, 1, 1, 15, -10.79, 9.94, 1, 2, 15, 77.54, 0.57, 0.51721, 16, -0.29, 0.49, 0.48279, 2, 16, 62.02, 0.46, 0.37821, 17, 0.99, 0.7, 0.62179, 3, 17, 55.2, 1.15, 0.29972, 18, 1.68, 1.03, 0.70012, 19, -43.6, 10.69, 0.00016, 2, 18, 46.77, -0.48, 0.48159, 19, 0.1, -0.51, 0.51841, 2, 19, 68.66, 1.07, 0.44724, 20, 0.62, 0.92, 0.55276], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 50, 52, 52, 54, 6, 8, 8, 10], "width": 185, "height": 360}}, "nane2_white": {"nane2_white": {"type": "mesh", "uvs": [0.67431, 0, 0.79953, 0.01329, 0.93542, 0.12164, 1, 0.25018, 1, 0.31528, 0.99655, 0.41847, 0.86732, 0.59108, 0.76027, 0.73406, 0.57599, 0.98019, 0.47893, 1, 0.44741, 1, 0.45879, 0.90384, 0.47015, 0.84047, 0.28772, 0.96187, 0.13759, 0.90166, 0.03447, 0.75976, 0, 0.54444, 0, 0.46717, 0.04605, 0.47198, 0.12337, 0.47548, 0.1986, 0.41576, 0.28957, 0.2632, 0.38054, 0.11063, 0.55555, 0, 0.77098, 0.24489, 0.53632, 0.27083, 0.39841, 0.49132, 0.26873, 0.6729], "triangles": [14, 27, 13, 27, 14, 15, 19, 16, 18, 27, 15, 19, 19, 15, 16, 19, 20, 27, 16, 17, 18, 8, 9, 11, 9, 10, 11, 11, 12, 8, 8, 12, 7, 12, 13, 27, 27, 26, 12, 7, 12, 26, 27, 20, 26, 20, 21, 26, 21, 22, 26, 26, 22, 25, 7, 26, 25, 7, 25, 24, 0, 24, 25, 0, 25, 23, 25, 22, 23, 24, 0, 1, 7, 24, 6, 6, 24, 5, 5, 24, 4, 4, 24, 2, 2, 3, 4, 24, 1, 2], "vertices": [3, 10, 88.11, -31.91, 0.03054, 11, 20.43, -40.73, 0.96635, 12, -55.35, -4.38, 0.00311, 2, 10, 57.84, -38.08, 0.4573, 11, -10.21, -36.76, 0.5427, 2, 10, 21.1, -30.63, 0.98352, 11, -42.55, -17.81, 0.01648, 1, 10, 0.45, -15.48, 1, 1, 10, -2.25, -5.67, 1, 4, 10, -5.7, 10.1, 0.99526, 11, -54.72, 29.4, 0.0035, 12, -63.11, 98.11, 0.00061, 13, -112.89, 94.25, 0.00063, 4, 10, 17.81, 44.53, 0.67709, 11, -21.31, 54.37, 0.22077, 12, -21.63, 93.84, 0.05271, 13, -71.28, 91.43, 0.04943, 4, 10, 37.29, 73.06, 0.26435, 11, 6.36, 75.05, 0.35668, 12, 12.73, 90.31, 0.17134, 13, -36.82, 89.08, 0.20763, 4, 10, 70.83, 122.17, 0.0297, 11, 53.99, 110.64, 0.16851, 12, 71.88, 84.22, 0.18795, 13, 22.51, 85.05, 0.61384, 4, 10, 93.04, 131.48, 0.01628, 11, 78.02, 112.27, 0.13799, 12, 90.66, 69.13, 0.16692, 13, 41.8, 70.62, 0.67881, 4, 10, 100.52, 133.54, 0.01545, 11, 85.76, 111.79, 0.136, 12, 96.03, 63.53, 0.16531, 13, 47.36, 65.21, 0.68324, 5, 10, 101.8, 118.31, 0.01581, 11, 82.04, 96.97, 0.13125, 12, 83.25, 55.15, 0.1636, 13, 34.88, 56.39, 0.68912, 14, -50.21, 27.31, 0.00021, 5, 10, 101.73, 108.02, 0.01354, 11, 78.65, 87.25, 0.1078, 12, 74.17, 50.31, 0.1472, 13, 25.97, 51.24, 0.72592, 14, -51.63, 17.12, 0.00553, 4, 11, 124.63, 103.43, 0.00161, 12, 118.95, 31.05, 3e-05, 13, 71.39, 33.55, 0.43936, 14, -9.75, 42.06, 0.559, 2, 13, 91.33, 1.05, 0.01547, 14, 28.14, 37.8, 0.98453, 1, 14, 56.32, 19.31, 1, 1, 14, 69.33, -12.85, 1, 1, 14, 70.98, -24.81, 1, 2, 13, 60.7, -62.79, 0.00896, 14, 59.65, -25.62, 0.99104, 3, 12, 92.14, -50.75, 0.00274, 13, 47.44, -49.14, 0.11411, 14, 40.73, -27.68, 0.88314, 3, 12, 72.59, -43.86, 0.07502, 13, 27.66, -42.92, 0.44414, 14, 23.66, -39.46, 0.48084, 3, 12, 39.89, -44.21, 0.59237, 13, -5.01, -44.41, 0.34644, 14, 4.75, -66.14, 0.06119, 4, 11, 93.66, -27.91, 0.07025, 12, 7.19, -44.57, 0.87912, 13, -37.68, -45.9, 0.04971, 14, -14.17, -92.81, 0.00093, 2, 11, 49.6, -42.52, 0.83428, 12, -35.11, -25.47, 0.16572, 2, 10, 55.02, -1.33, 0.67058, 11, -0.97, -1.08, 0.32942, 2, 11, 56.92, -0.58, 0.75332, 12, -1.31, 0.42, 0.24668, 3, 12, 47.05, -0.21, 0.3935, 13, 0.62, -0.18, 0.60638, 14, -26.67, -34.5, 0.00011, 2, 13, 43.28, -2.08, 0.40123, 14, 1.06, -2.02, 0.59877], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 40, 42, 42, 44, 10, 12, 12, 14, 14, 16], "width": 247, "height": 157}}, "tail_white": {"tail_white": {"type": "mesh", "uvs": [0.44914, 0, 0.64224, 0.05177, 0.74937, 0.13624, 0.84072, 0.26708, 0.85609, 0.44004, 0.82019, 0.56569, 0.7843, 0.69134, 0.78424, 0.75172, 0.81874, 0.81418, 0.88267, 0.8431, 0.99486, 0.84229, 1, 0.87489, 0.90353, 0.95943, 0.75181, 1, 0.66671, 1, 0.54018, 0.99442, 0.40276, 0.9338, 0.34226, 0.88429, 0.3012, 0.80884, 0.26014, 0.73338, 0.27632, 0.61357, 0.29251, 0.49376, 0.2865, 0.42338, 0.26384, 0.34597, 0.20921, 0.28683, 0.01405, 0.24783, 0, 0.22028, 0, 0.18786, 0.07471, 0.08332, 0.22017, 0.00926, 0.34697, 0, 0.2465, 0.15306, 0.47798, 0.17858, 0.61138, 0.30193, 0.63884, 0.46214, 0.59961, 0.64504, 0.63688, 0.83077, 0.78597, 0.90024], "triangles": [28, 29, 31, 27, 28, 31, 25, 26, 27, 31, 25, 27, 24, 25, 31, 32, 24, 31, 23, 24, 32, 37, 36, 8, 37, 8, 9, 11, 12, 9, 11, 9, 10, 37, 9, 12, 13, 37, 12, 36, 7, 8, 15, 16, 36, 14, 15, 36, 37, 14, 36, 13, 14, 37, 7, 35, 6, 18, 19, 35, 36, 35, 7, 18, 35, 36, 17, 18, 36, 16, 17, 36, 21, 22, 34, 5, 34, 4, 35, 21, 34, 35, 34, 5, 20, 21, 35, 6, 35, 5, 19, 20, 35, 22, 23, 33, 33, 3, 4, 34, 33, 4, 22, 33, 34, 32, 0, 1, 32, 1, 2, 33, 32, 2, 33, 2, 3, 23, 32, 33, 31, 29, 30, 30, 0, 32, 31, 30, 32], "vertices": [2, 39, 35.55, 49.79, 0.91276, 40, -42.01, 28.82, 0.08724, 2, 39, 78.38, 40.37, 0.28961, 40, -4.39, 51.36, 0.71039, 3, 39, 104.41, 18.87, 0.02649, 40, 29.3, 53.58, 0.92368, 41, -44.1, 36.19, 0.04983, 3, 40, 71.54, 44.64, 0.48215, 41, -3.38, 50.55, 0.51641, 42, -62.21, 35.47, 0.00144, 3, 40, 113.35, 15.32, 0.01764, 41, 47.57, 47.29, 0.61667, 42, -12.18, 45.69, 0.3657, 3, 41, 83.32, 34.98, 0.07011, 42, 25.53, 43.2, 0.87165, 43, -16.44, 50.91, 0.05824, 4, 42, 63.25, 40.7, 0.36784, 43, 18.6, 36.72, 0.56567, 44, -0.88, 49.48, 0.05078, 45, 12.97, 59.22, 0.01571, 4, 42, 80.88, 43.12, 0.08551, 43, 36.09, 33.5, 0.57991, 44, 10.22, 35.58, 0.23351, 45, 10.17, 41.66, 0.10107, 4, 42, 98.1, 52.92, 0.00436, 43, 55.52, 37.42, 0.15232, 44, 27.46, 25.8, 0.33038, 45, 14.55, 22.33, 0.51294, 3, 43, 66.35, 49.27, 0.01184, 44, 43.41, 27.65, 0.03976, 45, 26.67, 11.79, 0.9484, 1, 45, 50.3, 8.29, 1, 1, 45, 49.88, -1.37, 1, 2, 44, 68.29, 3.66, 0.01021, 45, 25.69, -22.76, 0.98979, 2, 44, 50.53, -25.86, 0.60581, 45, -8.09, -29.51, 0.39419, 3, 43, 103.54, -4.29, 0.01448, 44, 36.38, -37.18, 0.89977, 45, -25.99, -26.68, 0.08575, 2, 43, 97.07, -30.49, 0.17078, 44, 14.31, -52.72, 0.82922, 3, 42, 145.15, -29.99, 0.00088, 43, 74.23, -56.06, 0.55899, 44, -19.69, -57.06, 0.44013, 4, 42, 132.47, -44.75, 0.01366, 43, 57.56, -66.11, 0.73021, 44, -38.87, -53.72, 0.25601, 38, -30.99, -200.2, 0.00012, 4, 42, 111.64, -56.46, 0.07696, 43, 34.12, -70.7, 0.8327, 44, -59.58, -41.82, 0.08838, 38, -28.5, -176.44, 0.00196, 6, 40, 101.39, -137.76, 0.00109, 41, 117.09, -89.61, 0.00234, 42, 90.82, -68.17, 0.2009, 43, 10.68, -75.3, 0.77285, 44, -80.3, -29.92, 0.01529, 38, -26, -152.68, 0.00753, 5, 40, 76, -112.98, 0.01752, 41, 82.52, -81.69, 0.03993, 42, 55.38, -69.59, 0.47163, 43, -23.42, -65.55, 0.43623, 38, -6.67, -122.95, 0.03469, 5, 40, 50.62, -88.21, 0.09324, 41, 47.95, -73.76, 0.16899, 42, 19.94, -71.01, 0.50718, 43, -57.52, -55.8, 0.10315, 38, 12.67, -93.22, 0.12744, 5, 40, 33.64, -76.24, 0.17934, 41, 27.21, -72.38, 0.21821, 42, -0.42, -75.12, 0.32631, 43, -78.15, -53.33, 0.0249, 38, 21.1, -74.23, 0.25125, 6, 39, 10.55, -56.62, 0.00595, 40, 12.83, -65.73, 0.2161, 41, 3.98, -74.25, 0.14123, 42, -22.35, -83.03, 0.12929, 43, -101.46, -53.97, 0.00182, 38, 27.33, -51.76, 0.50561, 5, 39, -3.38, -40.97, 0.00109, 40, -8.04, -63.91, 0.09173, 41, -14.79, -83.56, 0.03962, 42, -38.02, -96.94, 0.03086, 38, 25.04, -30.94, 0.8367, 1, 38, -6.53, -1.58, 1, 2, 39, -50.21, -27.71, 4e-05, 38, -5.44, 7, 0.99996, 2, 39, -51.53, -18.25, 0.00478, 38, -1.04, 15.48, 0.99522, 2, 39, -40.03, 14.46, 0.27878, 38, 27.28, 35.47, 0.72122, 2, 39, -12.37, 40.35, 0.92954, 38, 64.83, 40.55, 0.07046, 2, 39, 14, 46.78, 0.99675, 40, -55.62, 11.84, 0.00325, 2, 39, -0.96, -0.84, 0.14755, 38, 50.27, 0.37, 0.85245, 2, 39, 48.9, -1.47, 0.13904, 40, 2.88, 0.69, 0.86096, 2, 40, 49, 0.12, 0.39234, 41, 0.56, 0.81, 0.60766, 2, 41, 48.12, 0.58, 0.39842, 42, 0.61, 0.76, 0.60158, 2, 42, 55.13, -0.13, 0.80513, 43, -1.9, 0.49, 0.19487, 2, 43, 53.35, -1.56, 0.36912, 44, 0.27, -2.21, 0.63088, 1, 45, 3.69, -1.62, 1], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 38, 40, 40, 42, 8, 10, 10, 12, 34, 36, 36, 38, 20, 22], "width": 213, "height": 295}}}}], "animations": {"eat": {"bones": {"Eye2_White": {"scale": [{"time": 0.8333, "curve": [0.9, 0.73, 0.967, 0.461, 0.9, 1, 0.967, 1]}, {"time": 1.0333, "x": 0.191, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "x": 0.091, "curve": [3.044, 0.394, 3.089, 0.697, 3.044, 1, 3.089, 1]}, {"time": 3.1333}]}, "Body_White3": {"rotate": [{"value": 0.19, "curve": [0.078, 0.12, 0.156, 0]}, {"time": 0.2333, "curve": [0.311, 0, 0.389, 0]}, {"time": 0.4667, "value": 0.19, "curve": [0.667, 0.68, 0.867, 9.06]}, {"time": 1.0667, "value": 9.06, "curve": [1.2, 9.06, 1.333, 2.89]}, {"time": 1.4667, "value": 1.91, "curve": [1.7, 0.19, 1.933, 0.67]}, {"time": 2.1667, "value": 0.19, "curve": [2.244, 0.04, 2.322, 0]}, {"time": 2.4, "curve": [2.6, 0, 2.8, 0.55]}, {"time": 3, "value": 0.55, "curve": [3.111, 0.55, 3.222, 0.3]}, {"time": 3.3333, "value": 0.19}]}, "Nane2_White2": {"rotate": [{"value": 0.79, "curve": [0.067, 0.61, 0.133, 0.32]}, {"time": 0.2, "value": 0.15, "curve": [0.233, 0.06, 0.267, 0]}, {"time": 0.3, "curve": [0.356, 0, 0.411, 0.15]}, {"time": 0.4667, "value": 0.28, "curve": [0.667, 0.74, 0.867, 0.95]}, {"time": 1.0667, "value": 1.76, "curve": [1.233, 2.44, 1.4, 4.76]}, {"time": 1.5667, "value": 4.76, "curve": [1.767, 4.76, 1.967, 1.94]}, {"time": 2.1667, "value": 0.79, "curve": [2.233, 0.4, 2.3, 0.32]}, {"time": 2.3667, "value": 0.15, "curve": [2.4, 0.06, 2.433, 0]}, {"time": 2.4667, "curve": [2.667, 0, 2.867, 1.42]}, {"time": 3.0667, "value": 1.42, "curve": [3.156, 1.42, 3.244, 1.03]}, {"time": 3.3333, "value": 0.79}]}, "Leg1_r_White2": {"rotate": [{"time": 0.4667, "value": 1.89, "curve": [0.667, 10.63, 0.867, 28.11]}, {"time": 1.0667, "value": 28.11, "curve": [1.2, 28.11, 1.333, 28.11]}, {"time": 1.4667, "value": 26.04, "curve": [1.7, 22.43, 1.933, 9.07]}, {"time": 2.1667, "value": 0.58}]}, "Tail_White": {"rotate": [{"curve": [0.156, 0, 0.311, -1.89]}, {"time": 0.4667, "value": -2.66, "curve": [0.667, -3.64, 0.867, -5.25]}, {"time": 1.0667, "value": -5.25, "curve": [1.267, -5.25, 1.467, 2.86]}, {"time": 1.6667, "value": 2.86, "curve": [1.833, 2.86, 2, 0.9]}, {"time": 2.1667, "curve": [2.367, -1.07, 2.567, -3.05]}, {"time": 2.7667, "value": -3.05, "curve": [2.956, -3.05, 3.144, 0]}, {"time": 3.3333}]}, "Tail_White3": {"rotate": [{"value": -1.86, "curve": [0.044, -1.62, 0.089, -1.3]}, {"time": 0.1333, "value": -1.07, "curve": [0.211, -0.68, 0.289, 0]}, {"time": 0.3667, "curve": [0.4, 0, 0.433, 0]}, {"time": 0.4667, "value": -0.25, "curve": [0.667, -1.78, 0.867, -7.1]}, {"time": 1.0667, "value": -7.1, "curve": [1.267, -7.1, 1.467, -3.68]}, {"time": 1.6667, "value": -2.7, "curve": [1.878, -1.67, 2.089, -1.73]}, {"time": 2.3, "value": -1.07, "curve": [2.378, -0.83, 2.456, 0]}, {"time": 2.5333, "curve": [2.722, 0, 2.911, -3.05]}, {"time": 3.1, "value": -3.05, "curve": [3.178, -3.05, 3.256, -2.28]}, {"time": 3.3333, "value": -1.86}]}, "Nane2_White5": {"rotate": [{"value": 1.35, "curve": [0.022, 1.4, 0.044, 1.42]}, {"time": 0.0667, "value": 1.42, "curve": [0.111, 1.42, 0.156, 1.39]}, {"time": 0.2, "value": 1.27, "curve": [0.289, 1.04, 0.378, 0.38]}, {"time": 0.4667, "value": 0.38, "curve": [0.667, 0.38, 0.867, 1.49]}, {"time": 1.0667, "value": 2.32, "curve": [1.233, 3.01, 1.4, 4.92]}, {"time": 1.5667, "value": 4.92, "curve": [1.767, 4.92, 1.967, 1.35]}, {"time": 2.1667, "value": 1.35, "curve": [2.189, 1.35, 2.211, 1.42]}, {"time": 2.2333, "value": 1.42, "curve": [2.278, 1.42, 2.322, 1.38]}, {"time": 2.3667, "value": 1.27, "curve": [2.522, 0.91, 2.678, 0]}, {"time": 2.8333, "curve": [3, 0, 3.167, 0.93]}, {"time": 3.3333, "value": 1.35}]}, "Tail_White6": {"rotate": [{"value": -2.73, "curve": [0.044, -2.96, 0.089, -3.05]}, {"time": 0.1333, "value": -3.05, "curve": [0.244, -3.05, 0.356, -1.12]}, {"time": 0.4667, "value": -1.12, "curve": [0.722, -1.12, 0.978, -7.98]}, {"time": 1.2333, "value": -7.98, "curve": [1.433, -7.98, 1.633, -2.14]}, {"time": 1.8333, "value": -2.14, "curve": [1.989, -2.14, 2.144, -3.05]}, {"time": 2.3, "value": -3.05, "curve": [2.489, -3.05, 2.678, 0]}, {"time": 2.8667, "curve": [3.022, 0, 3.178, -1.94]}, {"time": 3.3333, "value": -2.73}]}, "Leg2_l_White3": {"rotate": [{"curve": [0.156, -0.65, 0.311, 0]}, {"time": 0.4667, "value": -1.96, "curve": [0.667, -4.48, 0.867, -36.94]}, {"time": 1.0667, "value": -36.94, "curve": [1.2, -36.94, 1.333, -36.94]}, {"time": 1.4667, "value": -33.86, "curve": [1.7, -28.48, 1.933, -11.42]}, {"time": 2.1667, "value": -0.2}]}, "Nane1_White6": {"rotate": [{"value": -0.72, "curve": [0.078, -0.87, 0.156, -1.1]}, {"time": 0.2333, "value": -1.1, "curve": [0.311, -1.1, 0.389, -0.73]}, {"time": 0.4667, "value": -0.73, "curve": [0.667, -0.73, 0.867, -8.41]}, {"time": 1.0667, "value": -8.94, "curve": [1.244, -9.41, 1.422, -9.41]}, {"time": 1.6, "value": -9.41, "curve": [1.789, -9.41, 1.978, -0.72]}, {"time": 2.1667, "value": -0.72, "curve": [2.244, -0.72, 2.322, -1.1]}, {"time": 2.4, "value": -1.1, "curve": [2.6, -1.1, 2.8, 0]}, {"time": 3, "curve": [3.111, 0, 3.222, -0.5]}, {"time": 3.3333, "value": -0.72}]}, "Nane1_White": {"rotate": [{"value": -0.39, "curve": [0.078, -0.24, 0.156, 0]}, {"time": 0.2333, "curve": [0.311, 0, 0.389, 0]}, {"time": 0.4667, "value": -0.38, "curve": [0.667, -1.35, 0.867, -5.14]}, {"time": 1.0667, "value": -6.57, "curve": [1.244, -7.84, 1.422, -8.48]}, {"time": 1.6, "value": -8.48, "curve": [1.789, -8.48, 1.978, -1.33]}, {"time": 2.1667, "value": -0.39, "curve": [2.244, 0, 2.322, 0]}, {"time": 2.4, "curve": [2.6, 0, 2.8, -1.1]}, {"time": 3, "value": -1.1, "curve": [3.111, -1.1, 3.222, -0.61]}, {"time": 3.3333, "value": -0.39}]}, "Tail_White4": {"rotate": [{"value": -2.66, "curve": [0.044, -2.49, 0.089, -2.23]}, {"time": 0.1333, "value": -1.98, "curve": [0.244, -1.34, 0.356, 0]}, {"time": 0.4667, "curve": [0.689, 0, 0.911, -7.91]}, {"time": 1.1333, "value": -7.91, "curve": [1.333, -7.91, 1.533, -4.51]}, {"time": 1.7333, "value": -3.5, "curve": [1.922, -2.53, 2.111, -2.71]}, {"time": 2.3, "value": -1.98, "curve": [2.411, -1.54, 2.522, 0]}, {"time": 2.6333, "curve": [2.822, 0, 3.011, -3.05]}, {"time": 3.2, "value": -3.05, "curve": [3.244, -3.05, 3.289, -2.84]}, {"time": 3.3333, "value": -2.66}]}, "Leg2_r_White2": {"rotate": [{"time": 0.4667, "value": 0.64, "curve": [0.667, 1.09, 0.867, 2]}, {"time": 1.0667, "value": 2, "curve": [1.2, 2, 1.333, -7.94]}, {"time": 1.4667, "value": -7.94, "curve": [1.7, -7.94, 1.933, -2.85]}, {"time": 2.1667, "value": -0.3}]}, "Body_White2": {"rotate": [{"time": 0.4667, "curve": [0.667, 4.03, 0.867, 9.17]}, {"time": 1.0667, "value": 12.09, "curve": [1.2, 14.04, 1.333, 14.63]}, {"time": 1.4667, "value": 14.63, "curve": [1.7, 14.63, 1.933, 4.88]}, {"time": 2.1667}], "translate": [{"y": -0.26, "curve": [0.044, 0, 0.089, 0, 0.044, -0.08, 0.089, 0]}, {"time": 0.1333, "curve": [0.244, 0, 0.356, 0, 0.244, 0, 0.356, 0]}, {"time": 0.4667, "y": -1.58, "curve": [0.667, 0, 0.867, -19.17, 0.667, -4.42, 0.867, -47.51]}, {"time": 1.0667, "x": -19.17, "y": -47.51, "curve": "stepped"}, {"time": 1.4667, "x": -19.17, "y": -47.51, "curve": [1.7, -19.17, 1.933, 0, 1.7, -47.51, 1.933, -1.64]}, {"time": 2.1667, "y": -0.26, "curve": [2.211, 0, 2.256, 0, 2.211, 0, 2.256, 0]}, {"time": 2.3, "curve": [2.489, 0, 2.678, 0, 2.489, 0, 2.678, -2.52]}, {"time": 2.8667, "y": -2.52, "curve": [3.022, 0, 3.178, 0, 3.022, -2.52, 3.178, -0.92]}, {"time": 3.3333, "y": -0.26}]}, "Nane1_White3": {"rotate": [{"value": -0.97, "curve": [0.078, -0.81, 0.156, -0.55]}, {"time": 0.2333, "value": -0.39, "curve": [0.311, -0.23, 0.389, 0]}, {"time": 0.4667, "curve": [0.667, 0, 0.867, -5.49]}, {"time": 1.0667, "value": -7.14, "curve": [1.244, -8.62, 1.422, -9.38]}, {"time": 1.6, "value": -9.38, "curve": [1.789, -9.38, 1.978, -2.37]}, {"time": 2.1667, "value": -0.97, "curve": [2.244, -0.39, 2.322, -0.55]}, {"time": 2.4, "value": -0.39, "curve": [2.478, -0.23, 2.556, 0]}, {"time": 2.6333, "curve": [2.822, 0, 3.011, -1.1]}, {"time": 3.2, "value": -1.1, "curve": [3.244, -1.1, 3.289, -1.05]}, {"time": 3.3333, "value": -0.97}]}, "Leg1_r_White3": {"rotate": [{"time": 0.4667, "value": -3.95, "curve": [0.667, -23.81, 0.867, -59.61]}, {"time": 1.0667, "value": -63.52, "curve": [1.2, -66.14, 1.333, -66.14]}, {"time": 1.4667, "value": -66.14, "curve": [1.7, -66.14, 1.933, -22.72]}, {"time": 2.1667, "value": -1.01}]}, "Nane1_White4": {"rotate": [{"value": -1.1, "curve": [0.078, -1.1, 0.156, -0.87]}, {"time": 0.2333, "value": -0.72, "curve": [0.311, -0.56, 0.389, -0.16]}, {"time": 0.4667, "value": -0.16, "curve": [0.667, -0.16, 0.867, -5.69]}, {"time": 1.0667, "value": -7.28, "curve": [1.244, -8.7, 1.422, -9.2]}, {"time": 1.6, "value": -9.2, "curve": [1.789, -9.2, 1.978, -2.05]}, {"time": 2.1667, "value": -1.1, "curve": [2.244, -0.72, 2.322, -0.86]}, {"time": 2.4, "value": -0.72, "curve": [2.522, -0.49, 2.644, 0]}, {"time": 2.7667, "curve": [2.956, 0, 3.144, -1.1]}, {"time": 3.3333, "value": -1.1}]}, "Body_White4": {"rotate": [{"value": 0.06, "curve": [0.044, 0.02, 0.089, 0]}, {"time": 0.1333, "curve": [0.244, 0, 0.356, 0.35]}, {"time": 0.4667, "value": 0.35, "curve": [0.667, 0.35, 0.867, 0.06]}, {"time": 1.0667, "value": 0.06, "curve": "stepped"}, {"time": 2.1667, "value": 0.06, "curve": [2.211, 0.06, 2.256, 0]}, {"time": 2.3, "curve": [2.489, 0, 2.678, 0.55]}, {"time": 2.8667, "value": 0.55, "curve": [3.022, 0.55, 3.178, 0.2]}, {"time": 3.3333, "value": 0.06}]}, "Nane1_White2": {"rotate": [{"value": -0.72, "curve": [0.078, -0.56, 0.156, -0.27]}, {"time": 0.2333, "value": -0.11, "curve": [0.278, -0.03, 0.322, 0]}, {"time": 0.3667, "curve": [0.4, 0, 0.433, 0]}, {"time": 0.4667, "value": -0.09, "curve": [0.667, -0.65, 0.867, -5.3]}, {"time": 1.0667, "value": -6.9, "curve": [1.244, -8.32, 1.422, -9.14]}, {"time": 1.6, "value": -9.14, "curve": [1.789, -9.14, 1.978, -2.19]}, {"time": 2.1667, "value": -0.72, "curve": [2.244, -0.11, 2.322, -0.27]}, {"time": 2.4, "value": -0.11, "curve": [2.444, -0.03, 2.489, 0]}, {"time": 2.5333, "curve": [2.722, 0, 2.911, -1.1]}, {"time": 3.1, "value": -1.1, "curve": [3.178, -1.1, 3.256, -0.89]}, {"time": 3.3333, "value": -0.72}]}, "Ear_White": {"rotate": [{"curve": [0.156, 0, 0.311, 0.49]}, {"time": 0.4667, "value": 0.49, "curve": [0.589, 0.49, 0.712, -11.36]}, {"time": 0.8333, "value": -14.31, "curve": [0.946, -16.96, 1.056, -8.76]}, {"time": 1.1667, "value": -7.35, "curve": [1.3, -5.65, 1.433, -5.95]}, {"time": 1.5667, "value": -4.97, "curve": [1.767, -3.5, 1.967, -0.56]}, {"time": 2.1667, "curve": [2.367, 0.56, 2.567, 0.56]}, {"time": 2.7667, "value": 0.56, "curve": [2.956, 0.56, 3.144, 0]}, {"time": 3.3333}]}, "Nane2_White4": {"rotate": [{"value": 1.4, "curve": [0.067, 1.25, 0.133, 1.11]}, {"time": 0.2, "value": 0.92, "curve": [0.289, 0.67, 0.378, 0.07]}, {"time": 0.4667, "value": 0.07, "curve": [0.667, 0.07, 0.867, 1.41]}, {"time": 1.0667, "value": 2.37, "curve": [1.233, 3.17, 1.4, 5.35]}, {"time": 1.5667, "value": 5.35, "curve": [1.767, 5.35, 1.967, 2.5]}, {"time": 2.1667, "value": 1.4, "curve": [2.233, 1.03, 2.3, 1.1]}, {"time": 2.3667, "value": 0.92, "curve": [2.478, 0.63, 2.589, 0]}, {"time": 2.7, "curve": [2.9, 0, 3.1, 1.42]}, {"time": 3.3, "value": 1.42, "curve": [3.311, 1.42, 3.322, 1.42]}, {"time": 3.3333, "value": 1.4}]}, "Nane1_White5": {"rotate": [{"value": -0.99, "curve": [0.044, -1.07, 0.089, -1.1]}, {"time": 0.1333, "value": -1.1, "curve": [0.167, -1.1, 0.2, -1.06]}, {"time": 0.2333, "value": -0.99, "curve": [0.311, -0.82, 0.389, -0.39]}, {"time": 0.4667, "value": -0.39, "curve": [0.667, -0.39, 0.867, -6.06]}, {"time": 1.0667, "value": -7.17, "curve": [1.244, -8.15, 1.422, -8.15]}, {"time": 1.6, "value": -8.15, "curve": [1.789, -8.15, 1.978, -0.99]}, {"time": 2.1667, "value": -0.99, "curve": [2.211, -0.99, 2.256, -1.1]}, {"time": 2.3, "value": -1.1, "curve": [2.333, -1.1, 2.367, -1.05]}, {"time": 2.4, "value": -0.99, "curve": [2.556, -0.69, 2.711, 0]}, {"time": 2.8667, "curve": [3.022, 0, 3.178, -0.7]}, {"time": 3.3333, "value": -0.99}]}, "Tail_White7": {"rotate": [{"value": -1.92, "curve": [0.044, -2.18, 0.089, -2.52]}, {"time": 0.1333, "value": -2.73, "curve": [0.167, -2.89, 0.2, -3.05]}, {"time": 0.2333, "value": -3.05, "curve": [0.311, -3.05, 0.389, -2.03]}, {"time": 0.4667, "value": -2.03, "curve": [0.722, -2.03, 0.978, -7.17]}, {"time": 1.2333, "value": -7.17, "curve": [1.433, -7.17, 1.633, -0.91]}, {"time": 1.8333, "value": -0.91, "curve": [1.989, -0.91, 2.144, -2.14]}, {"time": 2.3, "value": -2.73, "curve": [2.333, -2.86, 2.367, -3.05]}, {"time": 2.4, "value": -3.05, "curve": [2.6, -3.05, 2.8, 0]}, {"time": 3, "curve": [3.111, 0, 3.222, -1.27]}, {"time": 3.3333, "value": -1.92}]}, "Tail_White8": {"rotate": [{"value": -1.43, "curve": [0.044, -1.68, 0.089, -2.01]}, {"time": 0.1333, "value": -2.25, "curve": [0.189, -2.55, 0.244, -3.05]}, {"time": 0.3, "value": -3.05, "curve": [0.356, -3.05, 0.411, -2.47]}, {"time": 0.4667, "value": -2.47, "curve": [0.722, -2.47, 0.978, -6.68]}, {"time": 1.2333, "value": -6.68, "curve": [1.433, -6.68, 1.633, -0.42]}, {"time": 1.8333, "value": -0.42, "curve": [1.989, -0.42, 2.144, -1.61]}, {"time": 2.3, "value": -2.25, "curve": [2.356, -2.48, 2.411, -3.05]}, {"time": 2.4667, "value": -3.05, "curve": [2.667, -3.05, 2.867, 0]}, {"time": 3.0667, "curve": [3.156, 0, 3.244, -0.93]}, {"time": 3.3333, "value": -1.43}]}, "Tail_White2": {"rotate": [{"value": -1.33, "curve": [0.044, -1.07, 0.089, -0.77]}, {"time": 0.1333, "value": -0.54, "curve": [0.178, -0.32, 0.222, 0]}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -0.27]}, {"time": 0.4667, "value": -0.81, "curve": [0.667, -2.46, 0.867, -6.57]}, {"time": 1.0667, "value": -6.57, "curve": [1.267, -6.57, 1.467, -3.15]}, {"time": 1.6667, "value": -2.17, "curve": [1.878, -1.14, 2.089, -1.14]}, {"time": 2.3, "value": -0.54, "curve": [2.344, -0.42, 2.389, 0]}, {"time": 2.4333, "curve": [2.633, 0, 2.833, -3.05]}, {"time": 3.0333, "value": -3.05, "curve": [3.133, -3.05, 3.233, -1.9]}, {"time": 3.3333, "value": -1.33}]}, "Leg1_l_White3": {"rotate": [{"curve": [0.156, -1.22, 0.311, 0]}, {"time": 0.4667, "value": -3.66, "curve": [0.667, -8.37, 0.867, -68.12]}, {"time": 1.0667, "value": -74.96, "curve": [1.2, -79.52, 1.333, -79.52]}, {"time": 1.4667, "value": -79.52, "curve": [1.7, -79.52, 1.933, -26.7]}, {"time": 2.1667, "value": -0.29}]}, "Tail_White5": {"rotate": [{"value": -3.05, "curve": [0.044, -3.05, 0.089, -2.98]}, {"time": 0.1333, "value": -2.73, "curve": [0.244, -2.1, 0.356, -0.4]}, {"time": 0.4667, "value": -0.4, "curve": [0.689, -0.4, 0.911, -8.3]}, {"time": 1.1333, "value": -8.3, "curve": [1.333, -8.3, 1.533, -4.79]}, {"time": 1.7333, "value": -3.83, "curve": [1.922, -2.93, 2.111, -3.43]}, {"time": 2.3, "value": -2.73, "curve": [2.456, -2.15, 2.611, 0]}, {"time": 2.7667, "curve": [2.956, 0, 3.144, -3.05]}, {"time": 3.3333, "value": -3.05}]}, "Leg2_r_White3": {"rotate": [{"time": 0.4667, "value": -1.54, "curve": [0.667, -7.33, 0.867, -18.92]}, {"time": 1.0667, "value": -18.92, "curve": [1.2, -18.92, 1.333, -12.73]}, {"time": 1.4667, "value": -10.42, "curve": [1.7, -6.39, 1.933, -3.41]}, {"time": 2.1667, "value": 0.1}]}, "Leg2_l_White2": {"rotate": [{"time": 0.4667, "value": 0.98, "curve": [0.667, 5.73, 0.867, 15.24]}, {"time": 1.0667, "value": 15.24, "curve": [1.2, 15.24, 1.333, 9.67]}, {"time": 1.4667, "value": 7.8, "curve": [1.7, 4.55, 1.933, 2.52]}, {"time": 2.1667, "value": -0.12}]}, "Head_White2": {"rotate": [{"time": 0.9667, "curve": [1.033, 0, 1.1, 11.65]}, {"time": 1.1667, "value": 11.65, "curve": [1.233, 11.65, 1.3, -0.76]}, {"time": 1.3667, "value": -0.76, "curve": [1.6, -0.76, 1.833, -0.76]}, {"time": 2.0667, "curve": [2.122, 0.18, 2.178, 7.46]}, {"time": 2.2333, "value": 7.46, "curve": [2.3, 7.46, 2.367, 0]}, {"time": 2.4333, "curve": [2.489, 0, 2.544, 7.46]}, {"time": 2.6, "value": 7.46, "curve": [2.656, 7.46, 2.711, 0]}, {"time": 2.7667, "curve": [2.822, 0, 2.878, 7.46]}, {"time": 2.9333, "value": 7.46, "curve": [3, 7.46, 3.067, 2.49]}, {"time": 3.1333}]}, "Nane2_White3": {"rotate": [{"value": 1.13, "curve": [0.067, 0.96, 0.133, 0.67]}, {"time": 0.2, "value": 0.5, "curve": [0.278, 0.3, 0.356, 0]}, {"time": 0.4333, "curve": [0.444, 0, 0.456, 0]}, {"time": 0.4667, "value": 0.02, "curve": [0.667, 0.43, 0.867, 1.18]}, {"time": 1.0667, "value": 2.1, "curve": [1.233, 2.87, 1.4, 5.1]}, {"time": 1.5667, "value": 5.1, "curve": [1.767, 5.1, 1.967, 2.28]}, {"time": 2.1667, "value": 1.13, "curve": [2.233, 0.75, 2.3, 0.67]}, {"time": 2.3667, "value": 0.5, "curve": [2.444, 0.3, 2.522, 0]}, {"time": 2.6, "curve": [2.789, 0, 2.978, 1.42]}, {"time": 3.1667, "value": 1.42, "curve": [3.222, 1.42, 3.278, 1.27]}, {"time": 3.3333, "value": 1.13}]}, "Nane2_White": {"rotate": [{"curve": [0.156, 0, 0.311, 1.24]}, {"time": 0.4667, "value": 1.24, "curve": [0.667, 1.24, 0.867, 0.97]}, {"time": 1.0667, "value": 0.97, "curve": [1.233, 0.97, 1.4, 3.78]}, {"time": 1.5667, "value": 3.78, "curve": [1.767, 3.78, 1.967, 0]}, {"time": 2.1667, "curve": [2.367, 0, 2.567, 1.42]}, {"time": 2.7667, "value": 1.42, "curve": [2.956, 1.42, 3.144, 0]}, {"time": 3.3333}]}, "Body_White6": {"rotate": [{"value": 0.06, "curve": [0.044, 0.02, 0.089, 0]}, {"time": 0.1333, "curve": [0.244, 0, 0.356, 0]}, {"time": 0.4667, "value": 0.35, "curve": [0.689, 1.05, 0.911, 3.65]}, {"time": 1.1333, "value": 4.8, "curve": [1.267, 5.5, 1.4, 5.9]}, {"time": 1.5333, "value": 5.9, "curve": [1.744, 5.9, 1.956, 0.33]}, {"time": 2.1667, "value": 0.06, "curve": [2.211, 0, 2.256, 0]}, {"time": 2.3, "curve": [2.489, 0, 2.678, 0.56]}, {"time": 2.8667, "value": 0.56, "curve": [3.022, 0.56, 3.178, 0.2]}, {"time": 3.3333, "value": 0.06}]}, "Leg1_l_White2": {"rotate": [{"curve": [0.156, 0.47, 0.311, 0]}, {"time": 0.4667, "value": 1.4, "curve": [0.667, 3.21, 0.867, 23.88]}, {"time": 1.0667, "value": 23.88, "curve": [1.2, 23.88, 1.333, 23.88]}, {"time": 1.4667, "value": 21.18, "curve": [1.7, 16.46, 1.933, 7.18]}, {"time": 2.1667, "value": 0.18}]}, "Body_White5": {"rotate": [{"curve": [0.156, 0, 0.311, 0]}, {"time": 0.4667, "value": 0.49, "curve": [0.667, 1.11, 0.867, 42.21]}, {"time": 1.0667, "value": 43.86, "curve": [1.2, 44.96, 1.333, 44.96]}, {"time": 1.4667, "value": 44.96, "curve": [1.7, 44.96, 1.933, 21.1]}, {"time": 2.1667, "value": 13.13, "curve": [2.367, 6.3, 2.567, 1.15]}, {"time": 2.7667, "value": 0.56, "curve": [2.956, 0, 3.144, 0]}, {"time": 3.3333}]}}}, "entry": {"bones": {"Body_White": {"translate": [{"x": 757.44, "curve": "stepped"}, {"time": 0.2333, "x": 757.44}, {"time": 0.6}]}, "Body_White2": {"rotate": [{"curve": [0.033, -9.84, 0.067, -23.58]}, {"time": 0.1, "value": -29.51, "curve": [0.144, -37.42, 0.189, -41.51]}, {"time": 0.2333, "value": -41.51, "curve": [0.378, -41.51, 0.522, 25.88]}, {"time": 0.6667, "value": 25.88, "curve": [0.711, 25.88, 0.756, 0]}, {"time": 0.8}], "translatex": [{"curve": [0.033, 18.2, 0.067, 54.59]}, {"time": 0.1, "value": 54.59, "curve": [0.156, 54.59, 0.211, -26.42]}, {"time": 0.2667, "value": -26.42, "curve": [0.4, -26.42, 0.533, -20.76]}, {"time": 0.6667, "value": -14.15, "curve": [0.711, -11.95, 0.756, 0]}, {"time": 0.8}], "translatey": [{"curve": [0.033, 0, 0.067, -28.3]}, {"time": 0.1, "value": -28.3, "curve": [0.144, -28.3, 0.189, 82.64]}, {"time": 0.2333, "value": 126.38, "curve": [0.3, 192, 0.367, 299.8]}, {"time": 0.4333, "value": 299.8, "curve": [0.511, 299.8, 0.589, 67.5]}, {"time": 0.6667, "value": -11.83, "curve": [0.711, -57.16, 0.756, -74.18]}, {"time": 0.8, "value": -74.18, "curve": [0.856, -74.18, 0.911, 0]}, {"time": 0.9667}]}, "Body_White3": {"rotate": [{"curve": [0.044, 0.5, 0.089, 1.49]}, {"time": 0.1333, "value": 1.49, "curve": [0.189, 1.49, 0.244, -0.9]}, {"time": 0.3, "value": -0.9, "curve": [0.422, -0.9, 0.544, 4.46]}, {"time": 0.6667, "value": 4.46, "curve": [0.722, 4.46, 0.778, 0]}, {"time": 0.8333}]}, "Body_White5": {"rotate": [{"time": 0.1333, "curve": [0.167, 8.44, 0.2, 25.31]}, {"time": 0.2333, "value": 25.31, "curve": [0.3, 25.31, 0.367, 24.77]}, {"time": 0.4333, "value": 20.87, "curve": [0.511, 16.33, 0.589, 6.96]}, {"time": 0.6667}]}, "Body_White6": {"rotate": [{"curve": [0.044, 0, 0.089, 17.72]}, {"time": 0.1333, "value": 17.72, "curve": [0.189, 17.72, 0.244, 8.51]}, {"time": 0.3, "value": 4.65, "curve": [0.344, 1.57, 0.386, -2.52]}, {"time": 0.4333, "value": -3.12, "curve": [0.512, -4.07, 0.589, -1.9]}, {"time": 0.6667, "curve": [0.722, 1.36, 0.778, 6.63]}, {"time": 0.8333, "value": 6.63, "curve": [0.878, 6.63, 0.922, 0]}, {"time": 0.9667}]}, "Ear_White": {"rotate": [{"curve": [0.044, 0, 0.089, 7.9]}, {"time": 0.1333, "value": 7.9, "curve": [0.189, 7.9, 0.244, -3.64]}, {"time": 0.3, "value": -8.23, "curve": [0.344, -11.91, 0.386, -16.14]}, {"time": 0.4333, "value": -16.91, "curve": [0.512, -18.12, 0.589, -16.91]}, {"time": 0.6667, "value": -14.13, "curve": [0.722, -12.14, 0.778, 6.63]}, {"time": 0.8333, "value": 6.63, "curve": [0.878, 6.63, 0.922, 0]}, {"time": 0.9667}]}, "Nane2_White2": {"rotate": [{"curve": [0.052, 0, 0.115, 2.42]}, {"time": 0.1667, "value": 2.42, "curve": [0.272, 2.42, 0.362, -11.91]}, {"time": 0.4667, "value": -11.91, "curve": [0.589, -11.91, 0.711, 14.4]}, {"time": 0.8333, "value": 14.4, "curve": [0.881, 14.4, 0.919, 0]}, {"time": 0.9667}]}, "Nane2_White3": {"rotate": [{"curve": [0.052, 0, 0.115, 2.42]}, {"time": 0.1667, "value": 2.42, "curve": [0.272, 2.42, 0.362, -11.91]}, {"time": 0.4667, "value": -11.91, "curve": [0.589, -11.91, 0.711, 14.4]}, {"time": 0.8333, "value": 14.4, "curve": [0.881, 14.4, 0.919, 0]}, {"time": 0.9667}]}, "Nane2_White4": {"rotate": [{"curve": [0.058, 0, 0.142, 2.42]}, {"time": 0.2, "value": 2.42, "curve": [0.305, 2.42, 0.395, -11.91]}, {"time": 0.5, "value": -11.91, "curve": [0.622, -11.91, 0.744, 14.4]}, {"time": 0.8667, "value": 14.4, "curve": [0.905, 14.4, 0.929, 0]}, {"time": 0.9667}]}, "Nane2_White5": {"rotate": [{"curve": [0.058, 0, 0.142, 2.42]}, {"time": 0.2, "value": 2.42, "curve": [0.305, 2.42, 0.395, -12.11]}, {"time": 0.5, "value": -12.11, "curve": [0.622, -12.11, 0.744, 14.4]}, {"time": 0.8667, "value": 14.4, "curve": [0.905, 14.4, 0.929, 0]}, {"time": 0.9667}]}, "Nane1_White": {"rotate": [{"curve": [0.044, 0.93, 0.089, 2.6]}, {"time": 0.1333, "value": 3.35, "curve": [0.222, 4.83, 0.311, 6.7]}, {"time": 0.4, "value": 6.7, "curve": [0.511, 6.7, 0.622, -4.34]}, {"time": 0.7333, "value": -4.34, "curve": [0.811, -4.34, 0.889, -1.63]}, {"time": 0.9667}]}, "Nane1_White2": {"rotate": [{"curve": [0.044, 0.93, 0.089, 2.6]}, {"time": 0.1333, "value": 3.35, "curve": [0.222, 4.83, 0.311, 6.7]}, {"time": 0.4, "value": 6.7, "curve": [0.511, 6.7, 0.622, -4.34]}, {"time": 0.7333, "value": -4.34, "curve": [0.811, -4.34, 0.889, -1.63]}, {"time": 0.9667}]}, "Nane1_White3": {"rotate": [{"curve": [0.044, 0.93, 0.089, 2.6]}, {"time": 0.1333, "value": 3.35, "curve": [0.222, 4.83, 0.311, 6.7]}, {"time": 0.4, "value": 6.7, "curve": [0.511, 6.7, 0.622, -4.34]}, {"time": 0.7333, "value": -4.34, "curve": [0.811, -4.34, 0.889, -1.63]}, {"time": 0.9667}]}, "Nane1_White4": {"rotate": [{"curve": [0.044, 0.93, 0.089, 2.6]}, {"time": 0.1333, "value": 3.35, "curve": [0.222, 4.83, 0.311, 6.7]}, {"time": 0.4, "value": 6.7, "curve": [0.511, 6.7, 0.622, -4.34]}, {"time": 0.7333, "value": -4.34, "curve": [0.811, -4.34, 0.889, -1.63]}, {"time": 0.9667}]}, "Nane1_White5": {"rotate": [{"curve": [0.044, 0.93, 0.089, 2.6]}, {"time": 0.1333, "value": 3.35, "curve": [0.222, 4.83, 0.311, 6.7]}, {"time": 0.4, "value": 6.7, "curve": [0.511, 6.7, 0.622, -4.34]}, {"time": 0.7333, "value": -4.34, "curve": [0.811, -4.34, 0.889, -1.63]}, {"time": 0.9667}]}, "Nane1_White6": {"rotate": [{"curve": [0.044, 0.93, 0.089, 2.6]}, {"time": 0.1333, "value": 3.35, "curve": [0.222, 4.83, 0.311, 6.7]}, {"time": 0.4, "value": 6.7, "curve": [0.511, 6.7, 0.622, -4.34]}, {"time": 0.7333, "value": -4.34, "curve": [0.811, -4.34, 0.889, -1.63]}, {"time": 0.9667}]}, "Leg2_r_White": {"rotate": [{"curve": [0.044, 3.98, 0.089, 6.76]}, {"time": 0.1333, "value": 11.95, "curve": [0.167, 15.84, 0.2, 27.24]}, {"time": 0.2333, "value": 27.24, "curve": [0.378, 27.24, 0.522, 18.51]}, {"time": 0.6667, "value": 11.95, "curve": [0.722, 9.43, 0.778, 0]}, {"time": 0.8333}]}, "Leg2_r_White2": {"rotate": [{"curve": [0.044, 33.87, 0.089, 101.6]}, {"time": 0.1333, "value": 101.6, "curve": [0.311, 101.6, 0.489, 40.28]}, {"time": 0.6667, "value": 14.48, "curve": [0.722, 6.41, 0.778, 0]}, {"time": 0.8333}]}, "Leg2_r_White3": {"rotate": [{"curve": [0.044, -23.14, 0.089, -69.43]}, {"time": 0.1333, "value": -69.43, "curve": [0.311, -69.43, 0.489, -61.04]}, {"time": 0.6667, "value": -43.41, "curve": [0.722, -37.9, 0.778, 0]}, {"time": 0.8333}]}, "Leg2_r_White4": {"rotate": [{"time": 0.2, "curve": [0.26, 0, 0.311, 82.9]}, {"time": 0.3667, "value": 82.9, "curve": [0.444, 82.9, 0.522, 77.33]}, {"time": 0.6, "value": 59.75, "curve": [0.644, 49.7, 0.689, 0]}, {"time": 0.7333}], "translate": [{"time": 0.2, "curve": [0.26, 0, 0.311, 137.83, 0.26, 0, 0.311, 421.34]}, {"time": 0.3667, "x": 137.83, "y": 421.34, "curve": [0.444, 137.83, 0.522, 124.16, 0.444, 421.34, 0.522, 317.21]}, {"time": 0.6, "x": 94.93, "y": 227.84, "curve": [0.644, 78.22, 0.689, 0, 0.644, 176.77, 0.689, 0]}, {"time": 0.7333}]}, "Leg2_l_White": {"rotate": [{"curve": [0.044, 6.52, 0.089, 13.22]}, {"time": 0.1333, "value": 19.55, "curve": [0.167, 24.29, 0.2, 33.23]}, {"time": 0.2333, "value": 33.23, "curve": [0.378, 33.23, 0.522, 27.55]}, {"time": 0.6667, "value": 19.55, "curve": [0.722, 16.47, 0.778, 0]}, {"time": 0.8333}]}, "Leg2_l_White2": {"rotate": [{"curve": [0.044, 29.24, 0.089, 87.71]}, {"time": 0.1333, "value": 87.71, "curve": [0.311, 87.71, 0.489, 34.02]}, {"time": 0.6667, "value": 11.74, "curve": [0.722, 4.78, 0.778, 0]}, {"time": 0.8333}]}, "Leg2_l_White3": {"rotate": [{"curve": [0.044, -24.97, 0.089, -74.9]}, {"time": 0.1333, "value": -74.9, "curve": [0.311, -74.9, 0.489, -73.44]}, {"time": 0.6667, "value": -54.42, "curve": [0.722, -48.48, 0.778, 0]}, {"time": 0.8333}]}, "Leg2_l_White4": {"rotate": [{"time": 0.2333, "curve": [0.294, 0, 0.344, 69.14]}, {"time": 0.4, "value": 69.14, "curve": [0.478, 69.14, 0.556, 47.07]}, {"time": 0.6333, "value": 32.41, "curve": [0.678, 24.03, 0.722, 0]}, {"time": 0.7667}], "translate": [{"time": 0.2333, "curve": [0.294, 0, 0.344, 175.02, 0.294, 0, 0.344, 410.1]}, {"time": 0.4, "x": 175.02, "y": 410.1, "curve": [0.478, 175.02, 0.556, 132.05, 0.478, 410.1, 0.556, 263.97]}, {"time": 0.6333, "x": 94.93, "y": 176.98, "curve": [0.678, 73.71, 0.722, 0, 0.678, 127.27, 0.722, 0]}, {"time": 0.7667}]}, "Leg1_r_White": {"rotate": [{"curve": [0.044, -0.71, 0.089, 0]}, {"time": 0.1333, "value": -2.12, "curve": [0.233, -6.88, 0.333, -26.17]}, {"time": 0.4333, "value": -26.17, "curve": [0.522, -26.17, 0.611, 6.23]}, {"time": 0.7, "value": 6.23, "curve": [0.767, 6.23, 0.833, 0]}, {"time": 0.9}], "translate": [{"time": 0.1333, "curve": [0.267, 0, 0.422, 15.1, 0.267, 0, 0.422, 41.12]}, {"time": 0.5667, "x": 15.1, "y": 41.12, "curve": [0.611, 15.1, 0.656, 0, 0.611, 41.12, 0.656, 0]}, {"time": 0.7}]}, "Leg1_r_White2": {"rotate": [{"curve": [0.044, 12.31, 0.089, 36.94]}, {"time": 0.1333, "value": 36.94, "curve": [0.311, 36.94, 0.489, 22.5]}, {"time": 0.6667, "value": 13.12, "curve": [0.722, 10.19, 0.778, 0]}, {"time": 0.8333}]}, "Leg1_r_White3": {"rotate": [{"curve": [0.044, -25.95, 0.089, -77.85]}, {"time": 0.1333, "value": -77.85, "curve": [0.311, -77.85, 0.489, -74.61]}, {"time": 0.6667, "value": -54.84, "curve": [0.722, -48.66, 0.778, 0]}, {"time": 0.8333}], "translate": [{"time": 0.4667}, {"time": 0.5667, "x": 32.15, "y": -1.19}, {"time": 0.6667}]}, "Leg1_r_White4": {"rotate": [{"curve": [0.033, -1.27, 0.067, 0]}, {"time": 0.1, "value": -3.82, "curve": [0.144, -8.91, 0.189, -53.9]}, {"time": 0.2333, "value": -53.9, "curve": [0.3, -53.9, 0.367, -33.7]}, {"time": 0.4333, "value": -23.9, "curve": [0.489, -15.73, 0.544, 0]}, {"time": 0.6}], "translate": [{"curve": [0.033, -43.06, 0.067, -95.61, 0.033, 75.23, 0.067, 168.2]}, {"time": 0.1, "x": -129.18, "y": 225.69, "curve": [0.144, -173.95, 0.189, -235.02, 0.144, 302.36, 0.189, 376.12]}, {"time": 0.2333, "x": -235.02, "y": 402.48, "curve": [0.3, -235.02, 0.367, -211.85, 0.3, 442.02, 0.367, 442.02]}, {"time": 0.4333, "x": -169.12, "y": 442.02, "curve": [0.489, -133.51, 0.544, 0, 0.489, 442.02, 0.544, 0]}, {"time": 0.6}]}, "Leg1_l_White": {"rotate": [{"curve": [0.044, -0.71, 0.089, 0]}, {"time": 0.1333, "value": -2.12, "curve": [0.233, -6.88, 0.333, -23.51]}, {"time": 0.4333, "value": -23.51, "curve": [0.511, -23.51, 0.589, -10.92]}, {"time": 0.6667, "value": -6.35, "curve": [0.722, -3.09, 0.778, 0]}, {"time": 0.8333}]}, "Leg1_l_White2": {"rotate": [{"curve": [0.044, -0.41, 0.089, -1.23]}, {"time": 0.1333, "value": -1.23, "curve": [0.311, -1.23, 0.489, 18.5]}, {"time": 0.6667, "value": 18.5, "curve": [0.722, 18.5, 0.778, 0]}, {"time": 0.8333}]}, "Leg1_l_White3": {"rotate": [{"curve": [0.044, -23.43, 0.089, -68.92]}, {"time": 0.1333, "value": -70.3, "curve": [0.311, -75.81, 0.489, -75.81]}, {"time": 0.6667, "value": -75.81, "curve": [0.722, -75.81, 0.778, 0]}, {"time": 0.8333}]}, "Leg1_l_White4": {"rotate": [{"curve": [0.033, -15.27, 0.067, -33.37]}, {"time": 0.1, "value": -45.8, "curve": [0.144, -62.39, 0.189, -87.06]}, {"time": 0.2333, "value": -87.06, "curve": [0.3, -87.06, 0.367, -48.03]}, {"time": 0.4333, "value": -33.52, "curve": [0.5, -19.01, 0.567, -11.17]}, {"time": 0.6333}], "translate": [{"curve": [0.033, -21.57, 0.067, -47.82, 0.033, 80.78, 0.067, 175.92]}, {"time": 0.1, "x": -64.72, "y": 242.33, "curve": [0.144, -87.25, 0.189, -107.26, 0.144, 330.88, 0.189, 464.89]}, {"time": 0.2333, "x": -118.29, "y": 464.89, "curve": [0.3, -134.84, 0.367, -147.47, 0.3, 464.89, 0.367, 464.89]}, {"time": 0.4333, "x": -147.47, "y": 458.99, "curve": [0.5, -147.47, 0.567, -49.15, 0.5, 453.1, 0.567, 153]}, {"time": 0.6333}]}, "Tail_White": {"rotate": [{"curve": [0.044, 0, 0.089, -4.57]}, {"time": 0.1333, "value": -4.57, "curve": [0.222, -4.57, 0.311, 16.2]}, {"time": 0.4, "value": 16.2, "curve": [0.489, 16.2, 0.578, 14.08]}, {"time": 0.6667, "value": 9.72, "curve": [0.722, 7, 0.778, -5.05]}, {"time": 0.8333, "value": -5.05, "curve": [0.878, -5.05, 0.922, 0]}, {"time": 0.9667}]}, "Tail_White2": {"rotate": [{"curve": [0.044, 0, 0.089, -4.57]}, {"time": 0.1333, "value": -4.57, "curve": [0.222, -4.57, 0.311, 16.2]}, {"time": 0.4, "value": 16.2, "curve": [0.489, 16.2, 0.578, 14.08]}, {"time": 0.6667, "value": 9.72, "curve": [0.722, 7, 0.778, -5.05]}, {"time": 0.8333, "value": -5.05, "curve": [0.878, -5.05, 0.922, 0]}, {"time": 0.9667}]}, "Tail_White3": {"rotate": [{"curve": [0.056, 0, 0.111, -4.57]}, {"time": 0.1667, "value": -4.57, "curve": [0.256, -4.57, 0.344, 16.2]}, {"time": 0.4333, "value": 16.2, "curve": [0.522, 16.2, 0.611, 14.08]}, {"time": 0.7, "value": 9.72, "curve": [0.756, 7, 0.811, -5.05]}, {"time": 0.8667, "value": -5.05, "curve": [0.9, -5.05, 0.933, 0]}, {"time": 0.9667}]}, "Tail_White4": {"rotate": [{"curve": [0.056, 0, 0.111, -4.57]}, {"time": 0.1667, "value": -4.57, "curve": [0.256, -4.57, 0.344, 16.2]}, {"time": 0.4333, "value": 16.2, "curve": [0.522, 16.2, 0.611, 14.08]}, {"time": 0.7, "value": 9.72, "curve": [0.756, 7, 0.811, -5.05]}, {"time": 0.8667, "value": -5.05, "curve": [0.9, -5.05, 0.933, 0]}, {"time": 0.9667}]}, "Tail_White5": {"rotate": [{"curve": [0.067, 0, 0.133, -4.57]}, {"time": 0.2, "value": -4.57, "curve": [0.289, -4.57, 0.378, 16.2]}, {"time": 0.4667, "value": 16.2, "curve": [0.556, 16.2, 0.644, 14.08]}, {"time": 0.7333, "value": 9.72, "curve": [0.789, 7, 0.844, -5.05]}, {"time": 0.9, "value": -5.05, "curve": [0.922, -5.05, 0.944, 0]}, {"time": 0.9667}]}, "Tail_White6": {"rotate": [{"curve": [0.067, 0, 0.133, -4.57]}, {"time": 0.2, "value": -4.57, "curve": [0.289, -4.57, 0.378, 16.2]}, {"time": 0.4667, "value": 16.2, "curve": [0.556, 16.2, 0.644, 14.08]}, {"time": 0.7333, "value": 9.72, "curve": [0.789, 7, 0.844, -5.05]}, {"time": 0.9, "value": -5.05, "curve": [0.922, -5.05, 0.944, 0]}, {"time": 0.9667}]}, "Tail_White7": {"rotate": [{"curve": [0.067, 0, 0.133, -4.57]}, {"time": 0.2, "value": -4.57, "curve": [0.289, -4.57, 0.378, 16.2]}, {"time": 0.4667, "value": 16.2, "curve": [0.556, 16.2, 0.644, 14.08]}, {"time": 0.7333, "value": 9.72, "curve": [0.789, 7, 0.844, -5.05]}, {"time": 0.9, "value": -5.05, "curve": [0.922, -5.05, 0.944, 0]}, {"time": 0.9667}]}, "Tail_White8": {"rotate": [{"curve": [0.067, 0, 0.133, -4.57]}, {"time": 0.2, "value": -4.57, "curve": [0.289, -4.57, 0.378, 16.2]}, {"time": 0.4667, "value": 16.2, "curve": [0.556, 16.2, 0.644, 14.08]}, {"time": 0.7333, "value": 9.72, "curve": [0.789, 7, 0.844, -5.05]}, {"time": 0.9, "value": -5.05, "curve": [0.922, -5.05, 0.944, 0]}, {"time": 0.9667}]}, "Nane2_White": {"rotate": [{"curve": [0.044, 0, 0.089, 6.67]}, {"time": 0.1333, "value": 6.67, "curve": [0.238, 6.67, 0.328, -5.73]}, {"time": 0.4333, "value": -5.73, "curve": [0.556, -5.73, 0.678, 2.58]}, {"time": 0.8, "value": 2.58, "curve": [0.856, 2.58, 0.911, 0]}, {"time": 0.9667}]}}}, "exit": {"slots": {"body_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "ear_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "eye1_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "head_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "leg1_l_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "leg1_r_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "leg2_l_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "leg2_r_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "nane1_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "nane2_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "tail_white": {"rgba": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}}, "bones": {"Body_White2": {"translatey": [{"value": -0.26, "curve": [0.044, -0.17, 0.095, 0]}, {"time": 0.1333, "curve": [0.316, 0, 0.489, -138.84]}, {"time": 0.6667, "value": -145.45, "curve": [0.744, -148.34, 0.829, -148.34]}, {"time": 0.9, "value": -148.34, "curve": [0.979, -148.34, 1.056, -146.41]}, {"time": 1.1333, "value": -145.45}]}, "Ear_White": {"rotate": [{"curve": [0.185, 0, 0.444, -2.39]}, {"time": 0.6667, "value": -2.39, "curve": [0.822, -2.39, 0.978, -1.62]}, {"time": 1.1333, "value": -1.24}], "translate": [{}, {"time": 0.6667, "x": -134.89, "y": 27.03}]}, "Leg1_l_White": {"translate": [{}, {"time": 0.6667, "x": -30.82, "y": -13.47}]}, "Leg1_r_White": {"rotate": [{}, {"time": 0.6667, "value": 36.04}], "translate": [{}, {"time": 0.6667, "x": 0.01, "y": 26.4}]}, "Nane1_White": {"rotate": [{"value": -0.39, "curve": [0.078, -0.26, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -5.33]}, {"time": 0.6667, "value": -5.33}]}, "Nane2_White": {"rotate": [{"curve": [0.185, 0, 0.444, 5.09]}, {"time": 0.6667, "value": 5.09}]}, "Tail_White": {"rotate": [{"curve": [0.185, 0, 0.444, 12.68]}, {"time": 0.6667, "value": 12.68}]}, "Nane2_White2": {"rotate": [{"value": 0.79, "curve": [0.067, 0.58, 0.133, 0.36]}, {"time": 0.2, "value": 0.15, "curve": [0.239, 0.05, 0.262, 0]}, {"time": 0.3, "curve": [0.422, 0, 0.544, 3.67]}, {"time": 0.6667, "value": 3.67}]}, "Nane2_White3": {"rotate": [{"value": 1.13, "curve": [0.067, 0.92, 0.133, 0.71]}, {"time": 0.2, "value": 0.5, "curve": [0.273, 0.32, 0.584, 3.67]}, {"time": 0.6667, "value": 3.67}]}, "Nane2_White4": {"rotate": [{"value": 1.4, "curve": [0.067, 1.24, 0.133, 1.08]}, {"time": 0.2, "value": 0.92, "curve": [0.309, 0.66, 0.615, 3.67]}, {"time": 0.6667, "value": 3.67}]}, "Nane2_White5": {"rotate": [{"value": 1.35, "curve": [0.027, 1.39, 0.044, 1.42]}, {"time": 0.0667, "value": 1.42, "curve": [0.111, 1.42, 0.156, 1.32]}, {"time": 0.2, "value": 1.27, "curve": [0.345, 0.92, 0.656, 3.67]}, {"time": 0.6667, "value": 3.67}]}, "Body_White6": {"rotate": [{"value": 0.06, "curve": [0.044, 0.04, 0.095, 0]}, {"time": 0.1333, "curve": [0.316, 0, 0.489, 9.6]}, {"time": 0.6667, "value": 10.92, "curve": [0.822, 12.07, 0.978, 11.69]}, {"time": 1.1333, "value": 12.07}]}, "Nane1_White2": {"rotate": [{"value": -0.72, "curve": [0.078, -0.52, 0.156, -0.32]}, {"time": 0.2333, "value": -0.11, "curve": [0.272, -0.04, 0.328, 0]}, {"time": 0.3667, "curve": [0.467, 0, 0.567, -5.33]}, {"time": 0.6667, "value": -5.33}]}, "Nane1_White3": {"rotate": [{"value": -0.97, "curve": [0.078, -0.77, 0.156, -0.58]}, {"time": 0.2333, "value": -0.39, "curve": [0.306, -0.25, 0.594, -5.33]}, {"time": 0.6667, "value": -5.33}]}, "Nane1_White4": {"rotate": [{"value": -1.1, "curve": [0.078, -0.97, 0.156, -0.85]}, {"time": 0.2333, "value": -0.72, "curve": [0.342, -0.51, 0.636, -5.33]}, {"time": 0.6667, "value": -5.33}]}, "Nane1_White5": {"rotate": [{"value": -0.99, "curve": [0.04, -1.06, 0.089, -1.1]}, {"time": 0.1333, "value": -1.1, "curve": [0.167, -1.1, 0.2, -1.03]}, {"time": 0.2333, "value": -0.99, "curve": [0.378, -0.72, 0.523, -5.33]}, {"time": 0.6667, "value": -5.33}]}, "Body_White3": {"rotate": [{"value": 0.19, "curve": [0.078, 0.13, 0.156, 0]}, {"time": 0.2333, "curve": [0.378, 0, 0.522, -8.56]}, {"time": 0.6667, "value": -8.56}]}, "Body_White5": {"rotate": [{"curve": [0.185, 0, 0.444, 10.25]}, {"time": 0.6667, "value": 11.9, "curve": [0.822, 13.05, 0.978, 12.67]}, {"time": 1.1333, "value": 13.05}]}, "Nane1_White6": {"rotate": [{"value": -0.72, "curve": [0.079, -0.93, 0.156, -0.72]}, {"time": 0.2333, "value": -1.1, "curve": [0.378, -1.83, 0.522, -6.44]}, {"time": 0.6667, "value": -6.44}]}, "Body_White4": {"rotate": [{"value": 0.06, "curve": [0.044, 0.04, 0.095, 0]}, {"time": 0.1333, "curve": [0.316, 0, 0.489, 1.38]}, {"time": 0.6667, "value": 1.38}]}, "Leg1_l_White2": {"rotate": [{"value": 0.18}, {"time": 0.6667, "value": 15.36}]}, "Tail_White2": {"rotate": [{"value": -1.33, "curve": [0.044, -1.07, 0.089, -0.81]}, {"time": 0.1333, "value": -0.54, "curve": [0.185, -0.27, 0.215, 0]}, {"time": 0.2667, "curve": [0.4, 0, 0.533, -12.02]}, {"time": 0.6667, "value": -12.02}]}, "Leg2_l_White3": {"rotate": [{"value": -0.2}, {"time": 0.6667, "value": -112.5}]}, "Tail_White3": {"rotate": [{"value": -1.86, "curve": [0.044, -1.6, 0.089, -1.33]}, {"time": 0.1333, "value": -1.07, "curve": [0.211, -0.67, 0.29, 0]}, {"time": 0.3667, "curve": [0.467, 0, 0.567, -12.02]}, {"time": 0.6667, "value": -12.02}]}, "Tail_White4": {"rotate": [{"value": -2.66, "curve": [0.044, -2.43, 0.089, -2.21]}, {"time": 0.1333, "value": -1.98, "curve": [0.243, -1.4, 0.593, 0.25]}, {"time": 0.6667, "value": 0.25}]}, "Tail_White5": {"rotate": [{"value": -3.05, "curve": [0.044, -2.94, 0.089, -2.84]}, {"time": 0.1333, "value": -2.73, "curve": [0.28, -1.97, 0.635, 27.72]}, {"time": 0.6667, "value": 27.72}]}, "Tail_White6": {"rotate": [{"value": -2.73, "curve": [0.04, -2.92, 0.089, -2.94]}, {"time": 0.1333, "value": -3.05, "curve": [0.316, -3.05, 0.484, 27.72]}, {"time": 0.6667, "value": 27.72}]}, "Tail_White7": {"rotate": [{"value": -1.92, "curve": [0.039, -2.22, 0.089, -2.46]}, {"time": 0.1333, "value": -2.73, "curve": [0.174, -2.92, 0.2, -2.73]}, {"time": 0.2333, "value": -3.05, "curve": [0.378, -4.42, 0.522, -13.97]}, {"time": 0.6667, "value": -13.97}]}, "Tail_White8": {"rotate": [{"value": -1.43, "curve": [0.039, -1.73, 0.089, -1.98]}, {"time": 0.1333, "value": -2.25, "curve": [0.199, -2.7, 0.244, -2.25]}, {"time": 0.3, "value": -3.05, "curve": [0.422, -4.8, 0.544, -13.97]}, {"time": 0.6667, "value": -13.97}]}, "Leg1_l_White3": {"rotate": [{"value": -0.29}, {"time": 0.6667, "value": -100.71}]}, "Leg2_r_White4": {"rotate": [{}, {"time": 0.6667, "value": -31.57}], "translatex": [{}, {"time": 0.6667, "value": -79.63}]}, "Leg2_l_White4": {"translatex": [{}, {"time": 0.6667, "value": -44.92}]}, "Leg1_r_White4": {"rotate": [{}, {"time": 0.6667, "value": -14.27}]}, "Leg1_l_White4": {"rotate": [{}, {"time": 0.6667, "value": -5.74}]}, "Leg2_r_White2": {"rotate": [{"value": -0.3}, {"time": 0.6667, "value": 32.82}]}, "Leg2_r_White3": {"rotate": [{"value": 0.1}, {"time": 0.6667, "value": -106.71}]}, "Leg2_l_White2": {"rotate": [{"value": -0.12}, {"time": 0.6667, "value": 70.9}]}, "Leg1_r_White2": {"rotate": [{"value": 0.58}, {"time": 0.6667, "value": 0.5}]}, "Leg1_r_White3": {"rotate": [{"value": -1.01}, {"time": 0.6667, "value": -119.65}]}}}, "idle": {"bones": {"Eye2_White": {"scale": [{"time": 0.8333, "curve": [0.9, 0.73, 0.967, 0.461, 0.9, 1, 0.967, 1]}, {"time": 1.0333, "x": 0.191, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "x": 0.091, "curve": [3.044, 0.394, 3.089, 0.697, 3.044, 1, 3.089, 1]}, {"time": 3.1333}]}, "Body_White2": {"translate": [{"y": -0.26, "curve": [0.044, 0, 0.089, 0, 0.044, -0.08, 0.089, 0]}, {"time": 0.1333, "curve": [0.244, 0, 0.356, 0, 0.244, 0, 0.356, -1.58]}, {"time": 0.4667, "y": -1.58, "curve": [1.033, 0, 1.6, 0, 1.033, -1.58, 1.6, -0.75]}, {"time": 2.1667, "y": -0.26, "curve": [2.211, 0, 2.256, 0, 2.211, -0.22, 2.256, 0]}, {"time": 2.3, "curve": [2.489, 0, 2.678, 0, 2.489, 0, 2.678, -2.52]}, {"time": 2.8667, "y": -2.52, "curve": [3.022, 0, 3.178, 0, 3.022, -2.52, 3.178, -0.92]}, {"time": 3.3333, "y": -0.26}]}, "Ear_White": {"rotate": [{"curve": [0.156, 0, 0.311, 0.49]}, {"time": 0.4667, "value": 0.49, "curve": [0.589, 0.49, 0.712, -11.36]}, {"time": 0.8333, "value": -14.31, "curve": [0.946, -16.96, 1.056, -8.76]}, {"time": 1.1667, "value": -7.35, "curve": [1.3, -5.65, 1.433, -5.95]}, {"time": 1.5667, "value": -4.97, "curve": [1.767, -3.5, 1.967, -0.56]}, {"time": 2.1667, "curve": [2.367, 0.56, 2.567, 0.56]}, {"time": 2.7667, "value": 0.56, "curve": [2.956, 0.56, 3.144, 0]}, {"time": 3.3333}]}, "Body_White3": {"rotate": [{"value": 0.19, "curve": [0.078, 0.12, 0.156, 0]}, {"time": 0.2333, "curve": [0.311, 0, 0.389, 0.07]}, {"time": 0.4667, "value": 0.19, "curve": [0.8, 0.71, 1.133, 1.91]}, {"time": 1.4667, "value": 1.91, "curve": [1.7, 1.91, 1.933, 0.67]}, {"time": 2.1667, "value": 0.19, "curve": [2.244, 0.04, 2.322, 0]}, {"time": 2.4, "curve": [2.6, 0, 2.8, 0.55]}, {"time": 3, "value": 0.55, "curve": [3.111, 0.55, 3.222, 0.3]}, {"time": 3.3333, "value": 0.19}]}, "Body_White4": {"rotate": [{"value": 0.06, "curve": [0.044, 0.02, 0.089, 0]}, {"time": 0.1333, "curve": [0.244, 0, 0.356, 0.35]}, {"time": 0.4667, "value": 0.35, "curve": [0.667, 0.35, 0.867, 0.06]}, {"time": 1.0667, "value": 0.06, "curve": "stepped"}, {"time": 2.1667, "value": 0.06, "curve": [2.211, 0.06, 2.256, 0]}, {"time": 2.3, "curve": [2.489, 0, 2.678, 0.55]}, {"time": 2.8667, "value": 0.55, "curve": [3.022, 0.55, 3.178, 0.2]}, {"time": 3.3333, "value": 0.06}]}, "Body_White5": {"rotate": [{"curve": [0.156, 0, 0.311, 0.47]}, {"time": 0.4667, "value": 0.49, "curve": [1.233, 0.56, 2, 0.56]}, {"time": 2.7667, "value": 0.56, "curve": [2.956, 0.56, 3.144, 0]}, {"time": 3.3333}]}, "Body_White6": {"rotate": [{"value": 0.06, "curve": [0.044, 0.02, 0.089, 0]}, {"time": 0.1333, "curve": [0.244, 0, 0.356, 0]}, {"time": 0.4667, "value": 0.35, "curve": [0.689, 1.05, 0.911, 3.65]}, {"time": 1.1333, "value": 4.8, "curve": [1.267, 5.5, 1.4, 5.9]}, {"time": 1.5333, "value": 5.9, "curve": [1.744, 5.9, 1.956, 0.33]}, {"time": 2.1667, "value": 0.06, "curve": [2.211, 0, 2.256, 0]}, {"time": 2.3, "curve": [2.489, 0, 2.678, 0.56]}, {"time": 2.8667, "value": 0.56, "curve": [3.022, 0.56, 3.178, 0.2]}, {"time": 3.3333, "value": 0.06}]}, "Nane2_White": {"rotate": [{"curve": [0.156, 0, 0.311, 1.24]}, {"time": 0.4667, "value": 1.24, "curve": [0.667, 1.24, 0.867, 0.97]}, {"time": 1.0667, "value": 0.97, "curve": [1.233, 0.97, 1.4, 3.78]}, {"time": 1.5667, "value": 3.78, "curve": [1.767, 3.78, 1.967, 0]}, {"time": 2.1667, "curve": [2.367, 0, 2.567, 1.42]}, {"time": 2.7667, "value": 1.42, "curve": [2.956, 1.42, 3.144, 0]}, {"time": 3.3333}]}, "Nane2_White4": {"rotate": [{"value": 1.4, "curve": [0.067, 1.25, 0.133, 1.11]}, {"time": 0.2, "value": 0.92, "curve": [0.289, 0.67, 0.378, 0.07]}, {"time": 0.4667, "value": 0.07, "curve": [0.667, 0.07, 0.867, 1.41]}, {"time": 1.0667, "value": 2.37, "curve": [1.233, 3.17, 1.4, 5.35]}, {"time": 1.5667, "value": 5.35, "curve": [1.767, 5.35, 1.967, 2.5]}, {"time": 2.1667, "value": 1.4, "curve": [2.233, 1.03, 2.3, 1.1]}, {"time": 2.3667, "value": 0.92, "curve": [2.478, 0.63, 2.589, 0]}, {"time": 2.7, "curve": [2.9, 0, 3.1, 1.42]}, {"time": 3.3, "value": 1.42, "curve": [3.311, 1.42, 3.322, 1.42]}, {"time": 3.3333, "value": 1.4}]}, "Nane2_White5": {"rotate": [{"value": 1.35, "curve": [0.022, 1.4, 0.044, 1.42]}, {"time": 0.0667, "value": 1.42, "curve": [0.111, 1.42, 0.156, 1.39]}, {"time": 0.2, "value": 1.27, "curve": [0.289, 1.04, 0.378, 0.38]}, {"time": 0.4667, "value": 0.38, "curve": [0.667, 0.38, 0.867, 1.49]}, {"time": 1.0667, "value": 2.32, "curve": [1.233, 3.01, 1.4, 4.92]}, {"time": 1.5667, "value": 4.92, "curve": [1.767, 4.92, 1.967, 1.35]}, {"time": 2.1667, "value": 1.35, "curve": [2.189, 1.35, 2.211, 1.42]}, {"time": 2.2333, "value": 1.42, "curve": [2.278, 1.42, 2.322, 1.38]}, {"time": 2.3667, "value": 1.27, "curve": [2.522, 0.91, 2.678, 0]}, {"time": 2.8333, "curve": [3, 0, 3.167, 0.93]}, {"time": 3.3333, "value": 1.35}]}, "Nane1_White": {"rotate": [{"value": -0.39, "curve": [0.078, -0.24, 0.156, 0]}, {"time": 0.2333, "curve": [0.311, 0, 0.389, 0]}, {"time": 0.4667, "value": -0.38, "curve": [0.667, -1.35, 0.867, -5.14]}, {"time": 1.0667, "value": -6.57, "curve": [1.244, -7.84, 1.422, -8.48]}, {"time": 1.6, "value": -8.48, "curve": [1.789, -8.48, 1.978, -1.33]}, {"time": 2.1667, "value": -0.39, "curve": [2.244, 0, 2.322, 0]}, {"time": 2.4, "curve": [2.6, 0, 2.8, -1.1]}, {"time": 3, "value": -1.1, "curve": [3.111, -1.1, 3.222, -0.61]}, {"time": 3.3333, "value": -0.39}]}, "Nane1_White2": {"rotate": [{"value": -0.72, "curve": [0.078, -0.56, 0.156, -0.27]}, {"time": 0.2333, "value": -0.11, "curve": [0.278, -0.03, 0.322, 0]}, {"time": 0.3667, "curve": [0.4, 0, 0.433, 0]}, {"time": 0.4667, "value": -0.09, "curve": [0.667, -0.65, 0.867, -5.3]}, {"time": 1.0667, "value": -6.9, "curve": [1.244, -8.32, 1.422, -9.14]}, {"time": 1.6, "value": -9.14, "curve": [1.789, -9.14, 1.978, -2.19]}, {"time": 2.1667, "value": -0.72, "curve": [2.244, -0.11, 2.322, -0.27]}, {"time": 2.4, "value": -0.11, "curve": [2.444, -0.03, 2.489, 0]}, {"time": 2.5333, "curve": [2.722, 0, 2.911, -1.1]}, {"time": 3.1, "value": -1.1, "curve": [3.178, -1.1, 3.256, -0.89]}, {"time": 3.3333, "value": -0.72}]}, "Nane1_White3": {"rotate": [{"value": -0.97, "curve": [0.078, -0.81, 0.156, -0.55]}, {"time": 0.2333, "value": -0.39, "curve": [0.311, -0.23, 0.389, 0]}, {"time": 0.4667, "curve": [0.667, 0, 0.867, -5.49]}, {"time": 1.0667, "value": -7.14, "curve": [1.244, -8.62, 1.422, -9.38]}, {"time": 1.6, "value": -9.38, "curve": [1.789, -9.38, 1.978, -2.37]}, {"time": 2.1667, "value": -0.97, "curve": [2.244, -0.39, 2.322, -0.55]}, {"time": 2.4, "value": -0.39, "curve": [2.478, -0.23, 2.556, 0]}, {"time": 2.6333, "curve": [2.822, 0, 3.011, -1.1]}, {"time": 3.2, "value": -1.1, "curve": [3.244, -1.1, 3.289, -1.05]}, {"time": 3.3333, "value": -0.97}]}, "Nane1_White4": {"rotate": [{"value": -1.1, "curve": [0.078, -1.1, 0.156, -0.87]}, {"time": 0.2333, "value": -0.72, "curve": [0.311, -0.56, 0.389, -0.16]}, {"time": 0.4667, "value": -0.16, "curve": [0.667, -0.16, 0.867, -5.69]}, {"time": 1.0667, "value": -7.28, "curve": [1.244, -8.7, 1.422, -9.2]}, {"time": 1.6, "value": -9.2, "curve": [1.789, -9.2, 1.978, -2.05]}, {"time": 2.1667, "value": -1.1, "curve": [2.244, -0.72, 2.322, -0.86]}, {"time": 2.4, "value": -0.72, "curve": [2.522, -0.49, 2.644, 0]}, {"time": 2.7667, "curve": [2.956, 0, 3.144, -1.1]}, {"time": 3.3333, "value": -1.1}]}, "Nane1_White5": {"rotate": [{"value": -0.99, "curve": [0.044, -1.07, 0.089, -1.1]}, {"time": 0.1333, "value": -1.1, "curve": [0.167, -1.1, 0.2, -1.06]}, {"time": 0.2333, "value": -0.99, "curve": [0.311, -0.82, 0.389, -0.39]}, {"time": 0.4667, "value": -0.39, "curve": [0.667, -0.39, 0.867, -6.06]}, {"time": 1.0667, "value": -7.17, "curve": [1.244, -8.15, 1.422, -8.15]}, {"time": 1.6, "value": -8.15, "curve": [1.789, -8.15, 1.978, -0.99]}, {"time": 2.1667, "value": -0.99, "curve": [2.211, -0.99, 2.256, -1.1]}, {"time": 2.3, "value": -1.1, "curve": [2.333, -1.1, 2.367, -1.05]}, {"time": 2.4, "value": -0.99, "curve": [2.556, -0.69, 2.711, 0]}, {"time": 2.8667, "curve": [3.022, 0, 3.178, -0.7]}, {"time": 3.3333, "value": -0.99}]}, "Nane1_White6": {"rotate": [{"value": -0.72, "curve": [0.078, -0.87, 0.156, -1.1]}, {"time": 0.2333, "value": -1.1, "curve": [0.311, -1.1, 0.389, -0.73]}, {"time": 0.4667, "value": -0.73, "curve": [0.667, -0.73, 0.867, -8.41]}, {"time": 1.0667, "value": -8.94, "curve": [1.244, -9.41, 1.422, -9.41]}, {"time": 1.6, "value": -9.41, "curve": [1.789, -9.41, 1.978, -0.72]}, {"time": 2.1667, "value": -0.72, "curve": [2.244, -0.72, 2.322, -1.1]}, {"time": 2.4, "value": -1.1, "curve": [2.6, -1.1, 2.8, 0]}, {"time": 3, "curve": [3.111, 0, 3.222, -0.5]}, {"time": 3.3333, "value": -0.72}]}, "Leg2_l_White3": {"rotate": [{"curve": [0.156, -0.65, 0.311, 0]}, {"time": 0.4667, "value": -1.96, "curve": [0.667, -4.48, 0.867, -36.94]}, {"time": 1.0667, "value": -36.94, "curve": [1.2, -36.94, 1.333, -36.94]}, {"time": 1.4667, "value": -33.86, "curve": [1.7, -28.48, 1.933, -11.42]}, {"time": 2.1667, "value": -0.2}]}, "Tail_White": {"rotate": [{"curve": [0.156, 0, 0.311, -1.89]}, {"time": 0.4667, "value": -2.66, "curve": [0.667, -3.64, 0.867, -5.25]}, {"time": 1.0667, "value": -5.25, "curve": [1.267, -5.25, 1.467, 2.86]}, {"time": 1.6667, "value": 2.86, "curve": [1.833, 2.86, 2, 0.9]}, {"time": 2.1667, "curve": [2.367, -1.07, 2.567, -3.05]}, {"time": 2.7667, "value": -3.05, "curve": [2.956, -3.05, 3.144, 0]}, {"time": 3.3333}]}, "Tail_White2": {"rotate": [{"value": -1.33, "curve": [0.044, -1.07, 0.089, -0.77]}, {"time": 0.1333, "value": -0.54, "curve": [0.178, -0.32, 0.222, 0]}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -0.27]}, {"time": 0.4667, "value": -0.81, "curve": [0.667, -2.46, 0.867, -6.57]}, {"time": 1.0667, "value": -6.57, "curve": [1.267, -6.57, 1.467, -3.15]}, {"time": 1.6667, "value": -2.17, "curve": [1.878, -1.14, 2.089, -1.14]}, {"time": 2.3, "value": -0.54, "curve": [2.344, -0.42, 2.389, 0]}, {"time": 2.4333, "curve": [2.633, 0, 2.833, -3.05]}, {"time": 3.0333, "value": -3.05, "curve": [3.133, -3.05, 3.233, -1.9]}, {"time": 3.3333, "value": -1.33}]}, "Tail_White3": {"rotate": [{"value": -1.86, "curve": [0.044, -1.62, 0.089, -1.3]}, {"time": 0.1333, "value": -1.07, "curve": [0.211, -0.68, 0.289, 0]}, {"time": 0.3667, "curve": [0.4, 0, 0.433, 0]}, {"time": 0.4667, "value": -0.25, "curve": [0.667, -1.78, 0.867, -7.1]}, {"time": 1.0667, "value": -7.1, "curve": [1.267, -7.1, 1.467, -3.68]}, {"time": 1.6667, "value": -2.7, "curve": [1.878, -1.67, 2.089, -1.73]}, {"time": 2.3, "value": -1.07, "curve": [2.378, -0.83, 2.456, 0]}, {"time": 2.5333, "curve": [2.722, 0, 2.911, -3.05]}, {"time": 3.1, "value": -3.05, "curve": [3.178, -3.05, 3.256, -2.28]}, {"time": 3.3333, "value": -1.86}]}, "Tail_White4": {"rotate": [{"value": -2.66, "curve": [0.044, -2.49, 0.089, -2.23]}, {"time": 0.1333, "value": -1.98, "curve": [0.244, -1.34, 0.356, 0]}, {"time": 0.4667, "curve": [0.689, 0, 0.911, -7.91]}, {"time": 1.1333, "value": -7.91, "curve": [1.333, -7.91, 1.533, -4.51]}, {"time": 1.7333, "value": -3.5, "curve": [1.922, -2.53, 2.111, -2.71]}, {"time": 2.3, "value": -1.98, "curve": [2.411, -1.54, 2.522, 0]}, {"time": 2.6333, "curve": [2.822, 0, 3.011, -3.05]}, {"time": 3.2, "value": -3.05, "curve": [3.244, -3.05, 3.289, -2.84]}, {"time": 3.3333, "value": -2.66}]}, "Tail_White5": {"rotate": [{"value": -3.05, "curve": [0.044, -3.05, 0.089, -2.98]}, {"time": 0.1333, "value": -2.73, "curve": [0.244, -2.1, 0.356, -0.4]}, {"time": 0.4667, "value": -0.4, "curve": [0.689, -0.4, 0.911, -8.3]}, {"time": 1.1333, "value": -8.3, "curve": [1.333, -8.3, 1.533, -4.79]}, {"time": 1.7333, "value": -3.83, "curve": [1.922, -2.93, 2.111, -3.43]}, {"time": 2.3, "value": -2.73, "curve": [2.456, -2.15, 2.611, 0]}, {"time": 2.7667, "curve": [2.956, 0, 3.144, -3.05]}, {"time": 3.3333, "value": -3.05}]}, "Tail_White6": {"rotate": [{"value": -2.73, "curve": [0.044, -2.96, 0.089, -3.05]}, {"time": 0.1333, "value": -3.05, "curve": [0.244, -3.05, 0.356, -1.12]}, {"time": 0.4667, "value": -1.12, "curve": [0.722, -1.12, 0.978, -7.98]}, {"time": 1.2333, "value": -7.98, "curve": [1.433, -7.98, 1.633, -2.14]}, {"time": 1.8333, "value": -2.14, "curve": [1.989, -2.14, 2.144, -3.05]}, {"time": 2.3, "value": -3.05, "curve": [2.489, -3.05, 2.678, 0]}, {"time": 2.8667, "curve": [3.022, 0, 3.178, -1.94]}, {"time": 3.3333, "value": -2.73}]}, "Tail_White7": {"rotate": [{"value": -1.92, "curve": [0.044, -2.18, 0.089, -2.52]}, {"time": 0.1333, "value": -2.73, "curve": [0.167, -2.89, 0.2, -3.05]}, {"time": 0.2333, "value": -3.05, "curve": [0.311, -3.05, 0.389, -2.03]}, {"time": 0.4667, "value": -2.03, "curve": [0.722, -2.03, 0.978, -7.17]}, {"time": 1.2333, "value": -7.17, "curve": [1.433, -7.17, 1.633, -0.91]}, {"time": 1.8333, "value": -0.91, "curve": [1.989, -0.91, 2.144, -2.14]}, {"time": 2.3, "value": -2.73, "curve": [2.333, -2.86, 2.367, -3.05]}, {"time": 2.4, "value": -3.05, "curve": [2.6, -3.05, 2.8, 0]}, {"time": 3, "curve": [3.111, 0, 3.222, -1.27]}, {"time": 3.3333, "value": -1.92}]}, "Tail_White8": {"rotate": [{"value": -1.43, "curve": [0.044, -1.68, 0.089, -2.01]}, {"time": 0.1333, "value": -2.25, "curve": [0.189, -2.55, 0.244, -3.05]}, {"time": 0.3, "value": -3.05, "curve": [0.356, -3.05, 0.411, -2.47]}, {"time": 0.4667, "value": -2.47, "curve": [0.722, -2.47, 0.978, -6.68]}, {"time": 1.2333, "value": -6.68, "curve": [1.433, -6.68, 1.633, -0.42]}, {"time": 1.8333, "value": -0.42, "curve": [1.989, -0.42, 2.144, -1.61]}, {"time": 2.3, "value": -2.25, "curve": [2.356, -2.48, 2.411, -3.05]}, {"time": 2.4667, "value": -3.05, "curve": [2.667, -3.05, 2.867, 0]}, {"time": 3.0667, "curve": [3.156, 0, 3.244, -0.93]}, {"time": 3.3333, "value": -1.43}]}, "Leg2_r_White2": {"rotate": [{"time": 0.4667, "value": 0.64, "curve": [0.667, 1.09, 0.867, 2]}, {"time": 1.0667, "value": 2, "curve": [1.2, 2, 1.333, -7.94]}, {"time": 1.4667, "value": -7.94, "curve": [1.7, -7.94, 1.933, -2.85]}, {"time": 2.1667, "value": -0.3}]}, "Leg2_r_White3": {"rotate": [{"time": 0.4667, "value": -1.54, "curve": [0.667, -7.33, 0.867, -18.92]}, {"time": 1.0667, "value": -18.92, "curve": [1.2, -18.92, 1.333, -12.73]}, {"time": 1.4667, "value": -10.42, "curve": [1.7, -6.39, 1.933, -3.41]}, {"time": 2.1667, "value": 0.1}]}, "Leg2_l_White2": {"rotate": [{"time": 0.4667, "value": 0.98, "curve": [0.667, 5.73, 0.867, 15.24]}, {"time": 1.0667, "value": 15.24, "curve": [1.2, 15.24, 1.333, 9.67]}, {"time": 1.4667, "value": 7.8, "curve": [1.7, 4.55, 1.933, 2.52]}, {"time": 2.1667, "value": -0.12}]}}}, "walk": {"bones": {"Body_White2": {"translatey": [{"curve": [0.053, 3.19, 0.111, 5.47]}, {"time": 0.1667, "value": 5.47, "curve": [0.256, 5.47, 0.344, -6.63]}, {"time": 0.4333, "value": -6.63, "curve": [0.533, -6.63, 0.633, 5.47]}, {"time": 0.7333, "value": 5.47, "curve": [0.822, 5.47, 0.911, -6.63]}, {"time": 1, "value": -6.63, "curve": [1.044, -6.63, 1.099, -2.04]}, {"time": 1.1333}]}, "Body_White3": {"translatey": [{"curve": [0.055, -7.1, 0.089, -13.79]}, {"time": 0.1333, "value": -13.79, "curve": [0.233, -13.79, 0.333, 12]}, {"time": 0.4333, "value": 12, "curve": [0.478, 12, 0.512, 8.51]}, {"time": 0.5667, "curve": [0.613, -7.21, 0.678, -13.79]}, {"time": 0.7333, "value": -13.79, "curve": [0.811, -13.79, 0.889, 12]}, {"time": 0.9667, "value": 12, "curve": [1.022, 12, 1.089, 5.7]}, {"time": 1.1333}]}, "Body_White4": {"translatey": [{"curve": [0.055, 5.44, 0.089, 7.19]}, {"time": 0.1333, "value": 7.19, "curve": [0.233, 7.19, 0.333, -6.96]}, {"time": 0.4333, "value": -6.96, "curve": [0.478, -6.96, 0.517, -3.43]}, {"time": 0.5667, "curve": [0.619, 3.58, 0.678, 7.19]}, {"time": 0.7333, "value": 7.19, "curve": [0.811, 7.19, 0.889, -6.96]}, {"time": 0.9667, "value": -6.96, "curve": [1.022, -6.96, 1.094, -3.9]}, {"time": 1.1333}]}, "Leg2_r_White": {"rotate": [{"value": -16.49, "curve": [0.052, -16.49, 0.089, -12.46]}, {"time": 0.1333, "value": -11.44, "curve": [0.278, -8.13, 0.422, -3.51]}, {"time": 0.5667, "value": -3.51, "curve": [0.622, -3.51, 0.678, -3.51]}, {"time": 0.7333, "value": -3.8, "curve": [0.867, -4.51, 0.998, -16.49]}, {"time": 1.1333, "value": -16.49}], "translate": [{"x": 8.03, "y": -21.52, "curve": [0.052, 8.03, 0.089, 7.84, 0.052, -21.52, 0.089, -14.67]}, {"time": 0.1333, "x": 7.68, "y": -13.66, "curve": [0.278, 7.17, 0.422, 6.02, 0.278, -10.41, 0.422, -8.75]}, {"time": 0.5667, "x": 6.02, "y": -8.75, "curve": [0.756, 6.02, 0.946, 8.03, 0.756, -8.75, 0.946, -21.52]}, {"time": 1.1333, "x": 8.03, "y": -21.52}]}, "Leg2_r_White2": {"rotate": [{"value": 5.45, "curve": [0.052, 5.45, 0.089, 14.59]}, {"time": 0.1333, "value": 14.59, "curve": [0.278, 14.59, 0.431, 5.45]}, {"time": 0.5667, "value": 5.45}]}, "Leg2_r_White3": {"rotate": [{"value": -17.19, "curve": [0.052, -17.19, 0.089, -26.19]}, {"time": 0.1333, "value": -26.19, "curve": [0.278, -26.19, 0.431, -17.19]}, {"time": 0.5667, "value": -17.19}]}, "Leg2_r_White4": {"rotate": [{"value": 15.77, "curve": [0.052, 15.77, 0.089, -4.46]}, {"time": 0.1333, "value": -4.46, "curve": [0.233, -4.46, 0.333, -3.57]}, {"time": 0.4333, "value": -2.79, "curve": [0.478, -2.45, 0.522, -2.79]}, {"time": 0.5667, "value": -1.09, "curve": [0.7, 4.01, 0.833, 34.5]}, {"time": 0.9667, "value": 34.5, "curve": [1.022, 34.5, 1.081, 15.77]}, {"time": 1.1333, "value": 15.77}], "translatex": [{"value": -102.97, "curve": [0.052, -102.97, 0.378, 21.27]}, {"time": 0.5667, "value": 21.27, "curve": [0.756, 21.27, 1.081, -102.97]}, {"time": 1.1333, "value": -102.97}], "translatey": [{"value": 28.55, "curve": [0.052, 28.55, 0.089, 18.85]}, {"time": 0.1333, "value": 18.85, "curve": [0.278, 18.85, 0.422, 28.55]}, {"time": 0.5667, "value": 28.55, "curve": "stepped"}, {"time": 0.7333, "value": 28.55, "curve": [0.811, 28.55, 0.889, 75.07]}, {"time": 0.9667, "value": 75.07, "curve": [1.022, 75.07, 1.081, 28.55]}, {"time": 1.1333, "value": 28.55}]}, "Leg2_l_White": {"rotate": [{"value": -0.07, "curve": [0.052, -0.07, 0.089, 21.28]}, {"time": 0.1333, "value": 21.28, "curve": [0.233, 21.28, 0.333, -26.76]}, {"time": 0.4333, "value": -26.76, "curve": [0.478, -26.76, 0.522, -20.09]}, {"time": 0.5667, "value": -20.09, "curve": [0.622, -20.09, 0.678, -33.05]}, {"time": 0.7333, "value": -33.05, "curve": [0.867, -33.05, 0.998, -0.07]}, {"time": 1.1333, "value": -0.07}], "translate": [{"x": 64.63, "y": -46.88, "curve": [0.052, 64.63, 0.089, 48.51, 0.052, -46.88, 0.089, -49.03]}, {"time": 0.1333, "x": 44.44, "y": -49.58, "curve": [0.278, 31.2, 0.422, 12.71, 0.278, -51.35, 0.422, -53.83]}, {"time": 0.5667, "x": 12.71, "y": -53.83, "curve": [0.756, 12.71, 0.946, 64.63, 0.756, -53.83, 0.946, -46.88]}, {"time": 1.1333, "x": 64.63, "y": -46.88}]}, "Leg2_l_White2": {"rotate": [{"value": 33.28, "curve": [0.052, 33.28, 0.089, 57.58]}, {"time": 0.1333, "value": 57.58, "curve": [0.278, 57.58, 0.431, 33.28]}, {"time": 0.5667, "value": 33.28}]}, "Leg2_l_White3": {"rotate": [{"value": -29.26, "curve": [0.052, -29.26, 0.089, -38.23]}, {"time": 0.1333, "value": -38.23, "curve": [0.278, -38.23, 0.431, -29.26]}, {"time": 0.5667, "value": -29.26}]}, "Leg2_l_White4": {"rotate": [{"curve": [0.052, 0, 0.089, 0]}, {"time": 0.1333, "value": 0.5, "curve": [0.233, 1.63, 0.333, 47.35]}, {"time": 0.4333, "value": 47.35, "curve": [0.478, 47.35, 0.522, 28.23]}, {"time": 0.5667, "value": 20.88, "curve": [0.622, 11.7, 0.682, 0.52]}, {"time": 0.7333, "value": -2.24, "curve": [0.87, -9.33, 0.998, 0]}, {"time": 1.1333}], "translatex": [{"value": 113, "curve": [0.05, 133.91, 0.089, 169.95]}, {"time": 0.1333, "value": 169.95, "curve": [0.278, 169.95, 0.422, -28.87]}, {"time": 0.5667, "value": -28.87, "curve": [0.756, -28.87, 0.952, 37.35]}, {"time": 1.1333, "value": 113}], "translatey": [{"curve": [0.052, 0, 0.089, 0]}, {"time": 0.1333, "value": 6.37, "curve": [0.233, 20.68, 0.333, 71.64]}, {"time": 0.4333, "value": 71.64, "curve": [0.478, 71.64, 0.522, 33.34]}, {"time": 0.5667, "value": 22.65, "curve": [0.622, 9.28, 0.678, -0.54]}, {"time": 0.7333, "value": -0.54, "curve": [0.867, -0.54, 0.998, 0]}, {"time": 1.1333}]}, "Leg1_r_White": {"rotate": [{"curve": [0.052, 0, 0.089, -27.79]}, {"time": 0.1333, "value": -27.79, "curve": [0.278, -27.79, 0.422, -24.54]}, {"time": 0.5667, "value": -23.62, "curve": [0.7, -22.77, 0.833, -23.62]}, {"time": 0.9667, "value": -22.48, "curve": [1.022, -22.01, 1.081, 0]}, {"time": 1.1333}]}, "Leg1_r_White2": {"rotate": [{"value": 46.58, "curve": [0.052, 46.58, 0.089, 24.78]}, {"time": 0.1333, "value": 24.78, "curve": [0.278, 24.78, 0.431, 46.58]}, {"time": 0.5667, "value": 46.58}]}, "Leg1_r_White3": {"rotate": [{"value": -46.27, "curve": [0.052, -46.27, 0.089, -40.57]}, {"time": 0.1333, "value": -40.57, "curve": [0.278, -40.57, 0.431, -46.27]}, {"time": 0.5667, "value": -46.27}]}, "Leg1_r_White4": {"rotate": [{"value": 40.05, "curve": [0.052, 40.05, 0.089, 28.51]}, {"time": 0.1333, "value": 23.93, "curve": [0.233, 13.61, 0.333, -4.66]}, {"time": 0.4333, "value": -4.66, "curve": [0.478, -4.66, 0.522, -4.26]}, {"time": 0.5667, "value": -1.42, "curve": [0.756, 10.65, 0.946, 40.05]}, {"time": 1.1333, "value": 40.05}], "translatex": [{"value": 57.41, "curve": [0.052, 57.41, 0.289, -94.52]}, {"time": 0.4333, "value": -95.2, "curve": [0.478, -95.41, 0.522, -95.41]}, {"time": 0.5667, "value": -95.41, "curve": [0.7, -95.41, 0.832, 30.26]}, {"time": 0.9667, "value": 58.57, "curve": [1.022, 70.17, 1.081, 57.41]}, {"time": 1.1333, "value": 57.41}], "translatey": [{"value": 56.2, "curve": [0.058, 63.01, 0.081, 70.86]}, {"time": 0.1333, "value": 70.86, "curve": [0.214, 72.46, 0.343, 62.43]}, {"time": 0.4333, "value": 41.42, "curve": [0.482, 34.94, 0.522, 27.4]}, {"time": 0.5667, "value": 27.4, "curve": [0.622, 27.4, 0.678, 31.83]}, {"time": 0.7333, "value": 34.21, "curve": [0.811, 37.54, 0.889, 40.25]}, {"time": 0.9667, "value": 44.52, "curve": [1.022, 47.58, 1.085, 50.56]}, {"time": 1.1333, "value": 56.2}]}, "Leg1_l_White": {"rotate": [{"curve": [0.052, 0, 0.089, 29.26]}, {"time": 0.1333, "value": 30.35, "curve": [0.278, 33.89, 0.422, 33.89]}, {"time": 0.5667, "value": 33.89, "curve": [0.622, 33.89, 0.678, -8.8]}, {"time": 0.7333, "value": -8.8, "curve": [0.811, -8.8, 0.889, 9.8]}, {"time": 0.9667, "value": 9.8, "curve": [1.022, 9.8, 1.081, 0]}, {"time": 1.1333}], "translate": [{"curve": [0.052, 0, 0.089, -0.16, 0.052, 0, 0.089, 17.52]}, {"time": 0.1333, "x": -0.16, "y": 17.52, "curve": [0.278, -0.16, 0.431, 0, 0.278, 17.52, 0.431, 0]}, {"time": 0.5667}]}, "Leg1_l_White2": {"rotate": [{"value": -0.08, "curve": [0.052, -0.08, 0.089, -13]}, {"time": 0.1333, "value": -13, "curve": [0.278, -13, 0.431, -0.08]}, {"time": 0.5667, "value": -0.08}]}, "Leg1_l_White3": {"rotate": [{"value": 0.42, "curve": [0.052, 0.42, 0.089, 0.27]}, {"time": 0.1333, "value": 0.27, "curve": [0.278, 0.27, 0.431, 0.42]}, {"time": 0.5667, "value": 0.42}]}, "Leg1_l_White4": {"rotate": [{"curve": [0.052, 0, 0.089, 8.82]}, {"time": 0.1333, "value": 14, "curve": [0.278, 30.82, 0.422, 65.99]}, {"time": 0.5667, "value": 65.99, "curve": [0.622, 65.99, 0.678, 39.34]}, {"time": 0.7333, "value": 32.87, "curve": [0.867, 17.35, 0.998, 0]}, {"time": 1.1333}], "translatex": [{"curve": [0.054, 14.96, 0.082, 17.67]}, {"time": 0.1667, "value": 58.85, "curve": [0.241, 94.87, 0.344, 155.09]}, {"time": 0.4333, "value": 155.09, "curve": [0.478, 155.09, 0.522, 155.09]}, {"time": 0.5667, "value": 152.06, "curve": [0.7, 142.99, 0.833, -28.52]}, {"time": 0.9667, "value": -28.52, "curve": [1.022, -28.52, 1.081, -14.5]}, {"time": 1.1333}], "translatey": [{"curve": [0.052, 0, 0.089, 0]}, {"time": 0.1333, "value": 1.44, "curve": [0.233, 4.67, 0.354, 16.32]}, {"time": 0.4333, "value": 28.4, "curve": [0.485, 36.31, 0.517, 46.36]}, {"time": 0.5667, "value": 56.31, "curve": [0.619, 66.74, 0.678, 76.02]}, {"time": 0.7333, "value": 76.02, "curve": [0.789, 76.02, 0.836, 58.17]}, {"time": 0.9, "value": 42.27, "curve": [0.968, 25.3, 1.06, 0]}, {"time": 1.1333}]}, "Tail_White": {"rotate": [{"value": -4.38, "curve": [0.111, -2.92, 0.223, 0]}, {"time": 0.3333, "curve": [0.533, 0, 0.733, -6.42]}, {"time": 0.9333, "value": -6.42, "curve": [1, -6.42, 1.067, -5.06]}, {"time": 1.1333, "value": -4.38}]}, "Tail_White2": {"rotate": [{"value": -1.7, "curve": [0.112, -3.11, 0.222, -4.78]}, {"time": 0.3333, "value": -6.32, "curve": [0.345, -6.35, 0.356, -6.42]}, {"time": 0.3667, "value": -6.42, "curve": [0.544, -6.42, 0.722, 0]}, {"time": 0.9, "curve": [0.978, 0, 1.056, -0.71]}, {"time": 1.1333, "value": -1.7}]}, "Tail_White3": {"rotate": [{"value": -0.49, "curve": [0.112, -1.48, 0.223, -3.77]}, {"time": 0.3333, "value": -5.19, "curve": [0.389, -5.9, 0.444, -6.42]}, {"time": 0.5, "value": -6.42, "curve": [0.633, -6.42, 0.767, -2.82]}, {"time": 0.9, "value": -1.03, "curve": [0.944, -0.49, 0.989, 0]}, {"time": 1.0333, "curve": [1.067, 0, 1.101, -0.18]}, {"time": 1.1333, "value": -0.49}]}, "Tail_White4": {"rotate": [{"value": -0.1, "curve": [0.012, -0.05, 0.022, 0]}, {"time": 0.0333, "curve": [0.134, 0, 0.235, -1.59]}, {"time": 0.3333, "value": -3.21, "curve": [0.435, -4.8, 0.533, -6.42]}, {"time": 0.6333, "value": -6.42, "curve": [0.722, -6.42, 0.811, -4.28]}, {"time": 0.9, "value": -3.21, "curve": [0.978, -2.27, 1.056, -1.14]}, {"time": 1.1333, "value": -0.1}]}, "Tail_White5": {"rotate": [{"value": -1.55, "curve": [0.056, -0.67, 0.111, 0]}, {"time": 0.1667, "curve": [0.223, 0, 0.279, -0.5]}, {"time": 0.3333, "value": -1.23, "curve": [0.479, -3.09, 0.622, -6.42]}, {"time": 0.7667, "value": -6.42, "curve": [0.811, -6.42, 0.856, -5.73]}, {"time": 0.9, "value": -5.39, "curve": [0.978, -4.46, 1.056, -2.83]}, {"time": 1.1333, "value": -1.55}]}, "Tail_White6": {"rotate": [{"value": -2.8, "curve": [0.089, -1.32, 0.178, 0]}, {"time": 0.2667, "curve": [0.289, 0, 0.312, -0.09]}, {"time": 0.3333, "value": -0.23, "curve": [0.512, -1.38, 0.689, -6.42]}, {"time": 0.8667, "value": -6.42, "curve": [0.878, -6.42, 0.889, -6.34]}, {"time": 0.9, "value": -6.31, "curve": [0.978, -5.52, 1.056, -3.97]}, {"time": 1.1333, "value": -2.8}]}, "Tail_White7": {"rotate": [{"value": -5.14, "curve": [0.111, -3.47, 0.222, -1.79]}, {"time": 0.3333, "value": -0.11, "curve": [0.344, 0, 0.356, 0]}, {"time": 0.3667, "curve": [0.545, 0, 0.722, -4.12]}, {"time": 0.9, "value": -6.18, "curve": [0.923, -6.32, 0.944, -6.42]}, {"time": 0.9667, "value": -6.42, "curve": [1.022, -6.42, 1.078, -5.57]}, {"time": 1.1333, "value": -5.14}]}, "Tail_White8": {"rotate": [{"value": -6.42, "curve": [0.111, -4.96, 0.222, -3.5]}, {"time": 0.3333, "value": -2.04, "curve": [0.4, -1.24, 0.467, 0]}, {"time": 0.5333, "curve": [0.656, 0, 0.778, -2.84]}, {"time": 0.9, "value": -4.26, "curve": [0.979, -5.43, 1.056, -5.7]}, {"time": 1.1333, "value": -6.42}]}, "Nane1_White": {"rotate": [{"curve": [0.178, 0, 0.356, -1.56]}, {"time": 0.5333, "value": -1.56, "curve": [0.733, -1.56, 0.933, 0]}, {"time": 1.1333}]}, "Nane1_White2": {"rotate": [{"value": -0.12, "curve": [0.033, -0.08, 0.067, 0]}, {"time": 0.1, "curve": [0.278, 0, 0.456, -1.56]}, {"time": 0.6333, "value": -1.56, "curve": [0.8, -1.56, 0.967, -0.6]}, {"time": 1.1333, "value": -0.12}]}, "Nane1_White3": {"rotate": [{"value": -0.41, "curve": [0.067, -0.27, 0.134, 0]}, {"time": 0.2, "curve": [0.378, 0, 0.556, -1.56]}, {"time": 0.7333, "value": -1.56, "curve": [0.867, -1.56, 1, -0.79]}, {"time": 1.1333, "value": -0.41}]}, "Nane1_White4": {"rotate": [{"value": -0.78, "curve": [0.1, -0.52, 0.201, 0]}, {"time": 0.3, "curve": [0.478, 0, 0.656, -1.56]}, {"time": 0.8333, "value": -1.56, "curve": [0.933, -1.56, 1.033, -1.04]}, {"time": 1.1333, "value": -0.78}]}, "Nane1_White5": {"rotate": [{"value": -1.16, "curve": [0.133, -0.77, 0.267, 0]}, {"time": 0.4, "curve": [0.578, 0, 0.756, -1.56]}, {"time": 0.9333, "value": -1.56, "curve": [1, -1.56, 1.067, -1.29]}, {"time": 1.1333, "value": -1.16}]}, "Nane1_White6": {"rotate": [{"value": -1.44, "curve": [0.167, -0.96, 0.333, 0]}, {"time": 0.5, "curve": [0.678, 0, 0.856, -1.56]}, {"time": 1.0333, "value": -1.56, "curve": [1.067, -1.56, 1.1, -1.48]}, {"time": 1.1333, "value": -1.44}]}, "Nane2_White": {"rotate": [{"curve": [0.189, 0, 0.378, 2.9]}, {"time": 0.5667, "value": 2.9, "curve": [0.756, 2.9, 0.944, 0]}, {"time": 1.1333}]}, "Nane2_White2": {"rotate": [{"value": 0.25, "curve": [0.033, 0.17, 0.067, 0]}, {"time": 0.1, "curve": [0.289, 0, 0.478, 2.9]}, {"time": 0.6667, "value": 2.9, "curve": [0.822, 2.9, 0.978, 1.13]}, {"time": 1.1333, "value": 0.25}]}, "Nane2_White3": {"rotate": [{"value": 0.83, "curve": [0.067, 0.56, 0.134, 0]}, {"time": 0.2, "curve": [0.389, 0, 0.578, 2.9]}, {"time": 0.7667, "value": 2.9, "curve": [0.889, 2.9, 1.011, 1.52]}, {"time": 1.1333, "value": 0.83}]}, "Nane2_White4": {"rotate": [{"value": 1.58, "curve": [0.1, 1.05, 0.201, 0]}, {"time": 0.3, "curve": [0.489, 0, 0.678, 2.9]}, {"time": 0.8667, "value": 2.9, "curve": [0.956, 2.9, 1.044, 2.02]}, {"time": 1.1333, "value": 1.58}]}, "Nane2_White5": {"rotate": [{"value": 2.29, "curve": [0.133, 1.53, 0.267, 0]}, {"time": 0.4, "curve": [0.589, 0, 0.778, 2.9]}, {"time": 0.9667, "value": 2.9, "curve": [1.022, 2.9, 1.078, 2.49]}, {"time": 1.1333, "value": 2.29}]}, "Ear_White2": {"rotate": [{"curve": [0.189, 0, 0.378, -7.89]}, {"time": 0.5667, "value": -7.89, "curve": [0.756, -7.89, 0.944, 0]}, {"time": 1.1333}]}, "Ear_White3": {"rotate": [{"value": -2.27, "curve": [0.067, -1.52, 0.134, 0]}, {"time": 0.2, "curve": [0.389, 0, 0.578, -7.89]}, {"time": 0.7667, "value": -7.89, "curve": [0.889, -7.89, 1.011, -4.15]}, {"time": 1.1333, "value": -2.27}]}, "Body_White5": {"rotate": [{"value": -1.29, "curve": [0.189, -1.29, 0.378, 1.3]}, {"time": 0.5667, "value": 1.3, "curve": [0.756, 1.3, 0.944, -1.29]}, {"time": 1.1333, "value": -1.29}]}, "Body_White6": {"rotate": [{"value": -0.75, "curve": [0.056, -0.93, 0.111, -1.29]}, {"time": 0.1667, "value": -1.29, "curve": [0.356, -1.29, 0.544, 1.3]}, {"time": 0.7333, "value": 1.3, "curve": [0.867, 1.3, 1, -0.06]}, {"time": 1.1333, "value": -0.75}]}, "Ear_White": {"rotate": [{"value": 0.12, "curve": [0.1, -0.35, 0.201, -1.29]}, {"time": 0.3, "value": -1.29, "curve": [0.489, -1.29, 0.678, 1.3]}, {"time": 0.8667, "value": 1.3, "curve": [0.956, 1.3, 1.044, 0.51]}, {"time": 1.1333, "value": 0.12}]}}}}}