{"skeleton": {"hash": "a3PK3afrUPE", "spine": "4.2.40", "x": -236.07, "y": -32.96, "width": 570.59, "height": 585.54, "images": "../images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "body", "parent": "bone", "x": -22.5, "y": 270.02}, {"name": "body2", "parent": "body", "length": 109.22, "rotation": -177.89, "x": -9.63}, {"name": "duoi", "parent": "body2", "length": 19.8, "rotation": -25.54, "x": 148.37, "y": -16.58}, {"name": "duoi2", "parent": "duoi", "length": 19.8, "rotation": -13.33, "x": 20.55, "y": -0.5}, {"name": "duoi3", "parent": "duoi2", "length": 19.8, "rotation": -30.24, "x": 19.55, "y": 0.03}, {"name": "duoi4", "parent": "duoi3", "length": 19.8, "rotation": -24.9, "x": 20.18, "y": 0.17}, {"name": "duoi5", "parent": "duoi4", "length": 19.8, "rotation": -22.96, "x": 19.57, "y": -0.17}, {"name": "duoi6", "parent": "duoi5", "length": 19.8, "rotation": -0.96, "x": 19.52, "y": -0.2}, {"name": "duoi7", "parent": "duoi6", "length": 19.8, "rotation": 15.73, "x": 18.55, "y": -0.12}, {"name": "duoi8", "parent": "duoi7", "length": 19.8, "rotation": -2.34, "x": 19.14, "y": -0.22}, {"name": "long duoi", "parent": "duoi8", "length": 19.8, "rotation": 0.7, "x": 19.77, "y": 0.08}, {"name": "long duoi2", "parent": "long duoi", "length": 19.8, "rotation": -11.81, "x": 15.86, "y": 0.02}, {"name": "body3", "parent": "body", "length": 80.42, "rotation": -1.37, "x": 5.49, "y": 1.49}, {"name": "body4", "parent": "body3", "length": 81.05, "rotation": 21.07, "x": 80.42}, {"name": "body5", "parent": "body4", "length": 94.62, "rotation": 35.22, "x": 54.41, "y": 28.69}, {"name": "dau", "parent": "body5", "rotation": -72.47, "x": 110.93, "y": -0.58}, {"name": "mat2", "parent": "dau", "length": 82.99, "rotation": 145.7, "x": 45.92, "y": 41.4}, {"name": "con nguoi1", "parent": "mat2", "x": 17.65, "y": 2.22}, {"name": "sung1", "parent": "dau", "length": 43.58, "rotation": 147.24, "x": -127.4, "y": 33.2}, {"name": "tai1", "parent": "dau", "length": 34.57, "rotation": -168.79, "x": -133.99, "y": -19.41}, {"name": "tai2", "parent": "tai1", "length": 26.67, "rotation": 1.4, "x": 34.57}, {"name": "tai3", "parent": "tai2", "length": 21.98, "rotation": -0.72, "x": 26.67}, {"name": "tai4", "parent": "tai3", "length": 30.07, "rotation": 0.6, "x": 21.98}, {"name": "swng2", "parent": "dau", "length": 24.88, "rotation": 80.29, "x": 13.2, "y": 84.94}, {"name": "tai5", "parent": "dau", "length": 26.4, "rotation": 17.57, "x": 41.09, "y": 51.27}, {"name": "tai6", "parent": "tai5", "length": 26.4, "rotation": 7.49, "x": 28.12, "y": -0.13}, {"name": "tai7", "parent": "tai6", "length": 28.05, "rotation": 12.73, "x": 28.94}, {"name": "tai8", "parent": "tai7", "length": 26.4, "rotation": -6.77, "x": 29.75, "y": -0.16}, {"name": "body6", "parent": "body2", "x": 80.24, "y": -2}, {"name": "dui sau1", "parent": "body6", "length": 99, "rotation": 106.98, "x": 61.06, "y": 7.05}, {"name": "dui sau2", "parent": "dui sau1", "length": 82.5, "rotation": -27.09, "x": 99.97, "y": -0.39}, {"name": "dui sau3", "parent": "dui sau2", "length": 84.85, "rotation": 18.36, "x": 82.06, "y": -0.06}, {"name": "dui sau4", "parent": "bone", "length": 23.34, "rotation": -40.03, "x": -164.8, "y": 10.04, "color": "ff3f00ff", "icon": "ik"}, {"name": "dui sau5", "parent": "body2", "length": 99, "rotation": 107.83, "x": 69.37, "y": -13.55}, {"name": "dui sau6", "parent": "dui sau5", "length": 82.5, "rotation": -30.2, "x": 97.81, "y": 0.34}, {"name": "dui sau7", "parent": "dui sau6", "length": 72.81, "rotation": 30.73, "x": 82.22, "y": -0.12}, {"name": "dui sau8", "parent": "bone", "length": 28.56, "rotation": -31.7, "x": -81.71, "y": 45.67, "color": "ff3f00ff", "icon": "ik"}, {"name": "dui truoc1", "parent": "body4", "length": 99, "rotation": -94.34, "x": -62.76, "y": -65.47}, {"name": "dui truoc2", "parent": "dui truoc1", "length": 97.71, "rotation": -7.96, "x": 97.4}, {"name": "dui truoc3", "parent": "bone", "length": 26.02, "rotation": -69.08, "x": 54.91, "y": -2.29, "color": "ff3f00ff", "icon": "ik"}, {"name": "dui truoc4", "parent": "body4", "length": 99, "rotation": -93.23, "x": 41.4, "y": -42.55}, {"name": "dui truoc5", "parent": "dui truoc4", "length": 97.71, "rotation": -4.07, "x": 101.08, "y": 0.06}, {"name": "dui truoc6", "parent": "bone", "length": 22.6, "rotation": -55.3, "x": 143.16, "y": 48.65, "color": "ff3f00ff", "icon": "ik"}, {"name": "Layer 8", "parent": "body5", "x": 155.28, "y": 100.73, "color": "abe323ff"}, {"name": "Layer 9", "parent": "Layer 8", "length": 23.21, "rotation": -57.33, "x": 7.1, "y": -8.98, "color": "abe323ff"}, {"name": "Layer 10", "parent": "Layer 9", "length": 28.69, "rotation": -20.35, "x": 23.21, "color": "abe323ff"}, {"name": "Layer 7", "parent": "body5", "rotation": -39.06, "x": 65.77, "y": 43.36}, {"name": "Layer 7 copy", "parent": "Layer 7", "rotation": 39.06, "x": -7.71, "y": -7.74}, {"name": "Layer 11", "parent": "body5", "rotation": -39.06, "x": 134.91, "y": -37.68, "scaleX": 0.8149, "scaleY": 0.901}, {"name": "Layer 7 copy2", "parent": "Layer 11", "rotation": 39.06, "x": -7.72, "y": -7.74}, {"name": "body7", "parent": "body", "x": -73.55, "y": -157.49}, {"name": "body8", "parent": "body7", "length": 21.85, "rotation": -64.08, "x": 1.16, "y": -5.53, "color": "abe323ff"}, {"name": "body9", "parent": "body8", "length": 18.83, "rotation": 6.63, "x": 23.5, "y": 0.86, "color": "abe323ff"}, {"name": "Layer 12", "parent": "body5", "length": 26.03, "rotation": -143.83, "x": 15.54, "y": -71.79}, {"name": "Layer 13", "parent": "Layer 12", "length": 22.28, "rotation": -5.48, "x": 14.36, "y": -3.32}], "slots": [{"name": "shank_back_right_white", "bone": "root", "attachment": "shank_back_right_white"}, {"name": "leg_back_right_white", "bone": "dui sau3", "attachment": "leg_back_right_white"}, {"name": "leg_front_right_white", "bone": "dui truoc2", "attachment": "leg_front_right_white"}, {"name": "shank_front_right_white", "bone": "dui truoc4", "attachment": "shank_front_right_white"}, {"name": "oxtail_white", "bone": "root", "attachment": "oxtail_white"}, {"name": "body_white", "bone": "body", "attachment": "body_white"}, {"name": "leg_front_left_white", "bone": "dui truoc2", "attachment": "leg_front_left_white"}, {"name": "shank_front_left_white", "bone": "dui truoc1", "attachment": "shank_front_left_white"}, {"name": "shank_back_left_white", "bone": "root", "attachment": "shank_back_left_white"}, {"name": "leg_back_left_white", "bone": "dui sau3", "attachment": "leg_back_left_white"}, {"name": "horn_right_white", "bone": "swng2", "attachment": "horn_right_white"}, {"name": "ear_right_white", "bone": "root", "attachment": "ear_right_white"}, {"name": "head_white", "bone": "body5", "attachment": "head_white"}, {"name": "mouth_white", "bone": "Layer 13", "attachment": "mouth_white"}, {"name": "ear_left_white", "bone": "root", "attachment": "ear_left_white"}, {"name": "horn_left_white", "bone": "sung1", "attachment": "horn_left_white"}, {"name": "hair_white", "bone": "Layer 8", "attachment": "hair_white"}, {"name": "body5", "bone": "body5", "attachment": "body5"}, {"name": "eye_right_white", "bone": "Layer 11", "attachment": "eye_right_white"}, {"name": "inner_eye_right_white", "bone": "Layer 7 copy2", "attachment": "inner_eye_right_white"}, {"name": "eye_left_white", "bone": "Layer 7", "attachment": "eye_left_white"}, {"name": "inner_eye_left_white", "bone": "Layer 7 copy", "attachment": "inner_eye_left_white"}], "ik": [{"name": "dui sau4", "order": 3, "bones": ["dui sau2", "dui sau3"], "target": "dui sau4", "stretch": true}, {"name": "dui sau8", "order": 2, "bones": ["dui sau6", "dui sau7"], "target": "dui sau8", "stretch": true}, {"name": "dui truoc3", "order": 1, "bones": ["dui truoc1", "dui truoc2"], "target": "dui truoc3", "bendPositive": false, "stretch": true}, {"name": "dui truoc6", "bones": ["dui truoc4", "dui truoc5"], "target": "dui truoc6", "bendPositive": false, "stretch": true}], "physics": [{"name": "body8", "order": 7, "bone": "body8", "rotate": 1, "limit": 266.97, "inertia": 0.5, "damping": 0.85}, {"name": "body9", "order": 8, "bone": "body9", "rotate": 1, "limit": 266.97, "inertia": 0.5, "damping": 0.85}, {"name": "Layer 8", "order": 4, "bone": "Layer 8", "x": 0.0632, "y": 0.0737, "limit": 1650, "inertia": 0.5, "damping": 0.85}, {"name": "Layer 9", "order": 5, "bone": "Layer 9", "rotate": 1, "limit": 1650, "inertia": 0.5, "damping": 0.85}, {"name": "Layer 10", "order": 6, "bone": "Layer 10", "rotate": 1, "limit": 1650, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"body5": {"body5": {"type": "clipping", "end": "inner_eye_right_white", "vertexCount": 12, "vertices": [82.32, -21.84, 97.01, -36.99, 104.68, -49.94, 112.41, -60.94, 120.58, -70.2, 124.56, -73.83, 130.58, -83.42, 149.25, -117.45, 205.8, -82.71, 208.11, -59.9, 163.71, 5.34, 116.85, 18.14], "color": "ce3a3aff"}}, "body_white": {"body_white": {"type": "mesh", "uvs": [0.76826, 0, 0.85734, 0.02365, 0.90771, 0.08092, 0.94545, 0.15318, 0.97379, 0.24501, 0.99439, 0.36198, 1, 0.45859, 1, 0.51964, 0.98542, 0.62277, 0.93754, 0.73393, 0.86937, 0.81497, 0.79153, 0.86868, 0.73239, 0.886, 0.63151, 0.89825, 0.5634, 0.89268, 0.49529, 0.8871, 0.43725, 0.88235, 0.43233, 0.88833, 0.41106, 0.92965, 0.36791, 0.94675, 0.35957, 0.94507, 0.34242, 0.99266, 0.32105, 1, 0.305, 1, 0.28191, 0.96874, 0.27969, 0.95115, 0.24091, 0.94061, 0.17761, 0.88364, 0.11431, 0.82667, 0.05585, 0.7256, 0.00899, 0.58823, 0.00081, 0.50945, 0.00068, 0.38747, 0.02973, 0.25477, 0.08256, 0.16164, 0.1196, 0.11882, 0.17508, 0.08049, 0.25321, 0.05428, 0.31774, 0.05231, 0.41622, 0.06585, 0.43103, 0.06592, 0.48168, 0.06241, 0.48307, 0.06209, 0.56758, 0.03213, 0.6521, 0.00216, 0.7413, 0], "triangles": [26, 27, 25, 17, 19, 20, 25, 27, 17, 24, 22, 23, 25, 20, 22, 24, 25, 22, 20, 21, 22, 17, 20, 25, 18, 19, 17, 6, 9, 5, 4, 5, 10, 6, 7, 9, 4, 1, 3, 3, 1, 2, 9, 7, 8, 0, 1, 4, 5, 9, 10, 0, 4, 45, 10, 11, 4, 4, 44, 45, 4, 43, 44, 4, 12, 43, 12, 4, 11, 16, 40, 41, 12, 42, 43, 15, 41, 42, 42, 14, 15, 42, 13, 14, 12, 13, 42, 16, 39, 40, 15, 16, 41, 17, 27, 16, 28, 29, 27, 16, 27, 29, 34, 31, 32, 34, 30, 31, 32, 33, 34, 39, 29, 30, 35, 36, 37, 37, 38, 35, 39, 35, 38, 35, 30, 34, 30, 35, 39, 29, 39, 16], "vertices": [5, 53, -253.14, 196.98, 0.00012, 3, -142.39, -88.29, 0.00517, 14, 121.68, 94.91, 0.18481, 15, 72.62, 73.74, 0.80987, 52, 188.9, 253.38, 2e-05, 5, 53, -244.59, 233.8, 0.00077, 3, -179.19, -79.67, 0.00051, 14, 158.94, 88.55, 0.09486, 15, 105.1, 54.4, 0.90369, 52, 225.99, 246.12, 0.00017, 5, 53, -226.15, 254.34, 0.00295, 3, -199.75, -61.25, 1e-05, 14, 180.58, 71.41, 0.04141, 15, 119.13, 30.63, 0.95481, 52, 247.21, 228.47, 0.00082, 4, 53, -202.92, 269.74, 0.00812, 14, 197.43, 49.27, 0.01553, 15, 126.9, 3.92, 0.97377, 52, 263.53, 205.93, 0.00259, 4, 53, -173.27, 281.26, 0.01755, 14, 210.91, 20.58, 0.0077, 15, 129.17, -27.69, 0.96908, 52, 276.32, 176.93, 0.00567, 4, 53, -135.4, 289.32, 0.03185, 14, 221.57, -16.47, 0.01087, 15, 125.8, -66.11, 0.94795, 52, 286.08, 139.63, 0.00933, 4, 53, -103.62, 291.19, 0.04892, 14, 225.76, -47.83, 0.02365, 15, 118.44, -96.87, 0.91581, 52, 289.52, 108.18, 0.01162, 4, 53, -83.07, 290.85, 0.06399, 14, 227.03, -68.17, 0.04782, 15, 112.31, -116.32, 0.87701, 52, 290.3, 87.81, 0.01118, 4, 53, -51.43, 283.01, 0.06921, 14, 221.18, -100.35, 0.08696, 15, 95.29, -144.24, 0.83573, 52, 283.68, 55.79, 0.0081, 5, 53, -19.84, 260.82, 0.06117, 3, -207.27, 144.5, 2e-05, 14, 200.57, -133.51, 0.14277, 15, 64.14, -167.78, 0.79167, 52, 262.29, 23.13, 0.00437, 5, 53, 1.1, 230.86, 0.04219, 3, -176.67, 165.69, 0.00025, 14, 171.31, -156.52, 0.2136, 15, 28.56, -178.72, 0.74239, 52, 232.48, 0.83, 0.00157, 5, 53, 13.93, 197.61, 0.02215, 3, -142.86, 178.64, 0.00131, 14, 138.36, -171.5, 0.2951, 15, -7.57, -180.86, 0.6811, 52, 199.18, -13.36, 0.00033, 5, 53, 17.02, 172.78, 0.00784, 3, -117.74, 181.77, 0.00439, 14, 113.47, -176.15, 0.38055, 15, -32.47, -176.26, 0.60662, 54, -7.31, 172.8, 0.0006, 5, 53, 19.01, 130.72, 0.00167, 3, -75.65, 183.69, 0.01058, 14, 71.57, -180.62, 0.4618, 15, -73.17, -165.37, 0.52225, 54, -4.72, 130.71, 0.00371, 6, 53, 16.92, 102.53, 0.00102, 3, -47.7, 181.47, 0.01916, 14, 43.55, -180.1, 0.52326, 15, -99.14, -154.81, 0.43629, 52, 104.19, -19.67, 0.00597, 54, -6.37, 102.5, 0.01431, 6, 53, 15.72, 74.6, 0.01146, 3, -20.29, 179.97, 0.02702, 14, 16.09, -180.27, 0.54479, 15, -124.82, -145.09, 0.35316, 52, 76.74, -19.18, 0.02433, 54, -7.04, 74.58, 0.03924, 6, 53, 15.69, 51.41, 0.0461, 3, 2.19, 179.35, 0.02984, 14, -6.38, -181.01, 0.5047, 15, -146.05, -137.71, 0.27193, 52, 54.25, -19.38, 0.06177, 54, -6.46, 51.28, 0.08565, 6, 53, 21.91, 49.95, 0.11597, 3, 2.19, 184.9, 0.02591, 14, -6.05, -186.56, 0.40426, 15, -147.74, -143.01, 0.19229, 52, 54.45, -24.94, 0.10113, 54, 0.29, 49.92, 0.16045, 6, 53, 37.36, 38.66, 0.20257, 3, 12.32, 200.63, 0.01729, 14, -15.2, -202.87, 0.26902, 15, -162.14, -154.94, 0.11837, 52, 44.91, -41.03, 0.12337, 54, 16.94, 38.62, 0.26939, 6, 53, 40.01, 19.21, 0.26187, 3, 32.3, 203.68, 0.00865, 14, -34.96, -207.12, 0.14425, 15, -182.12, -151.81, 0.06051, 52, 25.05, -44.81, 0.10844, 54, 20.63, 18.32, 0.41627, 6, 53, 39.47, 15.06, 0.25593, 3, 36.26, 203.42, 0.00295, 14, -38.93, -207.11, 0.05885, 15, -185.82, -150.36, 0.0239, 52, 21.08, -44.7, 0.07264, 54, 20.37, 13.88, 0.58573, 6, 53, 48.27, 1.12, 0.19412, 3, 51.72, 215.09, 0.00066, 14, -53.65, -219.69, 0.01765, 15, -204.07, -156.82, 0.00698, 52, 6.07, -56.93, 0.03618, 54, 30.15, -0.6, 0.7444, 6, 53, 45.24, -7.75, 0.13134, 3, 62.4, 212.6, 0.0006, 14, -64.47, -217.86, 0.00358, 15, -213.51, -151.22, 0.00135, 52, -4.7, -54.83, 0.02128, 54, 27.09, -10.12, 0.84184, 6, 53, 41.87, -12.99, 0.12178, 3, 68.96, 208.92, 0.00706, 14, -71.23, -214.59, 0.0007, 15, -218.64, -145.73, 0.00026, 52, -11.39, -51.4, 0.02413, 54, 23.64, -15.85, 0.84607, 6, 53, 30.07, -15.8, 0.17912, 3, 72.94, 194.56, 0.03599, 14, -76.08, -200.49, 0.00014, 15, -218.1, -130.84, 5e-05, 52, -15.89, -37.19, 0.03441, 54, 11.24, -19.3, 0.75028, 5, 53, 26.28, -14.45, 0.26275, 3, 71.01, 189.64, 0.1147, 14, -74.45, -195.46, 1e-05, 52, -14.14, -32.2, 0.03888, 54, 7.22, -17.82, 0.58366, 4, 53, 18.85, -28.49, 0.31342, 3, 86.2, 181.56, 0.26249, 52, -29.63, -24.69, 0.03353, 54, -0.4, -32.33, 0.39055, 4, 53, -2.05, -50.28, 0.29277, 3, 108.68, 159.6, 0.46665, 52, -52.89, -3.57, 0.02177, 54, -22.13, -54.46, 0.21882, 4, 53, -19.47, -76.5, 0.21188, 3, 134.32, 142.61, 0.67974, 52, -79.14, 12.47, 0.01017, 54, -39.86, -80.13, 0.09822, 4, 53, -47.75, -101.39, 0.11599, 3, 158.02, 114.97, 0.84785, 52, -103.84, 39.22, 0.00315, 54, -68.25, -104.21, 0.03301, 4, 53, -87.07, -121.82, 0.04531, 3, 177.42, 76.36, 0.9468, 52, -124.64, 77.09, 0.0005, 54, -107.34, -124.15, 0.00739, 3, 53, -109.43, -126.34, 0.01142, 3, 181.39, 54.51, 0.9878, 54, -129.51, -128.41, 0.00078, 3, 53, -146.12, -125.73, 0.00126, 3, 180.58, 17.98, 0.99873, 14, -194.24, -30.77, 1e-05, 3, 53, -186.09, -112.36, 1e-05, 3, 167.11, -21.94, 0.99963, 14, -183.22, 9.9, 0.00035, 2, 3, 144.13, -49.51, 0.99751, 14, -161.96, 38.81, 0.00249, 3, 3, 128.28, -61.99, 0.98711, 14, -146.89, 52.23, 0.01283, 15, -193.34, 130.45, 6e-05, 3, 3, 104.81, -72.83, 0.95724, 14, -124.13, 64.47, 0.04241, 15, -167.7, 133.68, 0.00036, 3, 3, 72.08, -79.62, 0.89225, 14, -91.87, 73.24, 0.10611, 15, -134.45, 130.27, 0.00164, 3, 3, 45.27, -79.24, 0.78737, 14, -65.08, 74.48, 0.20763, 15, -109, 121.8, 0.005, 3, 3, 4.53, -73.61, 0.64801, 14, -24.07, 71.34, 0.33802, 15, -71.87, 104.13, 0.01396, 3, 3, -1.62, -73.36, 0.49661, 14, -17.92, 71.46, 0.46717, 15, -66.08, 102.04, 0.03622, 3, 3, -22.69, -73.66, 0.34978, 14, 3.09, 73.04, 0.56294, 15, -45.91, 95.95, 0.08728, 3, 3, -23.27, -73.73, 0.22467, 14, 3.66, 73.15, 0.593, 15, -45.33, 95.85, 0.18233, 3, 3, -58.69, -81.57, 0.12561, 14, 38.55, 83.13, 0.54828, 15, -9.19, 92.62, 0.32611, 3, 3, -94.12, -89.41, 0.05906, 14, 73.43, 93.11, 0.44123, 15, 26.94, 89.39, 0.49971, 4, 53, -253.54, 185.78, 1e-05, 3, -131.18, -88.71, 0.0209, 14, 110.47, 94.65, 0.30778, 15, 62.06, 77.52, 0.67131], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 30, 32, 26, 28, 28, 30, 84, 86, 86, 88, 52, 54, 54, 56], "width": 416, "height": 306}}, "ear_left_white": {"ear_left_white": {"type": "mesh", "uvs": [0.68663, 0.04844, 0.75236, 0.08784, 0.82773, 0.13301, 0.9031, 0.17818, 0.95108, 0.23545, 1, 0.30885, 1, 0.33269, 0.99611, 0.35169, 0.93119, 0.74107, 0.89769, 0.81837, 0.83366, 0.88626, 0.77449, 0.93323, 0.68411, 0.97702, 0.60447, 0.99635, 0.45018, 0.99615, 0.32216, 0.9478, 0.20958, 0.86405, 0.10851, 0.73689, 0.04752, 0.6174, 0.0023, 0.48201, 0.00247, 0.37015, 0.02603, 0.30028, 0.10396, 0.18381, 0.17606, 0.12491, 0.24816, 0.06601, 0.39837, 0.00374, 0.56754, 0.00368], "triangles": [7, 4, 5, 6, 7, 5, 3, 7, 2, 7, 3, 4, 1, 2, 8, 7, 8, 2, 9, 10, 8, 10, 11, 1, 8, 10, 1, 1, 12, 0, 1, 11, 12, 12, 13, 0, 14, 15, 25, 14, 25, 26, 13, 14, 26, 13, 26, 0, 18, 21, 22, 19, 20, 21, 18, 19, 21, 23, 18, 22, 17, 18, 23, 16, 23, 24, 17, 23, 16, 15, 24, 25, 16, 24, 15], "vertices": [4, 24, -47.86, -39.27, 0.01063, 23, -25.46, -39.77, 0.08804, 22, 0.71, -39.45, 0.52271, 21, 36.25, -39.43, 0.37862, 4, 24, -56.57, -36.76, 0.00179, 23, -34.19, -37.35, 0.03803, 22, -7.99, -36.92, 0.38415, 21, 27.49, -37.1, 0.57603, 3, 23, -44.21, -34.57, 0.00882, 22, -17.97, -34.01, 0.19913, 21, 17.44, -34.44, 0.79204, 3, 23, -54.23, -31.79, 0.00067, 22, -27.95, -31.1, 0.07555, 21, 7.4, -31.77, 0.92379, 2, 22, -34.54, -26.83, 0.02779, 21, 0.7, -27.66, 0.97221, 2, 22, -41.38, -21.19, 0.00465, 21, -6.27, -22.19, 0.99535, 2, 22, -41.58, -19.16, 0.00349, 21, -6.52, -20.17, 0.99651, 2, 22, -41.24, -17.5, 0.00246, 21, -6.22, -18.5, 0.99754, 2, 22, -36.19, 16.43, 0.00824, 21, -2, 15.55, 0.99176, 3, 23, -59.52, 22.68, 0.00025, 22, -32.56, 23.43, 0.03235, 21, 1.46, 22.62, 0.9674, 4, 24, -73.68, 30.12, 4e-05, 23, -52, 29.34, 0.00487, 22, -24.96, 29.99, 0.1028, 21, 8.89, 29.38, 0.89228, 4, 24, -66.53, 34.86, 0.00158, 23, -44.91, 34.16, 0.01675, 22, -17.81, 34.73, 0.19842, 21, 15.92, 34.28, 0.78324, 4, 24, -55.39, 39.74, 0.01316, 23, -33.82, 39.15, 0.0553, 22, -6.66, 39.57, 0.35745, 21, 26.96, 39.4, 0.57408, 4, 24, -45.4, 42.39, 0.03912, 23, -23.87, 41.91, 0.11237, 22, 3.33, 42.2, 0.46033, 21, 36.88, 42.27, 0.38818, 4, 24, -25.74, 44.33, 0.16847, 23, -4.23, 44.05, 0.2637, 22, 23, 44.1, 0.44657, 21, 56.49, 44.65, 0.12126, 4, 24, -9.03, 41.83, 0.39257, 23, 12.52, 41.74, 0.31632, 22, 39.71, 41.58, 0.26586, 21, 73.26, 42.53, 0.02525, 4, 24, 6.03, 36.14, 0.68812, 23, 27.63, 36.2, 0.2095, 22, 54.75, 35.85, 0.10115, 21, 88.44, 37.17, 0.00124, 3, 24, 19.98, 26.6, 0.94121, 23, 41.68, 26.81, 0.04344, 22, 68.69, 26.28, 0.01534, 3, 24, 28.76, 17.21, 0.99875, 23, 50.56, 17.51, 0.00077, 22, 77.45, 16.88, 0.00048, 1, 24, 35.67, 6.27, 1, 1, 24, 36.59, -3.25, 1, 1, 24, 34.18, -9.49, 1, 3, 24, 25.24, -20.38, 0.97689, 23, 47.43, -20.11, 0.02251, 22, 73.85, -20.71, 0.0006, 3, 24, 16.55, -26.3, 0.86982, 23, 38.81, -26.13, 0.11567, 22, 65.15, -26.61, 0.01451, 3, 24, 7.86, -32.22, 0.6724, 23, 30.18, -32.14, 0.26295, 22, 56.45, -32.52, 0.06465, 4, 24, -10.75, -39.43, 0.2826, 23, 11.65, -39.54, 0.40802, 22, 37.82, -39.68, 0.29993, 21, 73.35, -38.75, 0.00945, 4, 24, -32.31, -41.57, 0.06241, 23, -9.89, -41.91, 0.23195, 22, 16.26, -41.78, 0.57229, 21, 51.85, -41.38, 0.13335], "hull": 27, "edges": [0, 52, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 0, 2, 2, 4, 4, 6, 44, 46, 46, 48], "width": 129, "height": 86}}, "ear_right_white": {"ear_right_white": {"type": "mesh", "uvs": [0.72902, 0.03999, 0.84833, 0.11101, 0.92359, 0.17752, 0.97488, 0.25259, 0.99732, 0.31818, 0.99738, 0.39809, 0.98306, 0.47075, 0.93175, 0.62248, 0.82669, 0.80433, 0.71281, 0.92998, 0.57872, 0.99552, 0.40671, 0.99682, 0.30595, 0.96653, 0.20338, 0.91319, 0.09504, 0.81661, 0.04449, 0.74906, 6e-05, 0.41066, 0.03873, 0.35683, 0.1595, 0.21812, 0.28128, 0.11437, 0.39319, 0.03456, 0.49235, 0.00403, 0.60955, 0.00391], "triangles": [5, 6, 3, 5, 3, 4, 2, 6, 1, 6, 2, 3, 1, 7, 0, 6, 7, 1, 7, 8, 0, 8, 9, 22, 8, 22, 0, 21, 22, 9, 10, 20, 21, 21, 9, 10, 19, 11, 12, 19, 20, 11, 20, 10, 11, 14, 15, 17, 16, 17, 15, 18, 14, 17, 12, 13, 18, 14, 18, 13, 12, 18, 19], "vertices": [4, 26, 78.83, 45.12, 0.02187, 27, 56.19, 38.24, 0.07241, 28, 35, 31.3, 0.65274, 29, 1.5, 31.85, 0.25298, 4, 26, 93.01, 39.38, 0.00246, 27, 69.49, 30.7, 0.01338, 28, 46.32, 21.01, 0.30143, 29, 13.96, 22.97, 0.68273, 4, 26, 101.95, 34, 5e-05, 27, 77.65, 24.2, 0.00129, 28, 52.85, 12.88, 0.08073, 29, 21.4, 15.66, 0.91793, 2, 28, 56.47, 5.08, 0.00874, 29, 25.91, 8.34, 0.99126, 2, 28, 57.13, -0.83, 6e-05, 29, 27.27, 2.56, 0.99994, 2, 28, 54.91, -6.89, 0.00304, 29, 25.78, -3.72, 0.99696, 2, 28, 51.28, -11.81, 0.03567, 29, 22.75, -9.04, 0.96433, 2, 28, 41.32, -21.22, 0.32251, 29, 13.97, -19.55, 0.67749, 3, 27, 59.62, -24.54, 0.00791, 28, 24.52, -30.69, 0.83579, 29, -1.59, -30.94, 0.1563, 3, 27, 44.88, -32.84, 0.11223, 28, 8.32, -35.55, 0.87461, 29, -17.11, -37.68, 0.01316, 3, 26, 60.97, -32.13, 0.01559, 27, 28.4, -36.02, 0.36108, 28, -8.46, -35.01, 0.62333, 3, 26, 40.53, -32.23, 0.17735, 27, 8.12, -33.45, 0.60663, 28, -27.67, -28.05, 0.21603, 3, 26, 28.56, -29.79, 0.41834, 27, -3.43, -29.46, 0.50964, 28, -38.06, -21.61, 0.07202, 3, 26, 16.38, -25.47, 0.74269, 27, -14.94, -23.6, 0.24724, 28, -48, -13.35, 0.01007, 2, 26, 3.51, -17.66, 0.97135, 27, -26.68, -14.17, 0.02865, 2, 26, -2.49, -12.2, 0.99828, 27, -31.92, -7.98, 0.00172, 2, 26, -7.77, 15.16, 0.99997, 28, -56.62, 33.13, 3e-05, 3, 26, -3.18, 19.51, 0.99644, 27, -28.46, 23.56, 0.00207, 28, -50.8, 35.62, 0.00148, 3, 26, 11.17, 30.73, 0.84522, 27, -12.77, 32.8, 0.1097, 28, -33.46, 41.18, 0.04508, 3, 26, 25.64, 39.11, 0.52196, 27, 2.66, 39.23, 0.29015, 28, -16.99, 44.05, 0.18789, 3, 26, 38.94, 45.56, 0.28231, 27, 16.69, 43.89, 0.32805, 28, -2.28, 45.51, 0.38964, 4, 26, 50.72, 48.03, 0.1609, 27, 28.69, 44.8, 0.27671, 28, 9.63, 43.75, 0.55818, 29, -25.16, 41.23, 0.00422, 4, 26, 64.64, 48.04, 0.07134, 27, 42.49, 42.99, 0.17382, 28, 22.69, 38.95, 0.69905, 29, -11.62, 38, 0.05578], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 120, "height": 82}}, "eye_left_white": {"eye_left_white": {"x": -2.85, "y": -2.95, "rotation": -15.85, "width": 69, "height": 84}}, "eye_right_white": {"eye_right_white": {"x": -2.85, "y": -2.95, "rotation": -15.85, "width": 69, "height": 84}}, "hair_white": {"hair_white": {"type": "mesh", "uvs": [0.45829, 0.01657, 0.4906, 0.05009, 0.49123, 0.13922, 0.6937, 0.12564, 0.79885, 0.14747, 0.86929, 0.19189, 0.86894, 0.26774, 0.83121, 0.32969, 0.79454, 0.36808, 0.95467, 0.53474, 1, 0.65989, 1, 0.70514, 0.92799, 0.78145, 0.7567, 0.73227, 0.79422, 0.83022, 0.7943, 0.96312, 0.71807, 1, 0.66675, 1, 0.57396, 0.96828, 0.45957, 0.88626, 0.40481, 0.91485, 0.35279, 0.91498, 0.29005, 0.90213, 0.19376, 0.85235, 0.08969, 0.7372, 0.03029, 0.588, 0, 0.46102, 0, 0.3779, 0.03625, 0.24587, 0.17771, 0.08034, 0.32589, 0.00384, 0.4266, 0.00375], "triangles": [15, 16, 14, 14, 16, 17, 14, 17, 18, 14, 18, 13, 18, 19, 13, 8, 13, 19, 8, 19, 2, 11, 12, 10, 10, 12, 13, 13, 8, 9, 8, 2, 3, 13, 9, 10, 7, 3, 4, 7, 8, 3, 7, 4, 6, 6, 4, 5, 20, 21, 19, 19, 21, 22, 22, 23, 19, 25, 2, 19, 28, 29, 2, 2, 30, 1, 0, 1, 31, 30, 2, 29, 1, 30, 31, 19, 23, 24, 19, 24, 25, 27, 28, 2, 2, 25, 26, 2, 26, 27], "vertices": [3, 45, 48.92, 1.18, 0.09442, 46, 14.02, 40.68, 0.86485, 47, -22.76, 34.95, 0.04073, 3, 45, 48.22, -2.79, 0.08535, 46, 16.99, 37.95, 0.86021, 47, -19.03, 33.42, 0.05443, 3, 45, 42.04, -7.2, 0.05093, 46, 17.36, 30.37, 0.79149, 47, -16.04, 26.44, 0.15758, 3, 45, 53.24, -21.13, 0.00022, 46, 35.14, 32.28, 0.35293, 47, -0.04, 34.41, 0.64685, 2, 46, 44.47, 30.81, 0.22864, 47, 9.22, 36.28, 0.77136, 2, 46, 50.83, 27.3, 0.1868, 47, 16.4, 35.2, 0.8132, 2, 46, 51.07, 20.84, 0.16746, 47, 18.88, 29.23, 0.83254, 2, 46, 47.98, 15.43, 0.14158, 47, 17.85, 23.08, 0.85842, 2, 46, 44.88, 12.03, 0.09163, 47, 16.14, 18.82, 0.90837, 1, 47, 34.64, 11.2, 1, 1, 47, 42.45, 2.92, 1, 1, 47, 43.94, -0.64, 1, 1, 47, 40.6, -9.08, 1, 3, 45, 14.17, -55.36, 4e-05, 46, 42.86, -19.09, 0.02437, 47, 25.07, -11.06, 0.97559, 2, 46, 46.52, -27.28, 0.0511, 47, 31.34, -17.47, 0.9489, 2, 46, 47, -38.59, 0.06117, 47, 35.73, -27.9, 0.93883, 3, 45, -6.44, -65.68, 0.00114, 46, 40.43, -42.01, 0.07707, 47, 30.75, -33.4, 0.9218, 3, 45, -9.04, -61.98, 0.00333, 46, 35.91, -42.2, 0.09529, 47, 26.58, -35.15, 0.90138, 3, 45, -11.52, -53.74, 0.01517, 46, 27.62, -39.84, 0.16242, 47, 17.99, -35.82, 0.82241, 3, 45, -11.61, -41.48, 0.09271, 46, 17.26, -33.29, 0.38391, 47, 6, -33.28, 0.52338, 3, 45, -16.37, -38.93, 0.15759, 46, 12.54, -35.93, 0.46411, 47, 2.49, -37.4, 0.3783, 3, 45, -19.01, -35.18, 0.20342, 46, 7.96, -36.14, 0.49119, 47, -1.73, -39.18, 0.30539, 3, 45, -21.29, -30.03, 0.27261, 46, 2.39, -35.27, 0.50059, 47, -7.25, -40.31, 0.2268, 3, 45, -22.71, -20.65, 0.42309, 46, -6.26, -31.4, 0.45472, 47, -16.71, -39.69, 0.1222, 3, 45, -19.96, -7.52, 0.71486, 46, -15.84, -21.99, 0.25402, 47, -28.96, -34.2, 0.03112, 3, 45, -12.57, 4.07, 0.9801, 46, -21.6, -9.52, 0.01897, 47, -38.7, -24.51, 0.00093, 2, 45, -5.26, 12.47, 0.99167, 46, -24.73, 1.17, 0.00833, 2, 45, 0.53, 16.54, 0.91983, 46, -25.02, 8.24, 0.08017, 2, 45, 11.57, 20.38, 0.6869, 46, -22.31, 19.61, 0.3131, 2, 45, 30.26, 18.29, 0.30515, 46, -10.45, 34.21, 0.69485, 3, 45, 43.1, 11.35, 0.14879, 46, 2.32, 41.27, 0.83942, 47, -33.94, 31.43, 0.01179, 3, 45, 48.21, 4.09, 0.10298, 46, 11.18, 41.65, 0.86443, 47, -25.76, 34.87, 0.03259], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 89, "height": 86}}, "head_white": {"head_white": {"type": "mesh", "uvs": [0.47794, 0, 0.56845, 0.00359, 0.67399, 0.05298, 0.7512, 0.11463, 0.80012, 0.18554, 0.83768, 0.2738, 0.85461, 0.40901, 0.86611, 0.44705, 0.88043, 0.47303, 0.89102, 0.48359, 0.95827, 0.52398, 0.99817, 0.60324, 1, 0.67941, 0.97892, 0.75245, 0.91886, 0.82979, 0.83877, 0.87768, 0.75375, 0.8958, 0.72453, 0.93204, 0.64614, 0.98821, 0.57036, 1, 0.52853, 1, 0.45232, 0.97887, 0.379, 0.93211, 0.36528, 0.91803, 0.23355, 0.89593, 0.14021, 0.84948, 0.08176, 0.80065, 0.02481, 0.72104, 0, 0.62427, 0, 0.58136, 0.00546, 0.53647, 0.05847, 0.27718, 0.08263, 0.17777, 0.12059, 0.12686, 0.16705, 0.08751, 0.27398, 0.02522, 0.41872, 0, 0.33753, 0.61414, 0.40921, 0.54026, 0.50138, 0.49593, 0.6072, 0.47093, 0.74358, 0.61037, 0.76418, 0.71685], "triangles": [40, 1, 2, 40, 2, 3, 40, 3, 4, 40, 4, 5, 40, 5, 6, 40, 39, 0, 40, 0, 1, 36, 0, 39, 35, 36, 39, 34, 35, 39, 38, 34, 39, 31, 33, 34, 31, 32, 33, 34, 38, 31, 37, 30, 31, 7, 41, 40, 7, 40, 6, 41, 7, 8, 41, 8, 9, 38, 37, 31, 29, 30, 37, 28, 29, 37, 41, 11, 42, 10, 41, 9, 11, 41, 10, 42, 11, 12, 27, 28, 37, 13, 42, 12, 26, 27, 37, 14, 42, 13, 25, 26, 37, 15, 42, 14, 39, 41, 42, 16, 42, 15, 24, 25, 37, 42, 23, 37, 24, 37, 23, 39, 40, 41, 42, 38, 39, 37, 38, 42, 16, 17, 42, 42, 22, 23, 42, 21, 22, 21, 42, 20, 20, 42, 19, 19, 42, 17, 18, 19, 17], "vertices": [184.57, 77.78, 198.9, 55.55, 204.8, 22.02, 203.02, -6.79, 194.26, -30.4, 179.46, -54.21, 149.95, -80.98, 142.78, -90.12, 138.97, -97.91, 138.22, -102.21, 139.85, -125.07, 127.58, -147.93, 109.67, -161.17, 88.65, -168.41, 60.07, -167.06, 35.16, -155.96, 16.56, -138.69, 2.98, -137.8, -23.62, -128.51, -39.16, -112.38, -46.18, -102.39, -53.92, -80.62, -55.05, -55.24, -53.98, -49.6, -70.81, -14.4, -75.37, 15.71, -73.5, 37.89, -64.01, 64.88, -45.03, 87.07, -34.76, 94.28, -23.1, 100.52, 47.83, 131.43, 75.67, 142.37, 94.22, 141.85, 111.43, 137.37, 144.29, 122.28, 174.63, 91.93, 14.06, 8.11, 43.77, 3.4, 69.85, -11.18, 93.6, -32.26, 83.14, -88.29, 61.12, -111.11], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 76, 78, 78, 80], "width": 293, "height": 293}}, "horn_left_white": {"horn_left_white": {"x": 31.51, "y": -8.65, "rotation": -129.68, "width": 73, "height": 91}}, "horn_right_white": {"horn_right_white": {"x": 22.89, "y": 8.39, "rotation": -62.73, "width": 68, "height": 92}}, "inner_eye_left_white": {"inner_eye_left_white": {"x": -1.07, "y": -0.22, "rotation": -54.91, "width": 39, "height": 49}}, "inner_eye_right_white": {"inner_eye_right_white": {"x": -1.07, "y": -0.22, "rotation": -54.91, "width": 39, "height": 49}}, "leg_back_left_white": {"leg_back_left_white": {"type": "mesh", "uvs": [0.62929, 0.02251, 0.66765, 0.03474, 0.76418, 0.07329, 0.83804, 0.15552, 0.85044, 0.24705, 0.83016, 0.32626, 0.78264, 0.40929, 0.79132, 0.45409, 0.80001, 0.49889, 0.81145, 0.53055, 0.84276, 0.5961, 0.87408, 0.66166, 0.88493, 0.68021, 0.91303, 0.71597, 0.98263, 0.80454, 1, 0.86274, 1, 0.88982, 0.98667, 0.92323, 0.93258, 0.95909, 0.83942, 0.98518, 0.71854, 0.99817, 0.51305, 0.99806, 0.38003, 0.98634, 0.26056, 0.96174, 0.18437, 0.92048, 0.14655, 0.87568, 0.11724, 0.77736, 0.08792, 0.67904, 0.07191, 0.57915, 0.05591, 0.47926, 0.02986, 0.39509, 0.0038, 0.31092, 0.00215, 0.23065, 0.03646, 0.14763, 0.08571, 0.08175, 0.16126, 0.03879, 0.24186, 0.01248, 0.3347, 0.00192, 0.48317, 0.00197, 0.44408, 0.30059], "triangles": [19, 20, 13, 13, 20, 21, 13, 21, 22, 14, 18, 19, 22, 12, 13, 15, 17, 18, 16, 17, 15, 18, 14, 15, 19, 13, 14, 26, 12, 22, 23, 24, 25, 26, 22, 23, 12, 26, 11, 25, 26, 23, 11, 27, 10, 11, 26, 27, 27, 9, 10, 9, 28, 8, 8, 28, 7, 6, 7, 39, 29, 39, 7, 9, 27, 28, 7, 28, 29, 6, 39, 5, 29, 30, 39, 30, 31, 39, 5, 39, 4, 31, 32, 39, 32, 33, 39, 33, 34, 39, 34, 35, 39, 35, 36, 39, 36, 37, 39, 39, 3, 4, 39, 2, 3, 39, 1, 2, 39, 0, 1, 39, 38, 0, 39, 37, 38], "vertices": [1, 32, 27.27, -2.01, 1, 2, 32, 27.33, 2.08, 0.99968, 33, -40.74, 36.62, 0.00032, 2, 32, 28.61, 12.98, 0.98715, 33, -32.79, 44.19, 0.01285, 2, 32, 37.24, 25.56, 0.91156, 33, -18.1, 48.35, 0.08844, 2, 32, 49.98, 33.92, 0.73113, 33, -2.96, 46.64, 0.26887, 2, 32, 62.39, 38.65, 0.48826, 33, 9.61, 42.35, 0.51174, 3, 32, 76.57, 41.49, 0.18748, 33, 22.33, 35.46, 0.81184, 34, -26.02, 67.01, 0.00069, 3, 32, 82.69, 45.79, 0.08035, 33, 29.78, 34.86, 0.91425, 34, -20.62, 61.83, 0.0054, 3, 32, 88.81, 50.09, 0.02821, 33, 37.24, 34.26, 0.95229, 34, -15.22, 56.65, 0.0195, 3, 32, 92.89, 53.57, 0.01057, 33, 42.6, 34.32, 0.94923, 34, -11.04, 53.31, 0.0402, 3, 32, 101.01, 61.37, 0.00025, 33, 53.83, 35.14, 0.87282, 34, -1.82, 46.84, 0.12693, 2, 33, 65.06, 35.95, 0.69687, 34, 7.4, 40.37, 0.30313, 2, 33, 68.27, 36.36, 0.62439, 34, 10.14, 38.65, 0.37561, 2, 33, 74.59, 37.8, 0.46996, 34, 15.95, 35.78, 0.53004, 2, 33, 90.23, 41.38, 0.1602, 34, 30.33, 28.66, 0.8398, 2, 33, 100.02, 41.15, 0.06128, 34, 37.77, 22.29, 0.93872, 2, 33, 104.44, 40.32, 0.0351, 34, 40.66, 18.85, 0.9649, 2, 33, 109.66, 38.07, 0.01554, 34, 43.29, 13.81, 0.98446, 2, 33, 114.57, 32.05, 0.00277, 34, 43.29, 6.04, 0.99723, 1, 34, 39.48, -2.81, 1, 1, 34, 32.32, -11.65, 1, 2, 33, 113.7, -7.24, 0.03881, 34, 17.77, -23.85, 0.96119, 2, 33, 109.49, -18.95, 0.20799, 34, 7.1, -30.26, 0.79201, 2, 33, 103.42, -29.04, 0.42401, 34, -3.97, -34.24, 0.57599, 2, 33, 95.38, -34.68, 0.58861, 34, -13.77, -33.52, 0.41139, 2, 33, 87.42, -36.72, 0.71819, 34, -21.23, -30.07, 0.28181, 3, 32, 159.81, 17.29, 0.00243, 33, 70.88, -36.34, 0.93368, 34, -33.8, -19.32, 0.06389, 3, 32, 146.84, 7.02, 0.02562, 33, 54.34, -35.96, 0.97301, 34, -46.37, -8.56, 0.00137, 2, 32, 133.05, -2.31, 0.11653, 33, 37.78, -34.32, 0.88347, 2, 32, 119.26, -11.63, 0.36324, 33, 21.22, -32.68, 0.63676, 2, 32, 108.2, -20.51, 0.66884, 33, 7.04, -32.44, 0.33116, 2, 32, 97.15, -29.38, 0.871, 33, -7.14, -32.19, 0.129, 2, 32, 85.57, -35.97, 0.95571, 33, -20.26, -29.86, 0.04429, 2, 32, 71.97, -39.87, 0.99365, 33, -33.2, -24.18, 0.00635, 1, 32, 60.2, -41.19, 1, 1, 32, 50.58, -38.53, 1, 1, 32, 43.15, -34.13, 1, 1, 32, 37.46, -27.48, 1, 1, 32, 30.83, -15.47, 1, 2, 32, 75.94, 5.38, 0.61488, 33, -1.23, 8.09, 0.38512], "hull": 39, "edges": [0, 76, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 12, 14, 14, 16, 58, 60, 60, 62, 54, 56, 56, 58, 50, 52, 52, 54, 18, 20, 20, 22, 24, 26, 26, 28], "width": 93, "height": 167}}, "leg_back_right_white": {"leg_back_right_white": {"type": "mesh", "uvs": [0.62929, 0.02251, 0.66765, 0.03474, 0.76418, 0.07329, 0.83804, 0.15552, 0.85044, 0.24705, 0.83016, 0.32626, 0.78264, 0.40929, 0.79132, 0.45409, 0.80001, 0.49889, 0.81145, 0.53055, 0.84276, 0.5961, 0.87408, 0.66166, 0.88493, 0.68021, 0.91303, 0.71597, 0.98263, 0.80454, 1, 0.86274, 1, 0.88982, 0.98667, 0.92323, 0.93258, 0.95909, 0.83942, 0.98518, 0.71854, 0.99817, 0.51305, 0.99806, 0.38003, 0.98634, 0.26056, 0.96174, 0.18437, 0.92048, 0.14655, 0.87568, 0.11724, 0.77736, 0.08792, 0.67904, 0.07191, 0.57915, 0.05591, 0.47926, 0.02986, 0.39509, 0.0038, 0.31092, 0.00215, 0.23065, 0.03646, 0.14763, 0.08571, 0.08175, 0.16126, 0.03879, 0.24186, 0.01248, 0.3347, 0.00192, 0.48317, 0.00197, 0.44408, 0.30059], "triangles": [19, 20, 13, 13, 20, 21, 13, 21, 22, 14, 18, 19, 22, 12, 13, 15, 17, 18, 16, 17, 15, 18, 14, 15, 19, 13, 14, 26, 12, 22, 23, 24, 25, 26, 22, 23, 12, 26, 11, 25, 26, 23, 11, 27, 10, 11, 26, 27, 27, 9, 10, 9, 28, 8, 8, 28, 7, 9, 27, 28, 7, 28, 29, 6, 7, 39, 29, 39, 7, 29, 30, 39, 6, 39, 5, 30, 31, 39, 5, 39, 4, 31, 32, 39, 32, 33, 39, 33, 34, 39, 34, 35, 39, 35, 36, 39, 36, 37, 39, 39, 3, 4, 39, 2, 3, 39, 1, 2, 39, 0, 1, 39, 38, 0, 39, 37, 38], "vertices": [2, 36, 15.38, -2.51, 0.99896, 37, -50.35, 44.02, 0.00104, 1, 36, 15.68, 1.56, 1, 1, 36, 17.62, 12.37, 1, 2, 36, 26.99, 24.41, 0.99691, 37, -23.45, 55.69, 0.00309, 2, 36, 40.2, 31.99, 0.9577, 37, -8.63, 52.17, 0.0423, 2, 36, 52.88, 35.97, 0.84382, 37, 3.33, 46.39, 0.15618, 3, 36, 67.2, 37.95, 0.54336, 37, 15.12, 38.02, 0.45617, 38, -19.04, 66.42, 0.00047, 3, 36, 73.57, 41.88, 0.33828, 37, 22.45, 36.53, 0.65721, 38, -14.45, 60.51, 0.00452, 3, 36, 79.93, 45.81, 0.18551, 37, 29.78, 35.03, 0.79663, 38, -9.86, 54.61, 0.01786, 3, 36, 84.22, 49.03, 0.10852, 37, 35.11, 34.45, 0.853, 38, -6.19, 50.69, 0.03849, 3, 36, 92.79, 56.33, 0.02701, 37, 46.36, 33.9, 0.84411, 38, 1.98, 42.95, 0.12888, 3, 36, 101.36, 63.63, 0.00308, 37, 57.6, 33.35, 0.67453, 38, 10.16, 35.21, 0.32239, 3, 36, 103.71, 65.86, 0.00108, 37, 60.84, 33.37, 0.59491, 38, 12.64, 33.12, 0.404, 2, 37, 67.29, 34.04, 0.42236, 38, 17.97, 29.44, 0.57764, 2, 37, 83.25, 35.7, 0.09853, 38, 31.16, 20.31, 0.90147, 2, 37, 92.94, 34.3, 0.02151, 38, 37.6, 12.93, 0.97849, 2, 37, 97.22, 32.93, 0.00712, 38, 39.97, 9.11, 0.99288, 2, 37, 102.13, 30.07, 0.00045, 38, 41.83, 3.74, 0.99955, 1, 38, 40.71, -3.95, 1, 1, 38, 35.66, -12.16, 1, 2, 37, 106.46, 2.69, 0.00444, 38, 27.29, -19.86, 0.99556, 2, 37, 100.67, -15.39, 0.19944, 38, 11.12, -29.82, 0.80056, 3, 36, 169.67, 45.68, 6e-05, 37, 95.09, -26.52, 0.45557, 38, -0.35, -34.63, 0.54437, 3, 36, 170.74, 33.96, 0.00139, 37, 87.84, -35.79, 0.66019, 38, -11.89, -36.95, 0.33842, 3, 36, 167.6, 24.65, 0.00482, 37, 79.18, -40.42, 0.78145, 38, -21.48, -34.82, 0.21373, 3, 36, 162.39, 18.29, 0.01054, 37, 71.03, -41.49, 0.85893, 38, -28.36, -30.33, 0.13053, 3, 36, 148.84, 8.81, 0.04364, 37, 54.66, -39.11, 0.93853, 38, -39.24, -17.87, 0.01783, 2, 36, 135.28, -0.67, 0.13471, 37, 38.29, -36.74, 0.86529, 2, 36, 120.95, -9.15, 0.34015, 37, 22.04, -33.11, 0.65985, 2, 36, 106.63, -17.63, 0.59317, 37, 5.79, -29.48, 0.40683, 2, 36, 95.06, -25.83, 0.47225, 37, -8.25, -27.54, 0.52775, 2, 36, 83.5, -34.02, 0.28257, 37, -22.29, -25.58, 0.71743, 2, 36, 71.54, -39.9, 0.33047, 37, -35.03, -21.68, 0.66953, 2, 36, 57.74, -42.99, 0.4976, 37, -47.2, -14.48, 0.5024, 2, 36, 45.91, -43.59, 0.63462, 37, -56.24, -6.82, 0.36538, 2, 36, 36.47, -40.37, 0.73194, 37, -60.91, 2, 0.26806, 2, 36, 29.31, -35.53, 0.8071, 37, -62.81, 10.42, 0.1929, 2, 36, 24.03, -28.55, 0.87526, 37, -61.87, 19.12, 0.12474, 2, 36, 18.13, -16.16, 0.96196, 37, -57.7, 32.19, 0.03804, 1, 36, 64.41, 1.94, 1], "hull": 39, "edges": [0, 76, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 12, 14, 14, 16, 58, 60, 60, 62, 54, 56, 56, 58, 50, 52, 52, 54, 18, 20, 20, 22, 24, 26, 26, 28], "width": 93, "height": 167}}, "leg_front_left_white": {"leg_front_left_white": {"type": "mesh", "uvs": [0.32757, 0.00208, 0.50461, 0.00191, 0.6393, 0.02767, 0.75276, 0.07538, 0.82088, 0.1345, 0.84823, 0.19708, 0.83456, 0.30521, 0.80735, 0.44266, 0.81304, 0.47622, 0.83012, 0.53254, 0.84246, 0.56724, 0.87803, 0.6393, 0.97121, 0.77187, 0.99656, 0.8318, 0.99655, 0.89566, 0.96532, 0.93183, 0.90826, 0.96279, 0.81591, 0.98599, 0.67281, 0.99789, 0.49182, 0.99831, 0.35623, 0.99156, 0.21486, 0.95955, 0.12598, 0.90821, 0.08987, 0.84904, 0.07475, 0.80217, 0.06015, 0.70391, 0.05186, 0.58442, 0.04493, 0.48453, 0.03736, 0.37699, 0.00133, 0.23053, 0.0045, 0.16927, 0.03765, 0.11167, 0.09655, 0.06458, 0.18373, 0.02417], "triangles": [11, 12, 22, 12, 13, 22, 21, 22, 20, 14, 15, 13, 13, 20, 22, 15, 16, 13, 16, 17, 13, 20, 13, 19, 17, 18, 13, 18, 19, 13, 5, 3, 4, 30, 32, 29, 32, 28, 29, 27, 3, 6, 5, 6, 3, 30, 31, 32, 3, 27, 28, 3, 0, 1, 32, 33, 0, 32, 0, 28, 2, 3, 1, 27, 6, 26, 28, 0, 3, 7, 26, 6, 25, 26, 10, 8, 26, 7, 9, 26, 8, 10, 26, 9, 25, 10, 11, 24, 25, 11, 23, 24, 11, 22, 23, 11], "vertices": [1, 40, -28.8, -15.79, 1, 1, 40, -29.64, 0.38, 1, 1, 40, -26.19, 12.88, 1, 1, 40, -19.16, 23.62, 1, 1, 40, -10.12, 30.31, 1, 1, 40, -0.35, 33.3, 1, 1, 40, 16.82, 32.92, 1, 2, 40, 38.69, 31.53, 0.99252, 41, -41.26, 52.67, 0.00748, 2, 40, 43.98, 32.32, 0.98115, 41, -36.11, 51.26, 0.01885, 2, 40, 52.81, 34.33, 0.9423, 41, -27.22, 49.53, 0.0577, 2, 40, 58.24, 35.73, 0.89914, 41, -21.68, 48.63, 0.10086, 2, 40, 69.47, 39.55, 0.74538, 41, -9.86, 47.59, 0.25462, 2, 40, 90.02, 49.12, 0.34972, 41, 12.8, 48.04, 0.65028, 2, 40, 99.38, 51.91, 0.22519, 41, 22.49, 46.82, 0.77481, 2, 40, 109.48, 52.42, 0.14022, 41, 31.94, 43.2, 0.85978, 2, 40, 115.35, 49.86, 0.10609, 41, 36.27, 38.49, 0.89391, 2, 40, 120.51, 44.9, 0.07431, 41, 38.99, 31.86, 0.92569, 2, 40, 124.61, 36.65, 0.03958, 41, 39.41, 22.67, 0.96042, 2, 40, 127.15, 23.68, 0.00518, 41, 36.5, 9.78, 0.99482, 1, 41, 30.65, -5.7, 1, 2, 40, 127.6, -5.27, 0.00554, 41, 25.23, -16.89, 0.99446, 2, 40, 123.19, -18.43, 0.10975, 41, 15.88, -27.15, 0.89025, 2, 40, 115.48, -26.96, 0.29383, 41, 5.38, -31.84, 0.70617, 2, 40, 106.29, -30.73, 0.51554, 41, -4.55, -31.57, 0.48446, 2, 40, 98.94, -32.48, 0.69463, 41, -11.98, -30.21, 0.30537, 2, 40, 83.46, -34.6, 0.94614, 41, -27, -25.9, 0.05386, 1, 40, 64.6, -36.31, 1, 1, 40, 48.83, -37.74, 1, 1, 40, 31.85, -39.29, 1, 1, 40, 8.84, -43.74, 1, 1, 40, -0.86, -43.95, 1, 1, 40, -10.12, -41.38, 1, 1, 40, -17.85, -36.38, 1, 1, 40, -24.64, -28.74, 1], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 50, 52, 52, 54, 54, 56], "width": 92, "height": 159}}, "leg_front_right_white": {"leg_front_right_white": {"type": "mesh", "uvs": [0.32757, 0.00208, 0.50461, 0.00191, 0.6393, 0.02767, 0.75276, 0.07538, 0.82088, 0.1345, 0.84823, 0.19708, 0.83456, 0.30521, 0.80735, 0.44266, 0.81304, 0.47622, 0.83012, 0.53254, 0.84246, 0.56724, 0.87803, 0.6393, 0.97121, 0.77187, 0.99656, 0.8318, 0.99655, 0.89566, 0.96532, 0.93183, 0.90826, 0.96279, 0.81591, 0.98599, 0.67281, 0.99789, 0.49182, 0.99831, 0.35623, 0.99156, 0.21486, 0.95955, 0.12598, 0.90821, 0.08987, 0.84904, 0.07475, 0.80217, 0.06015, 0.70391, 0.05186, 0.58442, 0.04493, 0.48453, 0.03736, 0.37699, 0.00133, 0.23053, 0.0045, 0.16927, 0.03765, 0.11167, 0.09655, 0.06458, 0.18373, 0.02417], "triangles": [18, 19, 13, 17, 18, 13, 20, 13, 19, 16, 17, 13, 15, 16, 13, 13, 20, 22, 14, 15, 13, 21, 22, 20, 12, 13, 22, 11, 12, 22, 22, 23, 11, 23, 24, 11, 24, 25, 11, 25, 10, 11, 10, 26, 9, 9, 26, 8, 8, 26, 7, 25, 26, 10, 7, 26, 6, 28, 0, 3, 27, 6, 26, 2, 3, 1, 32, 0, 28, 32, 33, 0, 3, 0, 1, 3, 27, 28, 30, 31, 32, 5, 6, 3, 27, 3, 6, 32, 28, 29, 30, 32, 29, 5, 3, 4], "vertices": [1, 43, -28.02, -13.36, 1, 1, 43, -28.3, 2.82, 1, 1, 43, -24.41, 15.2, 1, 1, 43, -17.01, 25.68, 1, 1, 43, -7.75, 32.06, 1, 1, 43, 2.13, 34.71, 1, 1, 43, 19.27, 33.73, 1, 2, 43, 41.08, 31.58, 0.99242, 44, -27.67, 58.64, 0.00758, 2, 43, 46.38, 32.18, 0.98026, 44, -23, 56.04, 0.01974, 2, 43, 55.28, 33.88, 0.9382, 44, -14.78, 52.25, 0.0618, 2, 43, 60.76, 35.1, 0.89126, 44, -9.62, 50.04, 0.10874, 2, 43, 72.12, 38.53, 0.72378, 44, 1.61, 46.22, 0.27622, 2, 43, 92.98, 47.37, 0.29534, 44, 23.73, 41.27, 0.70466, 2, 43, 102.44, 49.84, 0.16954, 44, 32.85, 37.77, 0.83046, 2, 43, 112.55, 49.99, 0.08896, 44, 41.17, 32.01, 0.91104, 2, 43, 118.33, 47.23, 0.05905, 44, 44.25, 26.4, 0.94095, 2, 43, 123.31, 42.09, 0.0337, 44, 45.32, 19.32, 0.9663, 2, 43, 127.12, 33.71, 0.01086, 44, 43.53, 10.29, 0.98914, 1, 44, 37.64, -1.53, 1, 1, 44, 28.27, -15.17, 1, 2, 43, 128.66, -8.29, 0.03276, 44, 20.34, -24.76, 0.96724, 2, 43, 123.79, -21.29, 0.18501, 44, 8.82, -32.5, 0.81499, 2, 43, 115.78, -29.54, 0.37728, 44, -2.49, -34.55, 0.62272, 2, 43, 106.46, -32.99, 0.57602, 44, -12.08, -31.93, 0.42398, 2, 43, 99.06, -34.49, 0.73435, 44, -18.97, -28.84, 0.26565, 2, 43, 83.52, -36.07, 0.95201, 44, -32.53, -21.07, 0.04799, 1, 43, 64.61, -37.12, 1, 1, 43, 48.8, -38, 1, 1, 43, 31.77, -38.96, 1, 1, 43, 8.63, -42.61, 1, 1, 43, -1.08, -42.47, 1, 1, 43, -10.25, -39.59, 1, 1, 43, -17.79, -34.32, 1, 1, 43, -24.31, -26.45, 1], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 50, 52, 52, 54, 54, 56], "width": 92, "height": 159}}, "mouth_white": {"mouth_white": {"type": "mesh", "uvs": [0.09857, 0, 0.16168, 0.05044, 0.16205, 0.07309, 0.07753, 0.09776, 0.15145, 0.28697, 0.20856, 0.38361, 0.25715, 0.44311, 0.3492, 0.52002, 0.39878, 0.54626, 0.49893, 0.5706, 0.56595, 0.56192, 0.65477, 0.52639, 0.74358, 0.49087, 0.8332, 0.42784, 0.9023, 0.35082, 0.95156, 0.26818, 0.98472, 0.1703, 0.99773, 0.16915, 0.9976, 0.26883, 0.97571, 0.35699, 0.92691, 0.4553, 0.88511, 0.49566, 0.81922, 0.55929, 0.7581, 0.61831, 0.73842, 0.66088, 0.71507, 0.7403, 0.69171, 0.81972, 0.65741, 0.88962, 0.62122, 0.94223, 0.5734, 0.98041, 0.52095, 0.99527, 0.44442, 0.99597, 0.35836, 0.94075, 0.30223, 0.87795, 0.24759, 0.78293, 0.21248, 0.70153, 0.16737, 0.58581, 0.14236, 0.49998, 0.10734, 0.37983, 0.08804, 0.3136, 0.076, 0.24742, 0.06396, 0.18123, 0.05574, 0.11861, 0.04439, 0.16379, 0.02803, 0.21647, 0.01448, 0.24462, 0.00236, 0.24383, 0.00231, 0.12225, 0.03477, 0.02321, 0.07791, 0, 0.11181, 0.32409, 0.14516, 0.40335, 0.1877, 0.48163, 0.25053, 0.56088, 0.32518, 0.61297, 0.40693, 0.66117, 0.49503, 0.67402, 0.56964, 0.6692, 0.66726, 0.65153], "triangles": [26, 58, 25, 27, 58, 26, 33, 34, 54, 32, 33, 55, 31, 56, 30, 32, 55, 31, 29, 30, 57, 29, 57, 28, 28, 57, 27, 34, 35, 53, 35, 36, 53, 55, 54, 8, 34, 53, 54, 36, 52, 53, 38, 39, 50, 52, 51, 5, 53, 6, 7, 54, 53, 7, 51, 50, 4, 51, 4, 5, 40, 3, 4, 3, 48, 49, 3, 49, 0, 2, 3, 0, 2, 0, 1, 42, 48, 3, 47, 48, 42, 43, 47, 42, 41, 42, 3, 40, 41, 3, 18, 16, 17, 15, 16, 18, 19, 15, 18, 14, 15, 19, 20, 14, 19, 21, 13, 14, 20, 21, 14, 22, 12, 13, 22, 13, 21, 23, 12, 22, 58, 11, 12, 58, 12, 23, 24, 58, 23, 58, 57, 10, 58, 10, 11, 56, 55, 9, 57, 56, 9, 57, 9, 10, 25, 58, 24, 27, 57, 58, 30, 56, 57, 44, 47, 43, 50, 39, 40, 4, 50, 40, 52, 5, 6, 55, 8, 9, 54, 7, 8, 53, 52, 6, 38, 50, 51, 37, 38, 51, 37, 51, 52, 46, 47, 44, 45, 46, 44, 36, 37, 52, 33, 54, 55, 31, 55, 56], "vertices": [1, 55, -24.66, -53.5, 1, 1, 55, -21.09, -44.92, 1, 1, 55, -19.55, -44.9, 1, 1, 55, -18.1, -56.5, 1, 1, 55, -5.12, -46.62, 1, 1, 55, 1.57, -38.93, 1, 1, 55, 5.72, -32.35, 1, 1, 55, 11.16, -19.84, 1, 1, 55, 13.06, -13.09, 1, 1, 55, 14.97, 0.59, 1, 1, 55, 14.55, 9.78, 1, 1, 55, 12.38, 21.99, 1, 1, 55, 10.21, 34.19, 1, 1, 55, 6.17, 46.55, 1, 1, 55, 1.15, 56.11, 1, 1, 55, -4.32, 62.96, 1, 1, 55, -10.85, 67.62, 1, 1, 55, -10.9, 69.41, 1, 1, 55, -4.15, 69.26, 1, 1, 55, 1.75, 66.15, 1, 1, 55, 8.28, 59.34, 1, 1, 55, 10.9, 53.57, 1, 2, 55, 15.04, 44.46, 0.92285, 56, -12.09, 48.1, 0.07715, 2, 55, 19.19, 35.97, 0.8261, 56, -7.16, 40.04, 0.1739, 2, 55, 23.45, 33, 0.53362, 56, -2.64, 37.49, 0.46638, 2, 55, 30.95, 29.37, 0.17156, 56, 5.17, 34.59, 0.82844, 1, 56, 13.09, 31.68, 1, 1, 56, 19.57, 27.28, 1, 1, 56, 24.01, 22.58, 1, 1, 56, 27.09, 16.25, 1, 1, 56, 28.64, 9.16, 1, 1, 56, 29.5, -1.28, 1, 1, 56, 26.67, -13.32, 1, 2, 55, 43.11, -27.94, 0.03271, 56, 22.76, -21.29, 0.96729, 2, 55, 35.45, -35.13, 0.06906, 56, 15.81, -29.19, 0.93094, 2, 55, 27.84, -39.53, 0.12904, 56, 8.66, -34.29, 0.87096, 2, 55, 17.52, -45.19, 0.3023, 56, -1.07, -40.91, 0.6977, 2, 55, 10.04, -48.26, 0.76939, 56, -8.22, -44.68, 0.23061, 2, 55, 1.12, -52.79, 0.99176, 56, -16.67, -50.04, 0.00824, 1, 55, -3.47, -55.34, 1, 1, 55, -7.98, -56.9, 1, 1, 55, -12.49, -58.47, 1, 1, 55, -16.75, -59.51, 1, 1, 55, -13.72, -61.13, 1, 1, 55, -10.2, -63.43, 1, 1, 55, -8.33, -65.32, 1, 1, 55, -8.42, -66.98, 1, 1, 55, -16.64, -66.83, 1, 1, 55, -23.26, -62.26, 1, 1, 55, -24.71, -56.32, 1, 1, 55, -2.71, -52.1, 1, 1, 55, 2.74, -47.63, 1, 1, 55, 8.14, -41.91, 1, 1, 55, 13.67, -33.41, 1, 1, 55, 17.38, -23.25, 1, 1, 55, 20.86, -12.12, 1, 1, 55, 21.95, -0.07, 1, 1, 55, 21.82, 10.15, 1, 2, 55, 20.89, 23.54, 0.99806, 56, -4.28, 27.83, 0.00194], "hull": 50, "edges": [0, 98, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 46, 48, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 86, 84, 84, 82, 78, 80, 80, 82, 76, 78, 74, 76, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 46, 66, 68, 62, 64, 66, 64, 60, 62, 58, 60, 56, 58, 52, 54, 56, 54, 48, 50, 50, 52, 44, 46, 40, 42, 42, 44, 20, 22, 22, 24, 72, 74, 68, 70, 72, 70], "width": 138, "height": 69}}, "oxtail_white": {"oxtail_white": {"type": "mesh", "uvs": [0.67359, 0, 0.71724, 0.01837, 0.68769, 0.09287, 0.68692, 0.11913, 0.72103, 0.18161, 0.75514, 0.24409, 0.7846, 0.29807, 0.75642, 0.38674, 0.64767, 0.46394, 0.44896, 0.5193, 0.38653, 0.5586, 0.36077, 0.59673, 0.33501, 0.63487, 0.33489, 0.66088, 0.34893, 0.70468, 0.36005, 0.72267, 0.4031, 0.75879, 0.47504, 0.78985, 0.56889, 0.81267, 0.60284, 0.81871, 0.75635, 0.83654, 0.90986, 0.85436, 0.99089, 0.89031, 1, 0.9229, 1, 0.93077, 0.96185, 0.96353, 0.9078, 0.98689, 0.83241, 0.99828, 0.66277, 0.99824, 0.47387, 0.98331, 0.35693, 0.96396, 0.27333, 0.94073, 0.23806, 0.9255, 0.16753, 0.89503, 0.11662, 0.86059, 0.08468, 0.83897, 0.05634, 0.80957, 0.0392, 0.77929, 0.01806, 0.74196, 0.01254, 0.70703, 0.01239, 0.67831, 0.01222, 0.64491, 0.02337, 0.60853, 0.05936, 0.54982, 0.0994, 0.51527, 0.13945, 0.48072, 0.03411, 0.4109, 0.0042, 0.35782, 0.00401, 0.27595, 0.04109, 0.2155, 0.09576, 0.16835, 0.19247, 0.1182, 0.3019, 0.08104, 0.42137, 0.05173, 0.63228, 0], "triangles": [23, 21, 22, 23, 25, 21, 24, 25, 23, 29, 30, 18, 21, 27, 20, 25, 27, 21, 28, 19, 20, 27, 28, 20, 29, 19, 28, 27, 25, 26, 17, 33, 34, 17, 34, 16, 32, 33, 17, 31, 32, 17, 18, 30, 31, 18, 31, 17, 29, 18, 19, 44, 45, 10, 11, 44, 10, 43, 44, 11, 12, 42, 43, 11, 12, 43, 41, 42, 12, 13, 41, 12, 40, 41, 13, 14, 39, 40, 14, 40, 13, 38, 39, 14, 15, 37, 38, 15, 38, 14, 16, 36, 37, 16, 37, 15, 35, 36, 16, 34, 35, 16, 7, 47, 48, 5, 49, 50, 7, 46, 47, 48, 49, 7, 6, 49, 5, 4, 5, 50, 6, 7, 49, 8, 46, 7, 45, 46, 8, 9, 45, 8, 10, 45, 9, 1, 54, 0, 1, 2, 54, 2, 53, 54, 3, 53, 2, 52, 53, 3, 4, 51, 52, 3, 4, 52, 4, 50, 51], "vertices": [2, 12, 38.85, -6.56, 2e-05, 13, 23.85, -1.73, 0.99998, 2, 12, 36.29, -10.56, 1e-05, 13, 22.17, -6.17, 0.99999, 2, 12, 22.65, -11.04, 0.00502, 13, 8.92, -9.43, 0.99498, 4, 12, 18, -11.95, 0.11972, 13, 4.55, -11.27, 0.87005, 9, 76.63, 2.32, 0.00033, 11, 37.92, -11.64, 0.0099, 4, 12, 7.5, -16.84, 0.44592, 13, -4.73, -18.21, 0.2659, 9, 67.63, -4.99, 0.01533, 11, 27.48, -16.67, 0.27285, 4, 12, -3, -21.73, 0.17825, 13, -14.01, -25.15, 0.04546, 9, 58.64, -12.29, 0.08089, 11, 17.03, -21.69, 0.6954, 4, 12, -12.08, -25.96, 0.04482, 13, -22.02, -31.15, 0.00619, 9, 50.87, -18.6, 0.17668, 11, 8.02, -26.03, 0.77231, 4, 12, -28.2, -27.06, 0.00048, 9, 35.5, -23.6, 0.40483, 11, -8.09, -27.33, 0.59442, 10, 9.95, -27.2, 0.00027, 3, 9, 19.27, -22.05, 0.63522, 11, -23.52, -22.06, 0.26707, 10, -5.26, -21.3, 0.09771, 3, 9, 3.53, -12.47, 0.91399, 11, -36.61, -9.09, 0.01556, 10, -17.81, -7.81, 0.07046, 6, 9, -4.97, -11.18, 0.7288, 11, -44.58, -5.87, 0.00013, 10, -25.64, -4.27, 0.00827, 6, 39.12, -26.46, 0.00276, 7, 28.39, -16.18, 0.01753, 8, 14.36, -11.3, 0.24251, 5, 9, -12.04, -12.38, 0.27558, 10, -32.77, -3.5, 0.00023, 6, 33.57, -21.92, 0.03658, 7, 21.44, -14.4, 0.185, 8, 7.27, -12.37, 0.5026, 4, 9, -19.11, -13.57, 0.06569, 6, 28.01, -17.38, 0.18629, 7, 14.5, -12.63, 0.50505, 8, 0.18, -13.45, 0.24297, 5, 9, -23.34, -15.61, 0.01812, 6, 23.7, -15.54, 0.43439, 7, 9.8, -12.78, 0.47716, 8, -4.08, -15.41, 0.07032, 5, 32.19, -25.33, 1e-05, 5, 9, -29.98, -20.03, 0.00061, 6, 15.99, -13.46, 0.87381, 7, 1.94, -14.13, 0.09969, 8, -10.79, -19.73, 0.00158, 5, 26.59, -19.65, 0.02432, 4, 9, -32.53, -22.23, 2e-05, 6, 12.67, -12.99, 0.90736, 7, -1.28, -15.1, 0.02078, 5, 23.95, -17.57, 0.07184, 2, 6, 5.35, -13.53, 0.62835, 5, 17.36, -14.36, 0.37165, 3, 6, -2, -16.5, 0.18352, 5, 9.52, -13.22, 0.81548, 4, 26.76, -15.55, 0.00101, 3, 6, -8.64, -21.62, 0.02049, 5, 1.2, -14.3, 0.83296, 4, 18.42, -14.68, 0.14655, 3, 6, -10.68, -23.62, 0.00642, 5, -1.57, -15.01, 0.70046, 4, 15.56, -14.73, 0.29312, 2, 5, -13.08, -19.59, 0.17391, 4, 3.31, -16.53, 0.82609, 2, 5, -24.58, -24.16, 0.0238, 4, -8.94, -18.34, 0.9762, 2, 5, -33.52, -22.74, 0.00263, 4, -17.31, -14.89, 0.99737, 2, 5, -37.61, -18.46, 9e-05, 4, -20.3, -9.78, 0.99991, 1, 4, -20.87, -8.47, 1, 1, 4, -20.49, -1.87, 1, 1, 4, -18.31, 3.68, 1, 1, 4, -13.74, 7.9, 1, 1, 4, -1.61, 13.14, 1, 2, 5, -11.32, 14.81, 0.00575, 4, 12.96, 16.52, 0.99425, 2, 5, -1.93, 17.46, 0.19222, 4, 22.7, 16.94, 0.80778, 3, 6, -20.93, 8.6, 0.00717, 5, 5.8, 18, 0.48784, 4, 30.34, 15.68, 0.505, 3, 6, -17.33, 10.06, 0.03222, 5, 9.64, 17.44, 0.61248, 4, 33.95, 14.25, 0.3553, 4, 6, -10.12, 12.97, 0.20237, 7, -32.87, -1.15, 0.00048, 5, 17.33, 16.33, 0.66322, 4, 41.18, 11.39, 0.13394, 4, 6, -2.85, 14.19, 0.54574, 7, -26.79, 3.02, 0.01608, 5, 24.23, 13.72, 0.4038, 4, 47.29, 7.27, 0.03438, 4, 6, 1.72, 14.96, 0.7397, 7, -22.97, 5.63, 0.05018, 5, 28.56, 12.08, 0.20086, 4, 51.13, 4.68, 0.00926, 4, 6, 7.46, 14.91, 0.81116, 7, -17.74, 8.02, 0.14116, 5, 33.51, 9.15, 0.04716, 4, 55.27, 0.68, 0.00052, 3, 6, 13.02, 14.01, 0.66847, 7, -12.33, 9.53, 0.32853, 5, 37.85, 5.57, 0.003, 2, 6, 19.86, 12.89, 0.29974, 7, -5.64, 11.4, 0.70026, 2, 6, 25.84, 10.82, 0.03626, 7, 0.64, 12.04, 0.96374, 1, 7, 5.82, 12.22, 1, 2, 7, 11.85, 12.43, 0.96927, 8, -12.03, 8.6, 0.03073, 2, 7, 18.44, 11.78, 0.74138, 8, -5.71, 10.57, 0.25862, 2, 7, 29.13, 9.34, 0.09792, 8, 5.09, 12.49, 0.90208, 4, 9, -7.67, 12.35, 0.0085, 10, -21.86, 19.12, 0.00384, 7, 35.47, 6.43, 0.00289, 8, 12.06, 12.28, 0.98477, 3, 9, -0.7, 12.26, 0.24193, 10, -15.17, 17.14, 0.07293, 8, 19.03, 12.07, 0.68514, 4, 12, -43.9, 27.13, 0.00157, 9, 7.07, 25.14, 0.33434, 10, -4.2, 27.42, 0.51707, 8, 27.01, 24.81, 0.14702, 5, 12, -34.99, 31.36, 0.01583, 9, 14.68, 31.41, 0.20103, 11, -15.6, 31.01, 0.00157, 10, 4.82, 31.4, 0.71968, 8, 34.73, 30.95, 0.06189, 5, 12, -20.52, 34.38, 0.0993, 9, 27.98, 37.86, 0.05655, 11, -1.17, 34.2, 0.04771, 10, 19.37, 34, 0.78578, 8, 48.13, 37.18, 0.01067, 5, 12, -9.25, 33.77, 0.24136, 9, 39.06, 40.01, 0.01296, 11, 10.1, 33.73, 0.10992, 10, 30.62, 33.07, 0.6347, 8, 59.25, 39.15, 0.00106, 4, 12, -0.05, 31.33, 0.42183, 9, 48.57, 39.89, 0.0014, 11, 19.33, 31.4, 0.13143, 10, 39.74, 30.37, 0.44535, 3, 12, 10.34, 25.79, 0.69869, 11, 29.8, 26, 0.08205, 10, 49.98, 24.54, 0.21926, 3, 12, 18.64, 18.81, 0.90672, 11, 38.18, 19.12, 0.01619, 10, 58.07, 17.32, 0.07709, 3, 12, 25.71, 10.78, 0.89233, 13, 7.44, 12.55, 0.09636, 10, 64.91, 9.09, 0.01131, 1, 13, 22.57, 1.22, 1], "hull": 55, "edges": [0, 108, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 90, 92, 92, 94, 94, 96, 38, 40, 40, 42, 66, 68, 64, 66, 62, 64, 58, 60, 62, 60, 72, 74, 68, 70, 72, 70, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 82, 84, 20, 22, 22, 24, 86, 88, 88, 90, 10, 12, 6, 8, 8, 10, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108], "width": 79, "height": 181}}, "shank_back_left_white": {"shank_back_left_white": {"type": "mesh", "uvs": [0.54683, 0, 0.66258, 0.01892, 0.86233, 0.18332, 0.99625, 0.30904, 1, 0.35681, 1, 0.41393, 0.97164, 0.50935, 0.94328, 0.60478, 0.8639, 0.70975, 0.77424, 0.792, 0.66764, 0.84866, 0.58755, 0.89123, 0.56049, 0.91858, 0.52251, 0.9793, 0.41297, 1, 0.34775, 1, 0.11716, 0.96921, 0.0019, 0.8934, 0.00238, 0.80381, 0.0669, 0.67014, 0.07518, 0.63363, 0.05747, 0.54176, 0.04045, 0.4535, 0.02727, 0.38516, 0.00747, 0.28244, 0, 0.24372, 0.01714, 0.21752, 0.09716, 0.09776, 0.28334, 0.01629, 0.47188, 0, 0.54155, 0.5469, 0.27319, 0.85454], "triangles": [13, 14, 12, 16, 31, 15, 31, 12, 14, 31, 14, 15, 16, 17, 31, 12, 31, 11, 17, 18, 31, 11, 31, 10, 30, 10, 31, 31, 18, 19, 20, 30, 31, 31, 19, 20, 10, 30, 9, 9, 30, 8, 20, 21, 30, 8, 30, 7, 7, 30, 6, 30, 21, 22, 30, 22, 23, 30, 24, 28, 28, 26, 27, 28, 29, 30, 6, 30, 4, 1, 2, 0, 0, 2, 29, 29, 2, 30, 5, 6, 4, 2, 4, 30, 4, 2, 3, 30, 23, 24, 26, 28, 24, 24, 25, 26], "vertices": [1, 31, -3.9, 36.47, 1, 1, 31, 4.87, 50.01, 1, 1, 31, 45.39, 64.59, 1, 2, 31, 75.55, 73.33, 0.97853, 32, -71.16, 31.1, 0.02147, 2, 31, 84.92, 70.62, 0.95588, 32, -62.88, 36.27, 0.04412, 2, 31, 95.93, 66.82, 0.90449, 32, -52.69, 41.91, 0.09551, 2, 31, 113.06, 56.83, 0.73236, 32, -33.81, 47.98, 0.26764, 2, 31, 130.2, 46.84, 0.48202, 32, -14.92, 54.05, 0.51798, 2, 31, 146.92, 29.69, 0.19281, 32, 9.01, 55.02, 0.80719, 2, 31, 158.81, 12.74, 0.04766, 32, 29.56, 52.54, 0.95234, 2, 31, 165.01, -4.67, 0.00415, 32, 46.66, 45.52, 0.99585, 1, 32, 59.5, 40.24, 1, 1, 32, 66.16, 39.74, 1, 1, 32, 79.47, 41.24, 1, 1, 32, 90.35, 30.32, 1, 1, 32, 94.62, 22.6, 1, 1, 32, 104.24, -7.74, 1, 1, 32, 98.27, -28.87, 1, 2, 31, 126.94, -86.74, 0.00162, 32, 82.26, -37.66, 0.99838, 2, 31, 104.03, -69.58, 0.07168, 32, 54.18, -43.23, 0.92832, 2, 31, 97.36, -66.09, 0.13646, 32, 47.12, -45.86, 0.86354, 2, 31, 78.87, -62.22, 0.37216, 32, 31.89, -57.03, 0.62784, 2, 31, 61.11, -58.52, 0.62556, 32, 17.26, -67.76, 0.37444, 2, 31, 47.36, -55.64, 0.78608, 32, 5.93, -76.07, 0.21392, 2, 31, 26.68, -51.33, 0.92883, 32, -11.1, -88.57, 0.07117, 2, 31, 18.89, -49.7, 0.9526, 32, -17.52, -93.27, 0.0474, 2, 31, 14.6, -45.76, 0.96448, 32, -23.32, -93.83, 0.03552, 2, 31, -4.94, -27.55, 0.99795, 32, -49.93, -96.19, 0.00205, 1, 31, -12.41, 1.69, 1, 1, 31, -7.21, 26.89, 1, 1, 32, 1.08, 0.78, 1, 1, 32, 73.56, -0.59, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 54, 56, 56, 58, 20, 22, 18, 20, 14, 16, 18, 16, 10, 12, 12, 14, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 48, 52, 50, 52], "width": 136, "height": 205}}, "shank_back_right_white": {"shank_back_right_white": {"type": "mesh", "uvs": [0.54683, 0, 0.66258, 0.01892, 0.86233, 0.18332, 0.99625, 0.30904, 1, 0.35681, 1, 0.41393, 0.97164, 0.50935, 0.94328, 0.60478, 0.8639, 0.70975, 0.77424, 0.792, 0.66764, 0.84866, 0.58755, 0.89123, 0.56049, 0.91858, 0.52251, 0.9793, 0.41297, 1, 0.34775, 1, 0.11716, 0.96921, 0.0019, 0.8934, 0.00238, 0.80381, 0.0669, 0.67014, 0.07518, 0.63363, 0.05747, 0.54176, 0.04045, 0.4535, 0.02727, 0.38516, 0.00747, 0.28244, 0, 0.24372, 0.01714, 0.21752, 0.09716, 0.09776, 0.28334, 0.01629, 0.47188, 0, 0.54155, 0.5469, 0.27319, 0.85454], "triangles": [24, 25, 26, 26, 28, 24, 30, 23, 24, 4, 2, 3, 30, 22, 23, 2, 4, 30, 5, 6, 4, 30, 21, 22, 29, 2, 30, 0, 2, 29, 1, 2, 0, 6, 30, 4, 28, 29, 30, 28, 26, 27, 30, 24, 28, 7, 30, 6, 8, 30, 7, 20, 21, 30, 9, 30, 8, 10, 30, 9, 31, 19, 20, 20, 30, 31, 31, 18, 19, 30, 10, 31, 11, 31, 10, 17, 18, 31, 12, 31, 11, 16, 17, 31, 31, 14, 15, 31, 12, 14, 16, 31, 15, 13, 14, 12], "vertices": [2, 36, -111.54, -45.6, 0.00616, 35, -12.91, 47.88, 0.99384, 2, 36, -114.81, -29.81, 0.00991, 35, -3.95, 61.29, 0.99009, 2, 36, -96.2, 9.03, 0.03097, 35, 36.79, 75.26, 0.96903, 2, 36, -80.88, 36.44, 0.04154, 35, 67.07, 83.56, 0.95846, 2, 36, -72.31, 41.09, 0.04127, 35, 76.4, 80.71, 0.95873, 2, 36, -61.8, 46.11, 0.03824, 35, 87.35, 76.74, 0.96176, 2, 36, -42.59, 51.04, 0.02282, 35, 104.34, 66.5, 0.97718, 2, 36, -23.37, 55.97, 0.00249, 35, 121.32, 56.26, 0.99751, 2, 36, 0.57, 55.51, 0.02044, 35, 137.79, 38.86, 0.97956, 2, 36, 20.94, 51.8, 0.1989, 35, 149.42, 21.74, 0.8011, 2, 36, 37.58, 43.76, 0.51789, 35, 155.37, 4.24, 0.48211, 2, 36, 50.09, 37.73, 0.7779, 35, 159.84, -8.9, 0.2221, 2, 36, 56.7, 36.83, 0.87133, 35, 163.83, -14.25, 0.12867, 2, 36, 70.09, 37.53, 0.94878, 35, 173.72, -23.3, 0.05122, 2, 36, 80.29, 25.98, 0.98325, 35, 172.64, -38.67, 0.01675, 2, 36, 84.09, 18.01, 0.99463, 35, 169.63, -46.97, 0.00537, 1, 36, 91.87, -12.84, 1, 2, 36, 84.65, -33.58, 0.99978, 35, 133.24, -83.55, 0.00022, 2, 36, 68.14, -41.4, 0.98527, 35, 116.08, -77.26, 0.01473, 2, 36, 39.77, -45.27, 0.79207, 35, 93.43, -59.76, 0.20793, 2, 36, 32.57, -47.47, 0.6568, 35, 86.81, -56.17, 0.3432, 2, 36, 16.7, -57.71, 0.31404, 35, 68.38, -52.03, 0.68596, 2, 36, 1.45, -67.55, 0.10748, 35, 50.67, -48.06, 0.89252, 2, 36, -10.36, -75.16, 0.03339, 35, 36.97, -44.99, 0.96661, 2, 36, -28.1, -86.62, 0.00147, 35, 16.36, -40.36, 0.99853, 2, 36, -34.79, -90.93, 6e-05, 35, 8.59, -38.62, 0.99994, 1, 35, 4.36, -34.62, 1, 1, 35, -14.91, -16.11, 1, 2, 36, -93.17, -76.34, 0.0005, 35, -21.94, 13.23, 0.9995, 2, 36, -107.16, -54.75, 0.00405, 35, -16.37, 38.35, 0.99595, 2, 36, -10.59, 1.84, 0.24119, 35, 91.7, 9.18, 0.75881, 1, 36, 61.67, -3.87, 1], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 54, 56, 56, 58, 20, 22, 18, 20, 14, 16, 18, 16, 10, 12, 12, 14, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 48, 52, 50, 52], "width": 136, "height": 205}}, "shank_front_left_white": {"shank_front_left_white": {"type": "mesh", "uvs": [0.73118, 0.01508, 0.81785, 0.05062, 0.89536, 0.10394, 0.99712, 0.20865, 0.99699, 0.26003, 0.98779, 0.32318, 0.95638, 0.40847, 0.92497, 0.49376, 0.89549, 0.57381, 0.87959, 0.63275, 0.87957, 0.68656, 0.87955, 0.74037, 0.87953, 0.79102, 0.86972, 0.83861, 0.85991, 0.88619, 0.82044, 0.93711, 0.67967, 0.98493, 0.53785, 0.99782, 0.38661, 0.99785, 0.28046, 0.98092, 0.20154, 0.94537, 0.16181, 0.89393, 0.14803, 0.84311, 0.13424, 0.79229, 0.12408, 0.75486, 0.10662, 0.70553, 0.07894, 0.6425, 0.05127, 0.57948, 0.01979, 0.50781, 0.00313, 0.42589, 0.00283, 0.31919, 0.03428, 0.25533, 0.09477, 0.20534, 0.22572, 0.11638, 0.37335, 0.04183, 0.52877, 0.00202, 0.65523, 0.00216, 0.55001, 0.70774], "triangles": [4, 2, 3, 5, 2, 4, 6, 2, 5, 32, 29, 30, 6, 1, 2, 32, 28, 29, 6, 7, 1, 30, 31, 32, 9, 27, 28, 1, 34, 35, 36, 1, 35, 34, 1, 8, 7, 8, 1, 9, 26, 27, 1, 36, 0, 8, 33, 34, 8, 32, 33, 8, 9, 32, 28, 32, 9, 26, 37, 25, 26, 9, 37, 10, 37, 9, 11, 37, 10, 24, 25, 37, 12, 37, 11, 23, 24, 37, 13, 37, 12, 22, 23, 37, 13, 16, 37, 19, 21, 22, 13, 14, 16, 19, 20, 21, 19, 22, 18, 37, 18, 22, 14, 15, 16, 16, 17, 37, 17, 18, 37], "vertices": [2, 40, -101.98, 12.49, 0.00048, 39, 6.87, 48.58, 0.99952, 2, 40, -97.13, 21.52, 0.00286, 39, 14.66, 55.25, 0.99714, 2, 40, -89.59, 29.77, 0.00817, 39, 24.68, 60.2, 0.99183, 2, 40, -74.51, 40.85, 0.01826, 39, 42.75, 65.07, 0.98174, 2, 40, -66.85, 41.22, 0.0368, 39, 50.02, 62.64, 0.9632, 2, 40, -57.4, 40.77, 0.0754, 39, 58.67, 58.79, 0.9246, 2, 40, -44.53, 38.23, 0.15329, 39, 69.75, 51.76, 0.84671, 2, 40, -31.66, 35.69, 0.28413, 39, 80.82, 44.74, 0.71587, 2, 40, -19.59, 33.31, 0.46285, 39, 91.21, 38.14, 0.53715, 2, 40, -10.73, 32.15, 0.65535, 39, 99.05, 33.85, 0.34465, 2, 40, -2.71, 32.55, 0.81985, 39, 106.67, 31.32, 0.18015, 2, 40, 5.3, 32.95, 0.9275, 39, 114.28, 28.79, 0.0725, 2, 40, 12.85, 33.33, 0.9798, 39, 121.45, 26.41, 0.0202, 2, 40, 19.99, 32.69, 0.99689, 39, 127.88, 23.23, 0.00311, 2, 40, 27.13, 32.06, 0.99988, 39, 134.3, 20.05, 0.00012, 1, 40, 34.91, 28.45, 1, 1, 40, 42.76, 14.57, 1, 1, 40, 45.4, 0.31, 1, 1, 40, 46.18, -14.99, 1, 1, 40, 44.2, -25.86, 1, 1, 40, 39.3, -34.11, 1, 2, 40, 31.85, -38.52, 0.97899, 39, 113.13, -47.44, 0.02101, 2, 40, 24.34, -40.29, 0.86379, 39, 105.49, -46.37, 0.13621, 2, 40, 16.84, -42.07, 0.7184, 39, 97.86, -45.32, 0.2816, 2, 40, 11.32, -43.38, 0.5319, 39, 92.24, -44.53, 0.4681, 2, 40, 4.06, -45.52, 0.33487, 39, 84.7, -43.9, 0.66513, 2, 40, -5.19, -48.79, 0.16756, 39, 74.89, -43.6, 0.83244, 2, 40, -14.43, -52.07, 0.06265, 39, 65.09, -43.3, 0.93735, 2, 40, -24.95, -55.79, 0.01479, 39, 53.94, -42.96, 0.98521, 2, 40, -37.07, -58.09, 0.00294, 39, 41.8, -40.72, 0.99706, 2, 40, -52.97, -58.92, 0.00021, 39, 26.69, -35.73, 0.99979, 1, 39, 18.65, -29.71, 1, 1, 39, 13.5, -21.55, 1, 1, 39, 5.09, -4.78, 1, 1, 39, -0.76, 12.92, 1, 1, 39, -1.44, 29.73, 1, 2, 40, -103.52, 4.7, 5e-05, 39, 2.62, 41.89, 0.99995, 1, 40, 2.13, -0.64, 1], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 48, 50, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 46, 48, 42, 44, 44, 46, 24, 26, 26, 28, 22, 24, 18, 20, 20, 22, 14, 16, 10, 12, 12, 14, 54, 56, 50, 52, 52, 54], "width": 102, "height": 150}}, "shank_front_right_white": {"shank_front_right_white": {"type": "mesh", "uvs": [0.73528, 0.01599, 0.81927, 0.05178, 0.89599, 0.10424, 0.99695, 0.20943, 0.99695, 0.25748, 0.9856, 0.32763, 0.95487, 0.41253, 0.92414, 0.49743, 0.90255, 0.55707, 0.88114, 0.61625, 0.87982, 0.64119, 0.87974, 0.71371, 0.87965, 0.78623, 0.86, 0.89017, 0.81252, 0.93961, 0.68282, 0.98419, 0.53948, 0.99787, 0.3741, 0.99782, 0.25942, 0.97481, 0.19498, 0.9373, 0.16076, 0.89141, 0.1332, 0.78703, 0.11784, 0.73724, 0.07449, 0.63508, 0.03113, 0.53292, 0.00282, 0.43733, 0.00322, 0.32359, 0.02645, 0.2628, 0.09173, 0.20722, 0.22946, 0.11408, 0.36897, 0.04442, 0.52444, 0.00216, 0.65536, 0.0021, 0.5462, 0.68953], "triangles": [9, 33, 23, 10, 33, 9, 11, 33, 10, 22, 23, 33, 12, 33, 11, 21, 22, 33, 13, 33, 12, 20, 21, 33, 18, 19, 20, 13, 15, 33, 33, 18, 20, 18, 33, 17, 13, 14, 15, 33, 16, 17, 15, 16, 33, 4, 5, 2, 4, 2, 3, 5, 1, 2, 5, 6, 1, 28, 26, 27, 28, 25, 26, 9, 24, 25, 6, 7, 1, 30, 1, 8, 7, 8, 1, 9, 23, 24, 1, 30, 31, 31, 32, 1, 1, 32, 0, 8, 29, 30, 8, 28, 29, 8, 9, 28, 25, 28, 9], "vertices": [1, 42, 10.57, 46.28, 1, 1, 42, 18.05, 52.98, 1, 1, 42, 27.72, 58.29, 1, 2, 42, 45.62, 63.79, 0.99988, 43, -71.66, 44.83, 0.00012, 2, 42, 52.51, 61.81, 0.99859, 43, -64.49, 44.95, 0.00141, 2, 42, 62.25, 57.81, 0.99106, 43, -54.01, 43.97, 0.00894, 2, 42, 73.56, 51.32, 0.9602, 43, -41.3, 41.06, 0.0398, 2, 42, 84.87, 44.83, 0.878, 43, -28.58, 38.15, 0.122, 2, 42, 92.82, 40.27, 0.76651, 43, -19.65, 36.11, 0.23349, 2, 42, 100.7, 35.75, 0.58618, 43, -10.79, 34.08, 0.41382, 2, 42, 104.24, 34.59, 0.49108, 43, -7.07, 34.01, 0.50892, 2, 42, 114.63, 31.59, 0.2423, 43, 3.75, 34.17, 0.7577, 2, 42, 125.03, 28.6, 0.08295, 43, 14.56, 34.34, 0.91705, 2, 42, 139.38, 22.4, 0.00501, 43, 30.1, 32.6, 0.99499, 1, 43, 37.55, 27.91, 1, 1, 43, 44.41, 14.88, 1, 1, 43, 46.68, 0.39, 1, 1, 43, 46.95, -16.36, 1, 1, 43, 43.7, -28.03, 1, 1, 43, 38.21, -34.65, 1, 1, 43, 31.43, -38.23, 1, 1, 43, 15.9, -41.27, 1, 2, 42, 96.69, -43.56, 0.391, 43, 8.5, -42.95, 0.609, 2, 42, 80.83, -43.57, 0.71419, 43, -6.66, -47.58, 0.28581, 2, 42, 64.97, -43.58, 0.934, 43, -21.83, -52.22, 0.066, 2, 42, 50.47, -42.4, 0.98791, 43, -36.04, -55.32, 0.01209, 2, 42, 34.18, -37.68, 0.99993, 43, -53, -55.56, 7e-05, 1, 42, 26.12, -32.91, 1, 1, 42, 19.97, -24.26, 1, 1, 42, 10.48, -7.02, 1, 1, 42, 4.39, 9.44, 1, 1, 42, 2.69, 26.31, 1, 1, 42, 6.34, 39.07, 1, 1, 43, 0.69, 0.33, 1], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 20, 22, 22, 24, 16, 18, 14, 16, 10, 12, 12, 14, 44, 46, 46, 48], "width": 102, "height": 150}}}}], "animations": {"eat": {"bones": {"dau": {"rotate": [{"curve": [0.167, 0, 0.5, 8.43]}, {"time": 0.6667, "value": 8.43, "curve": [0.833, 8.43, 1.167, 4.76]}, {"time": 1.3333, "value": 4.76, "curve": [1.5, 4.76, 1.833, 0.76]}, {"time": 2, "value": 0.76, "curve": [2.167, 0.76, 2.5, 4.76]}, {"time": 2.6667, "value": 4.76, "curve": [2.833, 4.76, 3.167, 0.76]}, {"time": 3.3333, "value": 0.76, "curve": [3.5, 0.76, 3.833, 4.76]}, {"time": 4, "value": 4.76, "curve": [4.167, 4.76, 4.5, 0]}, {"time": 4.6667}], "translate": [{"curve": [0.167, 0, 0.5, -34.67, 0.167, 0, 0.5, -36.44]}, {"time": 0.6667, "x": -34.67, "y": -36.44, "curve": [0.833, -34.67, 1.167, 0, 0.833, -36.44, 1.167, 0]}, {"time": 1.3333}]}, "body5": {"rotate": [{"curve": [0.167, 0, 0.5, -19.99]}, {"time": 0.6667, "value": -19.99, "curve": [0.833, -19.99, 1.167, 4.08]}, {"time": 1.3333, "value": 4.08, "curve": [1.584, -0.88, 1.811, -4]}, {"time": 2, "value": -4, "curve": [2.167, -4, 2.5, 4.08]}, {"time": 2.6667, "value": 4.08, "curve": [2.917, -0.88, 3.144, -4]}, {"time": 3.3333, "value": -4, "curve": [3.5, -4, 3.833, 4.08]}, {"time": 4, "value": 4.08, "curve": [4.25, 1.58, 4.478, 0]}, {"time": 4.6667}], "translate": [{"curve": [0.167, 0, 0.5, 19.95, 0.167, 0, 0.5, -194.88]}, {"time": 0.6667, "x": 19.95, "y": -194.88, "curve": [0.833, 19.95, 1.167, 0, 0.833, -194.88, 1.167, 0]}, {"time": 1.3333}]}, "body": {"rotate": [{"curve": [0.167, 0, 0.5, -2.12]}, {"time": 0.6667, "value": -2.12, "curve": [0.85, -2.12, 1.217, 0]}, {"time": 1.4}], "translate": [{"curve": [0.167, 0, 0.5, 39.66, 0.167, 0, 0.5, -15.17]}, {"time": 0.6667, "x": 39.66, "y": -15.17, "curve": [0.85, 39.66, 1.217, 0, 0.85, -15.17, 1.217, 0]}, {"time": 1.4, "curve": [1.551, 0, 1.774, 0.29, 1.551, 0, 1.774, 0]}, {"time": 2, "x": 0.59, "curve": [2.248, -1.15, 2.501, -2.98, 2.248, 0, 2.501, 0]}, {"time": 2.6667, "x": -2.98, "curve": [2.833, -2.98, 3.167, 0.59, 2.833, 0, 3.167, 0]}, {"time": 3.3333, "x": 0.59, "curve": [3.582, -1.15, 3.834, -2.98, 3.582, 0, 3.834, 0]}, {"time": 4, "x": -2.98, "curve": [4.167, -2.98, 4.5, 0, 4.167, 0, 4.5, 0]}, {"time": 4.6667}]}, "dui truoc1": {"translate": [{"curve": [0.167, 0, 0.5, 13.52, 0.167, 0, 0.5, 11.55]}, {"time": 0.6667, "x": 13.52, "y": 11.55, "curve": [0.833, 13.52, 1.167, 0, 0.833, 11.55, 1.167, 0.66]}, {"time": 1.3333, "y": 0.66, "curve": [1.5, 0, 1.833, 0, 1.5, 0.66, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.5, 0, 2.167, 0, 2.5, 0.66]}, {"time": 2.6667, "y": 0.66, "curve": [2.833, 0, 3.167, 0, 2.833, 0.66, 3.167, 0]}, {"time": 3.3333, "curve": [3.5, 0, 3.833, 0, 3.5, 0, 3.833, 0.66]}, {"time": 4, "y": 0.66, "curve": [4.167, 0, 4.5, 0, 4.167, 0.66, 4.5, 0]}, {"time": 4.6667}]}, "dui truoc4": {"translate": [{"curve": [0.167, 0, 0.5, 13.52, 0.167, 0, 0.5, 11.55]}, {"time": 0.6667, "x": 13.52, "y": 11.55, "curve": [0.833, 13.52, 1.167, 0, 0.833, 11.55, 1.167, 1.65]}, {"time": 1.3333, "y": 1.65, "curve": [1.5, 0, 1.833, 0, 1.5, 1.65, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.5, 0, 2.167, 0, 2.5, 1.65]}, {"time": 2.6667, "y": 1.65, "curve": [2.833, 0, 3.167, 0, 2.833, 1.65, 3.167, 0]}, {"time": 3.3333, "curve": [3.5, 0, 3.833, 0, 3.5, 0, 3.833, 1.65]}, {"time": 4, "y": 1.65, "curve": [4.167, 0, 4.5, 0, 4.167, 1.65, 4.5, 0]}, {"time": 4.6667}]}, "body4": {"rotate": [{"curve": [0.333, 0, 1, -0.68]}, {"time": 1.3333, "value": -0.68, "curve": [1.5, -0.68, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.5, -0.68]}, {"time": 2.6667, "value": -0.68, "curve": [2.833, -0.68, 3.167, 0]}, {"time": 3.3333, "curve": [3.5, 0, 3.833, -0.68]}, {"time": 4, "value": -0.68, "curve": [4.167, -0.68, 4.5, 0]}, {"time": 4.6667}]}, "body2": {"rotate": [{"curve": [0.167, 0, 0.5, -1.56]}, {"time": 0.6667, "value": -1.56, "curve": [0.833, -1.56, 1.167, 0]}, {"time": 1.3333}]}, "dui sau1": {"rotate": [{"curve": [0.167, 0, 0.5, -7.98]}, {"time": 0.6667, "value": -7.98, "curve": [0.833, -7.98, 1.167, 0]}, {"time": 1.3333}]}, "dui sau5": {"rotate": [{"curve": [0.167, 0, 0.5, -2.28]}, {"time": 0.6667, "value": -2.28, "curve": [0.833, -2.28, 1.167, 0]}, {"time": 1.3333}]}, "duoi2": {"rotate": [{"curve": [0.308, 0, 0.925, -1.66]}, {"time": 1.2333, "value": -1.66, "curve": [1.266, -1.19, 1.3, -0.9]}, {"time": 1.3333, "value": -0.87, "curve": [1.5, -0.87, 1.833, -11.03]}, {"time": 2, "value": -11.03, "curve": [2.221, -10.97, 2.391, -3.9]}, {"time": 2.5667, "value": -1.66, "curve": [2.6, -1.19, 2.633, -0.9]}, {"time": 2.6667, "value": -0.87, "curve": [2.833, -0.87, 3.167, -11.03]}, {"time": 3.3333, "value": -11.03, "curve": [3.776, -10.96, 4.115, -2.63]}, {"time": 4.4667}]}, "duoi3": {"rotate": [{"curve": [0.308, 0, 0.925, -0.84]}, {"time": 1.2333, "value": -0.84, "curve": [1.267, 2.08, 1.3, 4.61]}, {"time": 1.3333, "value": 6.44, "curve": [1.366, 8.42, 1.4, 9.64]}, {"time": 1.4333, "value": 9.78, "curve": [1.599, 9.78, 1.924, -30.36]}, {"time": 2.1, "value": -33.04, "curve": [2.279, -32.86, 2.424, -13.01]}, {"time": 2.5667, "value": -0.84, "curve": [2.6, 2.08, 2.633, 4.61]}, {"time": 2.6667, "value": 6.44, "curve": [2.7, 8.42, 2.733, 9.64]}, {"time": 2.7667, "value": 9.78, "curve": [2.932, 9.78, 3.257, -30.36]}, {"time": 3.4333, "value": -33.04, "curve": [3.831, -32.85, 4.152, -12.48]}, {"time": 4.4667}]}, "duoi4": {"rotate": [{"curve": [0.308, 0, 0.925, -3.82]}, {"time": 1.2333, "value": -3.82, "curve": [1.267, 0.41, 1.3, 4.52]}, {"time": 1.3333, "value": 8.14, "curve": [1.367, 11.86, 1.4, 15.08]}, {"time": 1.4333, "value": 17.42, "curve": [1.466, 19.94, 1.5, 21.5]}, {"time": 1.5333, "value": 21.68, "curve": [1.699, 21.68, 2.024, -29.49]}, {"time": 2.2, "value": -32.9, "curve": [2.338, -32.73, 2.455, -17.58]}, {"time": 2.5667, "value": -3.82, "curve": [2.6, 0.41, 2.634, 4.52]}, {"time": 2.6667, "value": 8.14, "curve": [2.7, 11.86, 2.733, 15.08]}, {"time": 2.7667, "value": 17.42, "curve": [2.8, 19.94, 2.833, 21.5]}, {"time": 2.8667, "value": 21.68, "curve": [3.032, 21.68, 3.357, -29.49]}, {"time": 3.5333, "value": -32.9, "curve": [3.885, -32.7, 4.183, -15.57]}, {"time": 4.4667}]}, "duoi5": {"rotate": [{"curve": [0.308, 0, 0.925, -15.87]}, {"time": 1.2333, "value": -15.87, "curve": [1.267, -11.98, 1.301, -7.85]}, {"time": 1.3333, "value": -3.82, "curve": [1.367, 0.41, 1.4, 4.52]}, {"time": 1.4333, "value": 8.14, "curve": [1.467, 11.86, 1.5, 15.08]}, {"time": 1.5333, "value": 17.42, "curve": [1.566, 19.94, 1.6, 21.5]}, {"time": 1.6333, "value": 21.68, "curve": [1.799, 21.68, 2.124, -29.49]}, {"time": 2.3, "value": -32.9, "curve": [2.398, -32.78, 2.485, -25.1]}, {"time": 2.5667, "value": -15.87, "curve": [2.601, -11.98, 2.634, -7.85]}, {"time": 2.6667, "value": -3.82, "curve": [2.7, 0.41, 2.734, 4.52]}, {"time": 2.7667, "value": 8.14, "curve": [2.8, 11.86, 2.833, 15.08]}, {"time": 2.8667, "value": 17.42, "curve": [2.9, 19.94, 2.933, 21.5]}, {"time": 2.9667, "value": 21.68, "curve": [3.132, 21.68, 3.457, -29.49]}, {"time": 3.6333, "value": -32.9, "curve": [3.939, -32.67, 4.211, -17.83]}, {"time": 4.4667}]}, "duoi6": {"rotate": [{"curve": [0.308, 0, 0.925, -25.72]}, {"time": 1.2333, "value": -25.72, "curve": [1.268, -22.89, 1.301, -19.49]}, {"time": 1.3333, "value": -15.87, "curve": [1.367, -11.98, 1.401, -7.85]}, {"time": 1.4333, "value": -3.82, "curve": [1.467, 0.41, 1.5, 4.52]}, {"time": 1.5333, "value": 8.14, "curve": [1.567, 11.86, 1.6, 15.08]}, {"time": 1.6333, "value": 17.42, "curve": [1.666, 19.94, 1.7, 21.5]}, {"time": 1.7333, "value": 21.68, "curve": [1.899, 21.68, 2.224, -29.49]}, {"time": 2.4, "value": -32.9, "curve": [2.459, -32.83, 2.514, -29.98]}, {"time": 2.5667, "value": -25.72, "curve": [2.601, -22.89, 2.634, -19.49]}, {"time": 2.6667, "value": -15.87, "curve": [2.701, -11.98, 2.734, -7.85]}, {"time": 2.7667, "value": -3.82, "curve": [2.8, 0.41, 2.834, 4.52]}, {"time": 2.8667, "value": 8.14, "curve": [2.9, 11.86, 2.933, 15.08]}, {"time": 2.9667, "value": 17.42, "curve": [3, 19.94, 3.033, 21.5]}, {"time": 3.0667, "value": 21.68, "curve": [3.232, 21.68, 3.557, -29.49]}, {"time": 3.7333, "value": -32.9, "curve": [3.792, -32.83, 3.848, -29.98]}, {"time": 3.9, "value": -25.72, "curve": [4.06, -20.84, 4.215, -14.99]}, {"time": 4.3667, "value": -8.76, "curve": [4.442, -8.76, 4.592, 0]}, {"time": 4.6667}]}, "duoi7": {"rotate": [{"curve": [0.308, 0, 0.925, -31.65]}, {"time": 1.2333, "value": -31.65, "curve": [1.268, -30.39, 1.301, -28.31]}, {"time": 1.3333, "value": -25.72, "curve": [1.368, -22.89, 1.401, -19.49]}, {"time": 1.4333, "value": -15.87, "curve": [1.467, -11.98, 1.501, -7.85]}, {"time": 1.5333, "value": -3.82, "curve": [1.567, 0.41, 1.6, 4.52]}, {"time": 1.6333, "value": 8.14, "curve": [1.667, 11.86, 1.7, 15.08]}, {"time": 1.7333, "value": 17.42, "curve": [1.766, 19.94, 1.8, 21.5]}, {"time": 1.8333, "value": 21.68, "curve": [1.999, 21.68, 2.324, -29.49]}, {"time": 2.5, "value": -32.9, "curve": [2.523, -32.88, 2.545, -32.43]}, {"time": 2.5667, "value": -31.65, "curve": [2.601, -30.39, 2.634, -28.31]}, {"time": 2.6667, "value": -25.72, "curve": [2.701, -22.89, 2.734, -19.49]}, {"time": 2.7667, "value": -15.87, "curve": [2.801, -11.98, 2.834, -7.85]}, {"time": 2.8667, "value": -3.82, "curve": [2.9, 0.41, 2.934, 4.52]}, {"time": 2.9667, "value": 8.14, "curve": [3, 11.86, 3.033, 15.08]}, {"time": 3.0667, "value": 17.42, "curve": [3.1, 19.94, 3.133, 21.5]}, {"time": 3.1667, "value": 21.68, "curve": [3.332, 21.68, 3.657, -29.49]}, {"time": 3.8333, "value": -32.9, "curve": [3.856, -32.88, 3.878, -32.43]}, {"time": 3.9, "value": -31.65, "curve": [4.061, -26.77, 4.216, -18.75]}, {"time": 4.3667, "value": -8.76, "curve": [4.442, -8.76, 4.592, 0]}, {"time": 4.6667}]}, "duoi8": {"rotate": [{"curve": [0.308, 0, 0.925, -31.53]}, {"time": 1.2333, "value": -31.53, "curve": [1.245, -32.16, 1.256, -32.63]}, {"time": 1.2667, "value": -32.9, "curve": [1.289, -32.88, 1.312, -32.43]}, {"time": 1.3333, "value": -31.65, "curve": [1.368, -30.39, 1.401, -28.31]}, {"time": 1.4333, "value": -25.72, "curve": [1.468, -22.89, 1.501, -19.49]}, {"time": 1.5333, "value": -15.87, "curve": [1.567, -11.98, 1.601, -7.85]}, {"time": 1.6333, "value": -3.82, "curve": [1.667, 0.41, 1.7, 4.52]}, {"time": 1.7333, "value": 8.14, "curve": [1.767, 11.86, 1.8, 15.08]}, {"time": 1.8333, "value": 17.42, "curve": [1.866, 19.94, 1.9, 21.5]}, {"time": 1.9333, "value": 21.68, "curve": [2.089, 21.68, 2.388, -24.06]}, {"time": 2.5667, "value": -31.53, "curve": [2.578, -32.16, 2.589, -32.63]}, {"time": 2.6, "value": -32.9, "curve": [2.623, -32.88, 2.645, -32.43]}, {"time": 2.6667, "value": -31.65, "curve": [2.701, -30.39, 2.734, -28.31]}, {"time": 2.7667, "value": -25.72, "curve": [2.801, -22.89, 2.834, -19.49]}, {"time": 2.8667, "value": -15.87, "curve": [2.901, -11.98, 2.934, -7.85]}, {"time": 2.9667, "value": -3.82, "curve": [3, 0.41, 3.034, 4.52]}, {"time": 3.0667, "value": 8.14, "curve": [3.1, 11.86, 3.133, 15.08]}, {"time": 3.1667, "value": 17.42, "curve": [3.2, 19.94, 3.233, 21.5]}, {"time": 3.2667, "value": 21.68, "curve": [3.422, 21.68, 3.721, -24.06]}, {"time": 3.9, "value": -31.53, "curve": [4.061, -20.98, 4.217, -13.28]}, {"time": 4.3667, "value": -8.76, "curve": [4.442, -8.76, 4.592, 0]}, {"time": 4.6667}]}, "long duoi": {"rotate": [{"curve": [0.308, 0, 0.925, -31.53]}, {"time": 1.2333, "value": -31.53, "curve": [1.245, -32.16, 1.256, -32.63]}, {"time": 1.2667, "value": -32.9, "curve": [1.289, -32.88, 1.312, -32.43]}, {"time": 1.3333, "value": -31.65, "curve": [1.368, -30.39, 1.401, -28.31]}, {"time": 1.4333, "value": -25.72, "curve": [1.468, -22.89, 1.501, -19.49]}, {"time": 1.5333, "value": -15.87, "curve": [1.567, -11.98, 1.601, -7.85]}, {"time": 1.6333, "value": -3.82, "curve": [1.667, 0.41, 1.7, 4.52]}, {"time": 1.7333, "value": 8.14, "curve": [1.767, 11.86, 1.8, 15.08]}, {"time": 1.8333, "value": 17.42, "curve": [1.866, 19.94, 1.9, 21.5]}, {"time": 1.9333, "value": 21.68, "curve": [2.089, 21.68, 2.388, -24.06]}, {"time": 2.5667, "value": -31.53, "curve": [2.578, -32.16, 2.589, -32.63]}, {"time": 2.6, "value": -32.9, "curve": [2.623, -32.88, 2.645, -32.43]}, {"time": 2.6667, "value": -31.65, "curve": [2.701, -30.39, 2.734, -28.31]}, {"time": 2.7667, "value": -25.72, "curve": [2.801, -22.89, 2.834, -19.49]}, {"time": 2.8667, "value": -15.87, "curve": [2.901, -11.98, 2.934, -7.85]}, {"time": 2.9667, "value": -3.82, "curve": [3, 0.41, 3.034, 4.52]}, {"time": 3.0667, "value": 8.14, "curve": [3.1, 11.86, 3.133, 15.08]}, {"time": 3.1667, "value": 17.42, "curve": [3.2, 19.94, 3.233, 21.5]}, {"time": 3.2667, "value": 21.68, "curve": [3.422, 21.68, 3.721, -24.06]}, {"time": 3.9, "value": -31.53, "curve": [4.061, -20.98, 4.217, -13.28]}, {"time": 4.3667, "value": -8.76, "curve": [4.442, -8.76, 4.592, 0]}, {"time": 4.6667}]}, "long duoi2": {"rotate": [{"curve": [0.308, 0, 0.925, -31.53]}, {"time": 1.2333, "value": -31.53, "curve": [1.245, -32.16, 1.256, -32.63]}, {"time": 1.2667, "value": -32.9, "curve": [1.289, -32.88, 1.312, -32.43]}, {"time": 1.3333, "value": -31.65, "curve": [1.368, -30.39, 1.401, -28.31]}, {"time": 1.4333, "value": -25.72, "curve": [1.468, -22.89, 1.501, -19.49]}, {"time": 1.5333, "value": -15.87, "curve": [1.567, -11.98, 1.601, -7.85]}, {"time": 1.6333, "value": -3.82, "curve": [1.667, 0.41, 1.7, 4.52]}, {"time": 1.7333, "value": 8.14, "curve": [1.767, 11.86, 1.8, 15.08]}, {"time": 1.8333, "value": 17.42, "curve": [1.866, 19.94, 1.9, 21.5]}, {"time": 1.9333, "value": 21.68, "curve": [2.089, 21.68, 2.388, -24.06]}, {"time": 2.5667, "value": -31.53, "curve": [2.578, -32.16, 2.589, -32.63]}, {"time": 2.6, "value": -32.9, "curve": [2.623, -32.88, 2.645, -32.43]}, {"time": 2.6667, "value": -31.65, "curve": [2.701, -30.39, 2.734, -28.31]}, {"time": 2.7667, "value": -25.72, "curve": [2.801, -22.89, 2.834, -19.49]}, {"time": 2.8667, "value": -15.87, "curve": [2.901, -11.98, 2.934, -7.85]}, {"time": 2.9667, "value": -3.82, "curve": [3, 0.41, 3.034, 4.52]}, {"time": 3.0667, "value": 8.14, "curve": [3.1, 11.86, 3.133, 15.08]}, {"time": 3.1667, "value": 17.42, "curve": [3.2, 19.94, 3.233, 21.5]}, {"time": 3.2667, "value": 21.68, "curve": [3.422, 21.68, 3.721, -24.06]}, {"time": 3.9, "value": -31.53, "curve": [4.061, -20.98, 4.217, -13.28]}, {"time": 4.3667, "value": -8.76, "curve": [4.442, -8.76, 4.592, 0]}, {"time": 4.6667}]}, "duoi": {"rotate": [{"curve": [0.308, 0, 0.925, -0.87]}, {"time": 1.2333, "value": -0.87, "curve": [1.496, -0.93, 1.687, -10.83]}, {"time": 1.9, "value": -11.03, "curve": [2.163, -10.96, 2.353, -1.06]}, {"time": 2.5667, "value": -0.87, "curve": [2.733, -0.87, 3.067, -11.03]}, {"time": 3.2333, "value": -11.03, "curve": [3.496, -10.96, 3.687, -1.06]}, {"time": 3.9, "value": -0.87, "curve": [4.092, -0.87, 4.475, 0]}, {"time": 4.6667}]}, "tai6": {"rotate": [{"time": 2.1667, "curve": [2.233, 0, 2.367, 3.64]}, {"time": 2.4333, "value": 3.64, "curve": [2.5, 3.64, 2.633, -5.66]}, {"time": 2.7, "value": -5.66, "curve": [2.758, -5.66, 2.875, 3.64]}, {"time": 2.9333, "value": 3.64, "curve": [3, 3.64, 3.133, -5.66]}, {"time": 3.2, "value": -5.66, "curve": [3.258, -5.66, 3.375, 3.64]}, {"time": 3.4333, "value": 3.64, "curve": [3.517, 3.64, 3.683, 0]}, {"time": 3.7667}]}, "tai7": {"rotate": [{"time": 2.2333, "curve": [2.3, 0, 2.433, 3.64]}, {"time": 2.5, "value": 3.64, "curve": [2.567, 3.64, 2.7, -5.66]}, {"time": 2.7667, "value": -5.66, "curve": [2.825, -5.66, 2.942, 3.64]}, {"time": 3, "value": 3.64, "curve": [3.067, 3.64, 3.2, -5.66]}, {"time": 3.2667, "value": -5.66, "curve": [3.325, -5.66, 3.442, 3.64]}, {"time": 3.5, "value": 3.64, "curve": [3.583, 3.64, 3.75, 0]}, {"time": 3.8333}]}, "tai8": {"rotate": [{"time": 2.3, "curve": [2.367, 0, 2.5, 3.64]}, {"time": 2.5667, "value": 3.64, "curve": [2.633, 3.64, 2.767, -5.66]}, {"time": 2.8333, "value": -5.66, "curve": [2.892, -5.66, 3.008, 3.64]}, {"time": 3.0667, "value": 3.64, "curve": [3.133, 3.64, 3.267, -5.66]}, {"time": 3.3333, "value": -5.66, "curve": [3.392, -5.66, 3.508, 3.64]}, {"time": 3.5667, "value": 3.64, "curve": [3.65, 3.64, 3.817, 0]}, {"time": 3.9}]}, "tai5": {"rotate": [{"time": 2.1, "curve": [2.167, 0, 2.3, 42.15]}, {"time": 2.3667, "value": 42.15, "curve": [2.433, 42.15, 2.567, -71.65]}, {"time": 2.6333, "value": -71.65, "curve": [2.692, -71.65, 2.808, 42.15]}, {"time": 2.8667, "value": 42.15, "curve": [2.933, 42.15, 3.067, -71.65]}, {"time": 3.1333, "value": -71.65, "curve": [3.192, -71.65, 3.308, 42.15]}, {"time": 3.3667, "value": 42.15, "curve": [3.45, 42.15, 3.617, 0]}, {"time": 3.7}]}, "con nguoi1": {"translate": [{"x": 3.8, "y": 2.21, "curve": [0.076, 3.8, 0.167, 1.41, 0.076, 2.21, 0.167, 1.55]}, {"time": 0.2667, "x": -2.39, "y": 0.5, "curve": [0.374, -2.85, 0.519, -3.98, 0.374, 0.69, 0.519, 1.17]}, {"time": 0.6, "x": -3.98, "y": 1.17, "curve": [0.75, -0.1, 0.953, 9.35, 0.75, 0.08, 0.953, -2.58]}, {"time": 1.0667, "x": 9.35, "y": -2.58, "curve": [1.292, 9.35, 1.742, 3.8, 1.292, -2.58, 1.742, 2.21]}, {"time": 1.9667, "x": 3.8, "y": 2.21, "curve": [2.231, 3.8, 2.55, 6.04, 2.231, 2.21, 2.55, 1.01]}, {"time": 2.9, "x": 9.59, "y": -0.9, "curve": "stepped"}, {"time": 3.8, "x": 9.59, "y": -0.9, "curve": [3.908, 9.59, 4.125, 3.8, 3.908, -0.9, 4.125, 2.21]}, {"time": 4.2333, "x": 3.8, "y": 2.21}]}, "tai1": {"rotate": [{"curve": [0.217, 0, 0.65, -5.09]}, {"time": 0.8667, "value": -5.09, "curve": [1.167, -5.09, 1.767, 7.47]}, {"time": 2.0667, "value": 7.47, "curve": [2.717, 7.47, 4.017, 0]}, {"time": 4.6667}]}, "tai4": {"rotate": [{"curve": [0.217, 0, 0.65, -5.09]}, {"time": 0.8667, "value": -5.09, "curve": [1.167, -5.09, 1.767, 7.47]}, {"time": 2.0667, "value": 7.47, "curve": [2.717, 7.47, 4.017, 0]}, {"time": 4.6667}]}, "tai2": {"rotate": [{"curve": [0.217, 0, 0.65, -5.09]}, {"time": 0.8667, "value": -5.09, "curve": [1.167, -5.09, 1.767, 7.47]}, {"time": 2.0667, "value": 7.47, "curve": [2.717, 7.47, 4.017, 0]}, {"time": 4.6667}]}, "tai3": {"rotate": [{"curve": [0.217, 0, 0.65, -5.09]}, {"time": 0.8667, "value": -5.09, "curve": [1.167, -5.09, 1.767, 7.47]}, {"time": 2.0667, "value": 7.47, "curve": [2.717, 7.47, 4.017, 0]}, {"time": 4.6667}]}, "Layer 13": {"rotate": [{"time": 0.7667}, {"time": 1.1667, "value": 6.59}, {"time": 1.6667}, {"time": 2.0667, "value": 6.59}], "translate": [{"time": 0.7667}, {"time": 0.9667, "x": 2.99, "y": -12.47}, {"time": 1.1667, "x": 3.48, "y": 14.56}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6667}, {"time": 1.8667, "x": 2.99, "y": -12.47}, {"time": 2.0667, "x": 3.48, "y": 14.56}, {"time": 2.4}], "scale": [{"time": 0.1333}, {"time": 0.5, "x": 1.7}, {"time": 0.7667, "x": 0.34}, {"time": 0.9667, "x": 1.394}, {"time": 1.1667, "x": 0.163}, {"time": 1.5}, {"time": 1.6667, "x": 0.34}, {"time": 1.8667, "x": 1.394}, {"time": 2.0667, "x": 0.163}, {"time": 2.4}]}, "Layer 7 copy": {"translate": [{}, {"time": 0.2, "x": 5.46, "y": -9.03, "curve": "stepped"}, {"time": 1.1333, "x": 5.46, "y": -9.03}, {"time": 1.4333, "x": 11.03, "y": 5.76, "curve": "stepped"}, {"time": 3.3, "x": 11.03, "y": 5.76}, {"time": 3.9}]}, "Layer 7 copy2": {"translate": [{}, {"time": 0.2, "x": 6.71, "y": -10.02, "curve": "stepped"}, {"time": 1.1333, "x": 6.71, "y": -10.02}, {"time": 1.4333, "x": 13.53, "y": 6.4, "curve": "stepped"}, {"time": 3.3, "x": 13.53, "y": 6.4}, {"time": 3.9}]}}}, "entry": {"bones": {"bone": {"translate": [{"x": -768.24, "y": -5.94}, {"time": 0.6667, "x": 2.97, "y": -5.94}]}, "body": {"rotate": [{"value": 11.02}, {"time": 0.3333, "value": 11.73}, {"time": 0.6667, "value": 5.04}, {"time": 0.8, "value": -21.03}, {"time": 1.1}], "translate": [{"curve": [0, 10.33, 0.222, -3.96, 0, -10.33, 0.222, 164.34]}, {"time": 0.3333, "x": -3.96, "y": 164.34, "curve": [0.444, -3.96, 0.667, 0.82, 0.444, 164.34, 0.667, 63.01]}, {"time": 0.6667, "x": 1.81, "y": 21.75, "curve": [0.667, 2.81, 0.756, -1.81, 0.667, 19.94, 0.756, -19.94]}, {"time": 0.8, "x": -1.81, "y": -19.94, "curve": [0.9, -1.81, 1, -7.69, 0.9, -19.94, 1, 0]}, {"time": 1.1, "x": -7.69}]}, "dui sau4": {"rotate": [{}, {"time": 0.1333, "value": -106.66}, {"time": 0.6, "value": -136.68}, {"time": 0.8333}], "translate": [{}, {"time": 0.1333, "x": -130.68, "y": 142.56}, {"time": 0.1667, "x": -156.56, "y": 142.94}, {"time": 0.3333, "x": -145.29, "y": 177.32}, {"time": 0.6, "x": -131.73, "y": 198.88}, {"time": 0.8333}]}, "dui sau8": {"rotate": [{}, {"time": 0.1333, "value": -106.66}, {"time": 0.6, "value": -136.68}, {"time": 0.8333}], "translate": [{}, {"time": 0.1333, "x": -130.68, "y": 142.56}, {"time": 0.1667, "x": -156.56, "y": 142.94}, {"time": 0.3333, "x": -145.29, "y": 177.32}, {"time": 0.6, "x": -131.73, "y": 198.88}, {"time": 0.8333}]}, "dui truoc6": {"rotate": [{}, {"time": 0.2, "value": 57.79}, {"time": 0.6667, "value": -6.09}, {"time": 0.8}], "translate": [{}, {"time": 0.2, "x": 176.25, "y": 305.94}, {"time": 0.4, "x": 212.32, "y": 332.99}, {"time": 0.6667, "x": 72.04, "y": 12.1}, {"time": 0.8}]}, "dui truoc3": {"rotate": [{}, {"time": 0.2, "value": 57.79}, {"time": 0.6667, "value": -6.09}, {"time": 0.8}], "translate": [{}, {"time": 0.2, "x": 176.25, "y": 305.94}, {"time": 0.4, "x": 212.32, "y": 332.99}, {"time": 0.6667, "x": 72.04, "y": 12.1}, {"time": 0.8}]}, "dui sau1": {"rotate": [{}, {"time": 0.2, "value": -30.02}, {"time": 0.4667, "value": -28.52}, {"time": 0.7, "value": 16.79}, {"time": 0.9333}]}, "dui sau5": {"rotate": [{}, {"time": 0.2, "value": -30.02}, {"time": 0.4667, "value": -28.52}, {"time": 0.7, "value": 16.79}, {"time": 0.9333}]}, "body5": {"rotate": [{"time": 0.3333, "curve": [0.422, 0, 0.511, 16.25]}, {"time": 0.6, "value": 16.25, "curve": [0.689, 16.25, 0.778, -13.27]}, {"time": 0.8667, "value": -13.27, "curve": [0.978, -13.27, 1.089, 0]}, {"time": 1.2}], "translate": [{"time": 0.3333, "curve": [0.422, 0, 0.511, 6.17, 0.422, 0, 0.511, 3.6]}, {"time": 0.6, "x": 6.17, "y": 3.6, "curve": [0.689, 6.17, 0.778, 21.1, 0.689, 3.6, 0.778, -80.58]}, {"time": 0.8667, "x": 21.1, "y": -80.58, "curve": [0.978, 21.1, 1.089, 0, 0.978, -80.58, 1.089, 0]}, {"time": 1.2}]}, "Layer 8": {"rotate": [{"value": 84.21}, {"time": 0.9667}]}}}, "exit": {"slots": {"inner_eye_left_white": {"attachment": [{}]}, "inner_eye_right_white": {"attachment": [{}]}}, "bones": {"body": {"rotate": [{"value": -0.49}], "translate": [{"x": -20.86, "y": -84.83}]}, "dui sau4": {"rotate": [{"value": 36.94}], "translate": [{"x": 84.72, "y": -29.96}]}, "dui sau8": {"translate": [{"x": 54.24, "y": 2.78}]}, "dui sau1": {"rotate": [{"value": 15.88}]}, "dui sau5": {"rotate": [{"value": 15.88}]}, "body3": {"rotate": [{"value": -3.28}]}, "body4": {"rotate": [{"value": -3.28, "curve": [0.333, -3.28, 0.667, -3.25]}, {"time": 1, "value": -3.25, "curve": [1.333, -3.25, 1.667, -3.28]}, {"time": 2, "value": -3.28}]}, "body5": {"rotate": [{"value": -71.74, "curve": [0.136, -72.76, 0.268, -73.63]}, {"time": 0.4, "value": -73.63, "curve": [0.733, -73.63, 1.067, -68.25]}, {"time": 1.4, "value": -68.25, "curve": [1.601, -68.25, 1.802, -70.18]}, {"time": 2, "value": -71.74}], "translate": [{"x": 26.12, "y": -61.44, "curve": [0.333, 26.12, 0.667, 23.39, 0.333, -61.44, 0.667, -62.53]}, {"time": 1, "x": 23.39, "y": -62.53, "curve": [1.333, 23.39, 1.667, 26.12, 1.333, -62.53, 1.667, -61.44]}, {"time": 2, "x": 26.12, "y": -61.44}]}, "dui truoc3": {"rotate": [{"value": 35.08}], "translate": [{"x": 111.25, "y": -41.72}]}, "dui truoc6": {"rotate": [{"value": 24.09}], "translate": [{"x": 114.03, "y": 1.39}]}, "dui truoc1": {"translate": [{"x": -8.55, "y": -50.81}]}, "duoi": {"rotate": [{"value": 92.02}], "translate": [{"x": 0.09, "y": 3.31}]}, "duoi2": {"rotate": [{"value": 19.52}]}, "duoi3": {"rotate": [{"value": 19.52}]}, "duoi4": {"rotate": [{"value": 17.86, "curve": [0.333, 17.86, 0.667, 21.69]}, {"time": 1, "value": 21.69, "curve": [1.333, 21.69, 1.667, 17.86]}, {"time": 2, "value": 17.86}]}, "duoi5": {"rotate": [{"value": 18.26, "curve": [0.069, 18.02, 0.134, 17.86]}, {"time": 0.2, "value": 17.86, "curve": [0.533, 17.86, 0.867, 21.69]}, {"time": 1.2, "value": 21.69, "curve": [1.468, 21.69, 1.736, 19.23]}, {"time": 2, "value": 18.26}]}, "duoi6": {"rotate": [{"value": 19.21, "curve": [0.136, 18.48, 0.268, 17.86]}, {"time": 0.4, "value": 17.86, "curve": [0.733, 17.86, 1.067, 21.69]}, {"time": 1.4, "value": 21.69, "curve": [1.601, 21.69, 1.802, 20.32]}, {"time": 2, "value": 19.21}]}, "duoi7": {"rotate": [{"value": -11.21, "curve": [0.202, -12.31, 0.401, -13.69]}, {"time": 0.6, "value": -13.69, "curve": [0.933, -13.69, 1.267, -9.86]}, {"time": 1.6, "value": -9.86, "curve": [1.734, -9.86, 1.869, -10.46]}, {"time": 2, "value": -11.21}]}, "body2": {"rotate": [{"value": 14.02}]}, "Layer 13": {"scale": [{"x": 0.247}]}, "tai1": {"rotate": [{"value": 60.69, "curve": [0.333, 60.69, 0.667, 74.53]}, {"time": 1, "value": 74.53, "curve": [1.333, 74.53, 1.667, 60.69]}, {"time": 2, "value": 60.69}], "translate": [{"x": 13.4, "y": 3.2}]}, "tai5": {"rotate": [{"value": 53.03}]}, "tai2": {"rotate": [{"value": -6.84, "curve": [0.069, -7.7, 0.134, -8.28]}, {"time": 0.2, "value": -8.28, "curve": [0.533, -8.28, 0.867, 5.57]}, {"time": 1.2, "value": 5.57, "curve": [1.468, 5.57, 1.736, -3.32]}, {"time": 2, "value": -6.84}]}, "tai3": {"rotate": [{"value": -3.4, "curve": [0.136, -6.03, 0.268, -8.28]}, {"time": 0.4, "value": -8.28, "curve": [0.733, -8.28, 1.067, 5.57]}, {"time": 1.4, "value": 5.57, "curve": [1.601, 5.57, 1.802, 0.6]}, {"time": 2, "value": -3.4}]}, "tai4": {"rotate": [{"value": 0.7, "curve": [0.202, -3.28, 0.401, -8.28]}, {"time": 0.6, "value": -8.28, "curve": [0.933, -8.28, 1.267, 5.57]}, {"time": 1.6, "value": 5.57, "curve": [1.734, 5.57, 1.869, 3.38]}, {"time": 2, "value": 0.7}]}, "tai6": {"rotate": [{"value": 5.18, "curve": [0.036, 5.41, 0.068, 5.57]}, {"time": 0.1, "value": 5.57, "curve": [0.368, 5.57, 0.636, -3.32]}, {"time": 0.9, "value": -6.84, "curve": [0.969, -7.7, 1.034, -8.28]}, {"time": 1.1, "value": -8.28, "curve": [1.401, -8.28, 1.702, 3]}, {"time": 2, "value": 5.18}]}, "tai7": {"rotate": [{"value": 2.58, "curve": [0.102, 4.29, 0.201, 5.57]}, {"time": 0.3, "value": 5.57, "curve": [0.501, 5.57, 0.702, 0.6]}, {"time": 0.9, "value": -3.4, "curve": [1.036, -6.03, 1.168, -8.28]}, {"time": 1.3, "value": -8.28, "curve": [1.534, -8.28, 1.769, -1.49]}, {"time": 2, "value": 2.58}]}, "tai8": {"rotate": [{"value": -1.35, "curve": [0.169, 2.08, 0.334, 5.57]}, {"time": 0.5, "value": 5.57, "curve": [0.634, 5.57, 0.769, 3.38]}, {"time": 0.9, "value": 0.7, "curve": [1.102, -3.28, 1.301, -8.28]}, {"time": 1.5, "value": -8.28, "curve": [1.668, -8.28, 1.836, -4.84]}, {"time": 2, "value": -1.35}]}, "duoi8": {"rotate": [{"value": 1.78, "curve": [0.269, 0.79, 0.534, -1.66]}, {"time": 0.8, "value": -1.66, "curve": [1.133, -1.66, 1.467, 2.17]}, {"time": 1.8, "value": 2.17, "curve": [1.868, 2.17, 1.936, 2.03]}, {"time": 2, "value": 1.78}]}, "long duoi": {"rotate": [{"value": 2.17, "curve": [0.333, 2.17, 0.667, -1.66]}, {"time": 1, "value": -1.66, "curve": [1.333, -1.66, 1.667, 2.17]}, {"time": 2, "value": 2.17}]}, "long duoi2": {"rotate": [{"value": 1.78, "curve": [0.069, 2.01, 0.134, 2.17]}, {"time": 0.2, "value": 2.17, "curve": [0.533, 2.17, 0.867, -1.66]}, {"time": 1.2, "value": -1.66, "curve": [1.468, -1.66, 1.736, 0.8]}, {"time": 2, "value": 1.78}]}}, "attachments": {"default": {"mouth_white": {"mouth_white": {"deform": [{"offset": 52, "vertices": [-1.35876, 0.02233, -5.49318, -0.10747, -1.32578, -0.21078, -5.26973, -0.33637, 0, 0, -1.83103, -0.03579, -2.7853, -0.16488, -2.13199, -0.02937, 0, 0, 0, 0, 0, 0, -1.20711, 0.66505, -5.12986, 0.54693, -2.65375, 0.75754, -11.00497, 0.5008, -3.52528, 0.91753, -14.58486, 0.57683, -4.94286, 1.53806, -20.54697, 1.05919, -6.6226, 1.00282, -27.12002, 0.36599, -2.5898, 1.10607, -10.88182, 0.8538, -1.25929, 0.50156]}]}}}}}, "idle": {"bones": {"duoi": {"rotate": [{"value": -0.87, "curve": [0.263, -0.93, 0.453, -10.83]}, {"time": 0.6667, "value": -11.03, "curve": [0.929, -10.96, 1.12, -1.06]}, {"time": 1.3333, "value": -0.87}]}, "duoi2": {"rotate": [{"value": -1.66, "curve": [0.033, -1.19, 0.066, -0.9]}, {"time": 0.1, "value": -0.87, "curve": [0.267, -0.87, 0.6, -11.03]}, {"time": 0.7667, "value": -11.03, "curve": [0.988, -10.97, 1.158, -3.9]}, {"time": 1.3333, "value": -1.66}]}, "duoi3": {"rotate": [{"value": -0.84, "curve": [0.033, 2.08, 0.067, 4.61]}, {"time": 0.1, "value": 6.44, "curve": [0.133, 8.42, 0.166, 9.64]}, {"time": 0.2, "value": 9.78, "curve": [0.365, 9.78, 0.691, -30.36]}, {"time": 0.8667, "value": -33.04, "curve": [1.046, -32.86, 1.191, -13.01]}, {"time": 1.3333, "value": -0.84}]}, "duoi4": {"rotate": [{"value": -3.82, "curve": [0.034, 0.41, 0.067, 4.52]}, {"time": 0.1, "value": 8.14, "curve": [0.133, 11.86, 0.167, 15.08]}, {"time": 0.2, "value": 17.42, "curve": [0.233, 19.94, 0.266, 21.5]}, {"time": 0.3, "value": 21.68, "curve": [0.465, 21.68, 0.791, -29.49]}, {"time": 0.9667, "value": -32.9, "curve": [1.105, -32.73, 1.222, -17.58]}, {"time": 1.3333, "value": -3.82}]}, "duoi5": {"rotate": [{"value": -15.87, "curve": [0.034, -11.98, 0.067, -7.85]}, {"time": 0.1, "value": -3.82, "curve": [0.134, 0.41, 0.167, 4.52]}, {"time": 0.2, "value": 8.14, "curve": [0.233, 11.86, 0.267, 15.08]}, {"time": 0.3, "value": 17.42, "curve": [0.333, 19.94, 0.366, 21.5]}, {"time": 0.4, "value": 21.68, "curve": [0.565, 21.68, 0.891, -29.49]}, {"time": 1.0667, "value": -32.9, "curve": [1.164, -32.78, 1.251, -25.1]}, {"time": 1.3333, "value": -15.87}]}, "duoi6": {"rotate": [{"value": -25.72, "curve": [0.034, -22.89, 0.068, -19.49]}, {"time": 0.1, "value": -15.87, "curve": [0.134, -11.98, 0.167, -7.85]}, {"time": 0.2, "value": -3.82, "curve": [0.234, 0.41, 0.267, 4.52]}, {"time": 0.3, "value": 8.14, "curve": [0.333, 11.86, 0.367, 15.08]}, {"time": 0.4, "value": 17.42, "curve": [0.433, 19.94, 0.466, 21.5]}, {"time": 0.5, "value": 21.68, "curve": [0.665, 21.68, 0.991, -29.49]}, {"time": 1.1667, "value": -32.9, "curve": [1.226, -32.83, 1.281, -29.98]}, {"time": 1.3333, "value": -25.72}]}, "duoi7": {"rotate": [{"value": -31.65, "curve": [0.035, -30.39, 0.068, -28.31]}, {"time": 0.1, "value": -25.72, "curve": [0.134, -22.89, 0.168, -19.49]}, {"time": 0.2, "value": -15.87, "curve": [0.234, -11.98, 0.267, -7.85]}, {"time": 0.3, "value": -3.82, "curve": [0.334, 0.41, 0.367, 4.52]}, {"time": 0.4, "value": 8.14, "curve": [0.433, 11.86, 0.467, 15.08]}, {"time": 0.5, "value": 17.42, "curve": [0.533, 19.94, 0.566, 21.5]}, {"time": 0.6, "value": 21.68, "curve": [0.765, 21.68, 1.091, -29.49]}, {"time": 1.2667, "value": -32.9, "curve": [1.289, -32.88, 1.312, -32.43]}, {"time": 1.3333, "value": -31.65}]}, "duoi8": {"rotate": [{"value": -31.53, "curve": [0.012, -32.16, 0.023, -32.63]}, {"time": 0.0333, "value": -32.9, "curve": [0.056, -32.88, 0.078, -32.43]}, {"time": 0.1, "value": -31.65, "curve": [0.135, -30.39, 0.168, -28.31]}, {"time": 0.2, "value": -25.72, "curve": [0.234, -22.89, 0.268, -19.49]}, {"time": 0.3, "value": -15.87, "curve": [0.334, -11.98, 0.367, -7.85]}, {"time": 0.4, "value": -3.82, "curve": [0.434, 0.41, 0.467, 4.52]}, {"time": 0.5, "value": 8.14, "curve": [0.533, 11.86, 0.567, 15.08]}, {"time": 0.6, "value": 17.42, "curve": [0.633, 19.94, 0.666, 21.5]}, {"time": 0.7, "value": 21.68, "curve": [0.856, 21.68, 1.154, -24.06]}, {"time": 1.3333, "value": -31.53}]}, "long duoi": {"rotate": [{"value": -31.53, "curve": [0.012, -32.16, 0.023, -32.63]}, {"time": 0.0333, "value": -32.9, "curve": [0.056, -32.88, 0.078, -32.43]}, {"time": 0.1, "value": -31.65, "curve": [0.135, -30.39, 0.168, -28.31]}, {"time": 0.2, "value": -25.72, "curve": [0.234, -22.89, 0.268, -19.49]}, {"time": 0.3, "value": -15.87, "curve": [0.334, -11.98, 0.367, -7.85]}, {"time": 0.4, "value": -3.82, "curve": [0.434, 0.41, 0.467, 4.52]}, {"time": 0.5, "value": 8.14, "curve": [0.533, 11.86, 0.567, 15.08]}, {"time": 0.6, "value": 17.42, "curve": [0.633, 19.94, 0.666, 21.5]}, {"time": 0.7, "value": 21.68, "curve": [0.856, 21.68, 1.154, -24.06]}, {"time": 1.3333, "value": -31.53}]}, "long duoi2": {"rotate": [{"value": -31.53, "curve": [0.012, -32.16, 0.023, -32.63]}, {"time": 0.0333, "value": -32.9, "curve": [0.056, -32.88, 0.078, -32.43]}, {"time": 0.1, "value": -31.65, "curve": [0.135, -30.39, 0.168, -28.31]}, {"time": 0.2, "value": -25.72, "curve": [0.234, -22.89, 0.268, -19.49]}, {"time": 0.3, "value": -15.87, "curve": [0.334, -11.98, 0.367, -7.85]}, {"time": 0.4, "value": -3.82, "curve": [0.434, 0.41, 0.467, 4.52]}, {"time": 0.5, "value": 8.14, "curve": [0.533, 11.86, 0.567, 15.08]}, {"time": 0.6, "value": 17.42, "curve": [0.633, 19.94, 0.666, 21.5]}, {"time": 0.7, "value": 21.68, "curve": [0.856, 21.68, 1.154, -24.06]}, {"time": 1.3333, "value": -31.53}]}, "body5": {"rotate": [{"value": -12.34, "curve": [0.222, -12.34, 0.444, -6.28]}, {"time": 0.6667, "value": -6.28, "curve": [0.889, -6.28, 1.111, -12.34]}, {"time": 1.3333, "value": -12.34}]}, "dau": {"rotate": [{"value": -10.22, "curve": [0.167, -10.22, 0.5, 13.12]}, {"time": 0.6667, "value": 13.12, "curve": [0.833, 13.12, 1.167, -10.22]}, {"time": 1.3333, "value": -10.22}], "translate": [{"x": -27.01, "y": 1.94, "curve": [0.167, -27.01, 0.5, -1.14, 0.167, 1.94, 0.5, 4.66]}, {"time": 0.6667, "x": -1.14, "y": 4.66, "curve": [0.833, -1.14, 1.167, -27.01, 0.833, 4.66, 1.167, 1.94]}, {"time": 1.3333, "x": -27.01, "y": 1.94}]}, "mat2": {"rotate": [{"value": 6.45, "curve": [0.167, 6.45, 0.5, 7.94]}, {"time": 0.6667, "value": 7.94, "curve": [0.833, 7.94, 1.167, 6.45]}, {"time": 1.3333, "value": 6.45}], "translate": [{"x": -2.61, "y": -1.14, "curve": [0.167, -2.61, 0.5, 10.33, 0.167, -1.14, 0.5, 14.2]}, {"time": 0.6667, "x": 10.33, "y": 14.2, "curve": [0.833, 10.33, 1.167, -2.61, 0.833, 14.2, 1.167, -1.14]}, {"time": 1.3333, "x": -2.61, "y": -1.14}], "scale": [{"y": 1.046, "curve": [0.167, 1, 0.5, 1, 0.167, 1.046, 0.5, 0.746]}, {"time": 0.6667, "y": 0.746, "curve": [0.833, 1, 1.167, 1, 0.833, 0.746, 1.167, 1.046]}, {"time": 1.3333, "y": 1.046}]}, "sung1": {"translate": [{"x": -5.83, "y": -5.97, "curve": [0.167, -5.83, 0.5, 13.89, 0.167, -5.97, 0.5, 6.6]}, {"time": 0.6667, "x": 13.89, "y": 6.6, "curve": [0.833, 13.89, 1.167, -5.83, 0.833, 6.6, 1.167, -5.97]}, {"time": 1.3333, "x": -5.83, "y": -5.97}]}, "tai1": {"translate": [{"x": 12.92, "y": -7.57, "curve": [0.167, 12.92, 0.5, 16.51, 0.167, -7.57, 0.5, 7.09]}, {"time": 0.6667, "x": 16.51, "y": 7.09, "curve": [0.833, 16.51, 1.111, 12.92, 0.833, 7.09, 1.111, -7.57]}, {"time": 1.3333, "x": 12.92, "y": -7.57}], "scale": [{"curve": [0.167, 1, 0.5, 0.94, 0.167, 1, 0.5, 1]}, {"time": 0.6667, "x": 0.94, "curve": [0.833, 0.94, 1.111, 1, 0.833, 1, 1.111, 1]}, {"time": 1.3333}]}, "tai5": {"scale": [{"curve": [0.167, 1, 0.5, 0.658, 0.167, 1, 0.5, 1]}, {"time": 0.6667, "x": 0.658, "curve": [0.833, 0.658, 1.167, 1, 0.833, 1, 1.167, 1]}, {"time": 1.3333}]}, "dui truoc1": {"translate": [{"x": -8.01, "y": 0.13, "curve": [0.167, -8.01, 0.5, 4.66, 0.167, 0.13, 0.5, -1.06]}, {"time": 0.6667, "x": 4.66, "y": -1.06, "curve": [0.833, 4.66, 1.167, -8.01, 0.833, -1.06, 1.167, 0.13]}, {"time": 1.3333, "x": -8.01, "y": 0.13}]}, "dui truoc4": {"translate": [{"x": 5.34, "y": -0.09, "curve": [0.167, 5.34, 0.5, -4.01, 0.167, -0.09, 0.5, 0.06]}, {"time": 0.6667, "x": -4.01, "y": 0.06, "curve": [0.833, -4.01, 1.167, 5.34, 0.833, 0.06, 1.167, -0.09]}, {"time": 1.3333, "x": 5.34, "y": -0.09}]}, "body": {"translate": [{"x": -0.69, "curve": [0.124, 1.19, 0.245, 3.29, 0.124, 0, 0.245, 0]}, {"time": 0.3667, "x": 3.29, "curve": [0.533, 3.29, 0.811, -3.64, 0.533, 0, 0.811, 0]}, {"time": 1.0333, "x": -3.64, "curve": [1.134, -3.64, 1.235, -2.24, 1.134, 0, 1.235, 0]}, {"time": 1.3333, "x": -0.69}]}, "con nguoi1": {"translate": [{"x": 9.9, "y": 3.25, "curve": [0.167, 9.9, 0.5, 7.79, 0.167, 3.25, 0.5, -2.48]}, {"time": 0.6667, "x": 7.79, "y": -2.48, "curve": [0.833, 7.79, 1.167, 9.9, 0.833, -2.48, 1.167, 3.25]}, {"time": 1.3333, "x": 9.9, "y": 3.25}]}, "swng2": {"translate": [{"curve": [0.167, 0, 0.5, -2.74, 0.167, 0, 0.5, -5.71]}, {"time": 0.6667, "x": -2.74, "y": -5.71, "curve": [0.833, -2.74, 1.167, 0, 0.833, -5.71, 1.167, 0]}, {"time": 1.3333}]}, "tai2": {"rotate": [{"value": -0.09, "curve": [0.114, -3.96, 0.224, -7.49]}, {"time": 0.3, "value": -7.49, "curve": [0.429, -7.49, 0.662, 3.11]}, {"time": 0.8333, "value": 7.35, "curve": [0.883, 8.72, 0.929, 9.57]}, {"time": 0.9667, "value": 9.57, "curve": [1.057, 9.57, 1.198, 4.49]}, {"time": 1.3333, "value": -0.09}]}, "tai3": {"rotate": [{"value": 4.37, "curve": [0.154, -0.51, 0.328, -7.49]}, {"time": 0.4333, "value": -7.49, "curve": [0.531, -7.49, 0.688, -1.46]}, {"time": 0.8333, "value": 3.36, "curve": [0.88, 4.91, 0.925, 6.32]}, {"time": 0.9667, "value": 7.35, "curve": [1.017, 8.72, 1.062, 9.57]}, {"time": 1.1, "value": 9.57, "curve": [1.161, 9.57, 1.244, 7.31]}, {"time": 1.3333, "value": 4.37}]}, "tai4": {"rotate": [{"value": 8.15, "curve": [0.173, 4.54, 0.429, -7.49]}, {"time": 0.5667, "value": -7.49, "curve": [0.635, -7.49, 0.732, -4.61]}, {"time": 0.8333, "value": -1.16, "curve": [0.877, 0.31, 0.923, 1.9]}, {"time": 0.9667, "value": 3.36, "curve": [1.013, 4.91, 1.058, 6.32]}, {"time": 1.1, "value": 7.35, "curve": [1.15, 8.72, 1.195, 9.57]}, {"time": 1.2333, "value": 9.57, "curve": [1.263, 9.57, 1.296, 9.04]}, {"time": 1.3333, "value": 8.15}]}, "tai6": {"rotate": [{"value": -0.09, "curve": [0.114, -3.96, 0.224, -7.49]}, {"time": 0.3, "value": -7.49, "curve": [0.429, -7.49, 0.662, 3.11]}, {"time": 0.8333, "value": 7.35, "curve": [0.883, 8.72, 0.929, 9.57]}, {"time": 0.9667, "value": 9.57, "curve": [1.057, 9.57, 1.198, 4.49]}, {"time": 1.3333, "value": -0.09}]}, "tai7": {"rotate": [{"value": 4.37, "curve": [0.154, -0.51, 0.328, -7.49]}, {"time": 0.4333, "value": -7.49, "curve": [0.531, -7.49, 0.688, -1.46]}, {"time": 0.8333, "value": 3.36, "curve": [0.88, 4.91, 0.925, 6.32]}, {"time": 0.9667, "value": 7.35, "curve": [1.017, 8.72, 1.062, 9.57]}, {"time": 1.1, "value": 9.57, "curve": [1.161, 9.57, 1.244, 7.31]}, {"time": 1.3333, "value": 4.37}]}, "tai8": {"rotate": [{"value": 8.15, "curve": [0.173, 4.54, 0.429, -7.49]}, {"time": 0.5667, "value": -7.49, "curve": [0.635, -7.49, 0.732, -4.61]}, {"time": 0.8333, "value": -1.16, "curve": [0.877, 0.31, 0.923, 1.9]}, {"time": 0.9667, "value": 3.36, "curve": [1.013, 4.91, 1.058, 6.32]}, {"time": 1.1, "value": 7.35, "curve": [1.15, 8.72, 1.195, 9.57]}, {"time": 1.2333, "value": 9.57, "curve": [1.263, 9.57, 1.296, 9.04]}, {"time": 1.3333, "value": 8.15}]}, "Layer 12": {"translate": [{"curve": [0.222, 0, 0.444, 14.95, 0.222, 0, 0.444, -18.12]}, {"time": 0.6667, "x": 14.95, "y": -18.12, "curve": [0.889, 14.95, 1.111, 0, 0.889, -18.12, 1.111, 0]}, {"time": 1.3333}], "scale": [{"curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 0.972]}, {"time": 0.6667, "y": 0.972, "curve": [0.889, 1, 1.111, 1, 0.889, 0.972, 1.111, 1]}, {"time": 1.3333}]}, "Layer 7": {"translate": [{"curve": [0.222, 0, 0.444, 9.16, 0.222, 0, 0.444, -10.4]}, {"time": 0.6667, "x": 9.16, "y": -10.4, "curve": [0.889, 9.16, 1.111, 0, 0.889, -10.4, 1.111, 0]}, {"time": 1.3333}], "scale": [{"curve": [0.222, 1, 0.444, 0.991, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 0.991, "curve": [0.889, 0.991, 1.111, 1, 0.889, 1, 1.111, 1]}, {"time": 1.3333}]}, "Layer 11": {"translate": [{"curve": [0.222, 0, 0.444, 9.16, 0.222, 0, 0.444, -10.4]}, {"time": 0.6667, "x": 9.16, "y": -10.4, "curve": [0.889, 9.16, 1.111, 0, 0.889, -10.4, 1.111, 0]}, {"time": 1.3333}], "scale": [{"curve": [0.222, 1, 0.444, 0.991, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 0.991, "curve": [0.889, 0.991, 1.111, 1, 0.889, 1, 1.111, 1]}, {"time": 1.3333}]}, "body3": {"rotate": [{"value": -0.4, "curve": [0.068, -0.5, 0.134, -0.58]}, {"time": 0.2, "value": -0.58, "curve": [0.422, -0.58, 0.644, 0.24]}, {"time": 0.8667, "value": 0.24, "curve": [1.023, 0.24, 1.179, -0.16]}, {"time": 1.3333, "value": -0.4}]}, "body4": {"rotate": [{"value": 0.01, "curve": [0.146, -0.23, 0.29, -0.58]}, {"time": 0.4333, "value": -0.58, "curve": [0.656, -0.58, 0.878, 0.24]}, {"time": 1.1, "value": 0.24, "curve": [1.179, 0.24, 1.257, 0.14]}, {"time": 1.3333, "value": 0.01}]}, "body2": {"rotate": [{"value": -1.38, "curve": [0.068, -1.98, 0.134, -2.43]}, {"time": 0.2, "value": -2.43, "curve": [0.422, -2.43, 0.644, 2.45]}, {"time": 0.8667, "value": 2.45, "curve": [1.023, 2.45, 1.179, 0.06]}, {"time": 1.3333, "value": -1.38}]}}, "attachments": {"default": {"head_white": {"head_white": {"deform": [{"curve": [0.222, 0, 0.444, 1]}, {"time": 0.6667, "offset": 74, "vertices": [13.08, -14.85075, 14.83376, -13.64953, 12.04426, -10.48237, 6.33929, -6.13352, 22.83023, -12.40704, 17.14056, -9.52249], "curve": [0.889, 0, 1.111, 1]}, {"time": 1.3333}]}}}}}, "walk": {"bones": {"body": {"translate": [{"y": 1.67, "curve": [0.05, 0, 0.15, 0, 0.05, 1.67, 0.15, -13.33]}, {"time": 0.2, "y": -13.33, "curve": [0.25, 0, 0.35, 0, 0.25, -13.33, 0.35, 1.67]}, {"time": 0.4, "y": 1.67, "curve": [0.45, 0, 0.55, 0, 0.45, 1.67, 0.55, -13.33]}, {"time": 0.6, "y": -13.33, "curve": [0.65, 0, 0.75, 0, 0.65, -13.33, 0.75, 1.67]}, {"time": 0.8, "y": 1.67}]}, "dui sau8": {"rotate": [{"value": -16.91, "curve": [0.025, -16.91, 0.075, -41.64]}, {"time": 0.1, "value": -41.64, "curve": [0.111, -43.34, 0.122, -45.28]}, {"time": 0.1333, "value": -47.44, "curve": [0.165, -38.67, 0.198, -27.27]}, {"time": 0.2333, "value": -14.22, "curve": [0.287, -6.09, 0.343, 3.74]}, {"time": 0.4, "value": 13.57, "curve": [0.434, 4.52, 0.467, -4.52]}, {"time": 0.5, "value": -13, "curve": [0.615, -15.17, 0.722, -16.91]}, {"time": 0.8, "value": -16.91}], "translate": [{"x": -81.64, "y": 12.24, "curve": [0.058, -81.64, 0.175, -1.62, 0.058, 12.24, 0.175, 22.42]}, {"time": 0.2333, "x": -1.62, "y": 22.42, "curve": [0.297, 23.34, 0.357, 44.73, 0.297, 16.24, 0.357, 10.95]}, {"time": 0.4, "x": 44.73, "y": 10.95, "curve": [0.428, 44.73, 0.462, 35.51, 0.428, 10.95, 0.462, 6.02]}, {"time": 0.5, "x": 21.68, "y": -1.38, "curve": [0.563, -2.95, 0.637, -44.01, 0.563, -1.28, 0.637, -1.11]}, {"time": 0.7, "x": -68.64, "y": -1.01, "curve": [0.738, -76.44, 0.772, -81.64, 0.738, 6.94, 0.772, 12.24]}, {"time": 0.8, "x": -81.64, "y": 12.24}]}, "dui truoc6": {"rotate": [{"value": 4.35, "curve": [0.039, 1.84, 0.073, 0]}, {"time": 0.1, "curve": [0.109, 0, 0.121, -6.44]}, {"time": 0.1333, "value": -16.09, "curve": [0.167, -29.8, 0.209, -57.21]}, {"time": 0.2333, "value": -57.21, "curve": [0.3, -57.21, 0.433, 23.56]}, {"time": 0.5, "value": 23.56, "curve": [0.573, 23.56, 0.699, 11.13]}, {"time": 0.8, "value": 4.35}], "translate": [{"x": -45.21, "y": 7.07, "curve": [0.039, -56.87, 0.073, -65.37, 0.039, 7.91, 0.073, 8.53]}, {"time": 0.1, "x": -65.37, "y": 8.53, "curve": [0.11, -65.37, 0.121, -63.59, 0.11, 8.53, 0.121, 10.25]}, {"time": 0.1333, "x": -60.34, "y": 13.42, "curve": [0.162, -54.55, 0.197, -40.87, 0.162, 14.15, 0.197, 15.89]}, {"time": 0.2333, "x": -24.04, "y": 18.02, "curve": [0.327, 17.68, 0.435, 80.26, 0.327, 15.69, 0.435, 12.19]}, {"time": 0.5, "x": 80.26, "y": 12.19, "curve": [0.528, 80.26, 0.562, 57.38, 0.528, 12.19, 0.562, 8.16]}, {"time": 0.6, "x": 23.05, "y": 2.12, "curve": [0.663, 4.48, 0.737, -25.35, 0.663, 3.46, 0.737, 5.62]}, {"time": 0.8, "x": -45.21, "y": 7.07}]}, "dui sau4": {"rotate": [{"value": 22.39, "curve": [0.028, 22.39, 0.062, 16.5]}, {"time": 0.1, "value": 7.67, "curve": [0.2, 9.57, 0.327, 13.38]}, {"time": 0.4, "value": 13.38, "curve": [0.433, 13.38, 0.5, -14.51]}, {"time": 0.5333, "value": -14.51, "curve": [0.567, -14.51, 0.633, -0.43]}, {"time": 0.6667, "value": -0.43, "curve": [0.7, -0.43, 0.767, 22.39]}, {"time": 0.8, "value": 22.39}], "translate": [{"x": 127.66, "y": 10.43, "curve": [0.1, 127.66, 0.3, -15.63, 0.1, 10.43, 0.3, 0]}, {"time": 0.4, "x": -15.63, "curve": [0.435, -15.63, 0.482, 1.8, 0.435, 0, 0.482, 5.45]}, {"time": 0.5333, "x": 25.03, "y": 12.72, "curve": [0.627, 66.09, 0.735, 127.66, 0.627, 11.81, 0.735, 10.43]}, {"time": 0.8, "x": 127.66, "y": 10.43}]}, "dui truoc3": {"rotate": [{"value": -38.42, "curve": [0.037, -9.99, 0.076, 24.75]}, {"time": 0.1, "value": 24.75, "curve": [0.2, 24.75, 0.4, 0]}, {"time": 0.5, "curve": [0.525, 0, 0.575, -70.17]}, {"time": 0.6, "value": -70.17, "curve": [0.633, -70.17, 0.7, -75.18]}, {"time": 0.7333, "value": -75.18, "curve": [0.751, -75.18, 0.775, -58.45]}, {"time": 0.8, "value": -38.42}], "translate": [{"x": 64.31, "y": 17.7, "curve": [0.038, 79.91, 0.073, 90.61, 0.038, 11.3, 0.073, 6.9]}, {"time": 0.1, "x": 90.61, "y": 6.9, "curve": [0.2, 90.61, 0.4, -67.59, 0.2, 6.9, 0.4, 7.76]}, {"time": 0.5, "x": -67.59, "y": 7.76, "curve": [0.528, -67.59, 0.562, -44.62, 0.528, 7.76, 0.562, 47.39]}, {"time": 0.6, "x": -27.71, "y": 54.4, "curve": [0.642, -18.4, 0.688, 29.66, 0.642, 57.69, 0.688, 49.86]}, {"time": 0.7333, "x": 41.72, "y": 54.12, "curve": [0.756, 52.62, 0.779, 55.54, 0.756, 49.65, 0.779, 21.3]}, {"time": 0.8, "x": 64.31, "y": 17.7}]}, "dui sau1": {"rotate": [{"value": 21.56, "curve": [0.042, 21.56, 0.125, 15.66]}, {"time": 0.1667, "value": 15.66, "curve": [0.252, 8.43, 0.343, -0.24]}, {"time": 0.4, "value": -0.24, "curve": [0.45, -0.24, 0.525, 4.55]}, {"time": 0.6, "value": 9.33, "curve": [0.675, 15.45, 0.75, 21.56]}, {"time": 0.8, "value": 21.56}], "translate": [{"curve": [0.1, 0, 0.3, 5.76, 0.1, 0, 0.3, 10.72]}, {"time": 0.4, "x": 5.76, "y": 10.72, "curve": [0.5, 5.76, 0.7, 0, 0.5, 10.72, 0.7, 0]}, {"time": 0.8}]}, "body3": {"rotate": [{"value": 0.45, "curve": [0.025, 1.02, 0.049, 1.45]}, {"time": 0.0667, "value": 1.45, "curve": [0.117, 1.45, 0.217, -2.06]}, {"time": 0.2667, "value": -2.06, "curve": [0.317, -2.06, 0.417, 1.45]}, {"time": 0.4667, "value": 1.45, "curve": [0.517, 1.45, 0.617, -2.06]}, {"time": 0.6667, "value": -2.06, "curve": [0.699, -2.06, 0.753, -0.55]}, {"time": 0.8, "value": 0.45}]}, "body4": {"rotate": [{"value": -1.06, "curve": [0.022, -0.6, 0.045, -0.01]}, {"time": 0.0667, "value": 0.45, "curve": [0.092, 1.02, 0.116, 1.45]}, {"time": 0.1333, "value": 1.45, "curve": [0.183, 1.45, 0.283, -2.06]}, {"time": 0.3333, "value": -2.06, "curve": [0.383, -2.06, 0.483, 1.45]}, {"time": 0.5333, "value": 1.45, "curve": [0.583, 1.45, 0.683, -2.06]}, {"time": 0.7333, "value": -2.06, "curve": [0.751, -2.06, 0.775, -1.63]}, {"time": 0.8, "value": -1.06}]}, "body5": {"rotate": [{"value": -2.06, "curve": [0.018, -2.06, 0.041, -1.63]}, {"time": 0.0667, "value": -1.06, "curve": [0.088, -0.6, 0.112, -0.01]}, {"time": 0.1333, "value": 0.45, "curve": [0.159, 1.02, 0.182, 1.45]}, {"time": 0.2, "value": 1.45, "curve": [0.25, 1.45, 0.35, -2.06]}, {"time": 0.4, "value": -2.06, "curve": [0.45, -2.06, 0.55, 1.45]}, {"time": 0.6, "value": 1.45, "curve": [0.65, 1.45, 0.75, -2.06]}, {"time": 0.8, "value": -2.06}]}, "dui truoc1": {"rotate": [{"value": 20.01}], "translate": [{"x": 17.47, "y": -4, "curve": [0.011, 17.55, 0.022, 19.33, 0.011, -3.91, 0.022, -11.67]}, {"time": 0.0333, "x": 18.07, "y": -14.39, "curve": [0.058, 15.44, 0.082, 8.57, 0.058, -20.09, 0.082, -23.25]}, {"time": 0.1, "x": 8.57, "y": -23.25, "curve": [0.149, 8.57, 0.226, 1.34, 0.149, -23.25, 0.226, 7.14]}, {"time": 0.3, "x": 0.08, "y": 14.89, "curve": [0.363, 0.04, 0.424, 0, 0.363, 7.12, 0.424, 0]}, {"time": 0.4667, "curve": [0.475, 0, 0.492, -0.02, 0.475, 0, 0.492, -4.29]}, {"time": 0.5, "x": -0.02, "y": -4.29, "curve": [0.573, -0.02, 0.699, 16.68, 0.573, -4.29, 0.699, -4.83]}, {"time": 0.8, "x": 17.47, "y": -4}]}, "dui sau5": {"rotate": [{"value": -7.48, "curve": [0.1, -7.48, 0.3, 6.59]}, {"time": 0.4, "value": 6.59, "curve": [0.5, 6.59, 0.7, -7.48]}, {"time": 0.8, "value": -7.48}], "translate": [{"x": 1.41, "y": 1.45, "curve": [0.1, 1.41, 0.3, 1.43, 0.1, 1.45, 0.3, 0.13]}, {"time": 0.4, "x": 1.43, "y": 0.13, "curve": [0.5, 1.43, 0.7, 1.41, 0.5, 0.13, 0.7, 1.45]}, {"time": 0.8, "x": 1.41, "y": 1.45}]}, "body2": {"rotate": [{"value": -2.91, "curve": [0.05, -2.91, 0.15, 6.03]}, {"time": 0.2, "value": 6.03, "curve": [0.25, 6.03, 0.35, -2.91]}, {"time": 0.4, "value": -2.91, "curve": [0.45, -2.91, 0.55, 6.03]}, {"time": 0.6, "value": 6.03, "curve": [0.65, 6.03, 0.75, -2.91]}, {"time": 0.8, "value": -2.91}]}, "dau": {"rotate": [{"value": -2.66, "curve": [0.05, -2.66, 0.15, 6.86]}, {"time": 0.2, "value": 6.86, "curve": [0.25, 6.86, 0.35, -2.66]}, {"time": 0.4, "value": -2.66, "curve": [0.45, -2.66, 0.55, 6.86]}, {"time": 0.6, "value": 6.86, "curve": [0.65, 6.86, 0.75, -2.66]}, {"time": 0.8, "value": -2.66}], "translate": [{"x": 3.8, "y": -13.2, "curve": [0.05, 3.8, 0.15, 4.84, 0.05, -13.2, 0.15, 9.02]}, {"time": 0.2, "x": 4.84, "y": 9.02, "curve": [0.25, 4.84, 0.35, 3.8, 0.25, 9.02, 0.35, -13.2]}, {"time": 0.4, "x": 3.8, "y": -13.2, "curve": [0.45, 3.8, 0.55, 4.84, 0.45, -13.2, 0.55, 9.02]}, {"time": 0.6, "x": 4.84, "y": 9.02, "curve": [0.65, 4.84, 0.75, 3.8, 0.65, 9.02, 0.75, -13.2]}, {"time": 0.8, "x": 3.8, "y": -13.2}]}, "tai5": {"rotate": [{"value": 17.76, "curve": [0.05, 17.76, 0.15, -13.5]}, {"time": 0.2, "value": -13.5, "curve": [0.25, -13.5, 0.35, 17.76]}, {"time": 0.4, "value": 17.76, "curve": [0.45, 17.76, 0.55, -13.5]}, {"time": 0.6, "value": -13.5, "curve": [0.65, -13.5, 0.75, 17.76]}, {"time": 0.8, "value": 17.76}]}, "tai6": {"rotate": [{"value": 8.89, "curve": [0.025, 13.95, 0.049, 17.76]}, {"time": 0.0667, "value": 17.76, "curve": [0.117, 17.76, 0.217, -13.5]}, {"time": 0.2667, "value": -13.5, "curve": [0.317, -13.5, 0.417, 17.76]}, {"time": 0.4667, "value": 17.76, "curve": [0.517, 17.76, 0.617, -13.5]}, {"time": 0.6667, "value": -13.5, "curve": [0.699, -13.5, 0.753, -0.07]}, {"time": 0.8, "value": 8.89}]}, "tai7": {"rotate": [{"value": -12.24, "curve": [0.022, -6.67, 0.045, 0.29]}, {"time": 0.0667, "value": 5.86, "curve": [0.092, 12.66, 0.116, 17.76]}, {"time": 0.1333, "value": 17.76, "curve": [0.183, 17.76, 0.283, -24.17]}, {"time": 0.3333, "value": -24.17, "curve": [0.383, -24.17, 0.483, 17.76]}, {"time": 0.5333, "value": 17.76, "curve": [0.583, 17.76, 0.683, -24.17]}, {"time": 0.7333, "value": -24.17, "curve": [0.751, -24.17, 0.775, -19.06]}, {"time": 0.8, "value": -12.24}]}, "tai8": {"rotate": [{"value": -24.17, "curve": [0.018, -24.17, 0.041, -19.06]}, {"time": 0.0667, "value": -12.24, "curve": [0.088, -6.67, 0.112, 0.29]}, {"time": 0.1333, "value": 5.86, "curve": [0.159, 12.66, 0.182, 17.76]}, {"time": 0.2, "value": 17.76, "curve": [0.25, 17.76, 0.35, -24.17]}, {"time": 0.4, "value": -24.17, "curve": [0.45, -24.17, 0.55, 17.76]}, {"time": 0.6, "value": 17.76, "curve": [0.65, 17.76, 0.75, -24.17]}, {"time": 0.8, "value": -24.17}]}, "tai1": {"rotate": [{"value": -13.91, "curve": [0.05, -13.91, 0.15, 6.68]}, {"time": 0.2, "value": 6.68, "curve": [0.25, 6.68, 0.35, -13.91]}, {"time": 0.4, "value": -13.91, "curve": [0.45, -13.91, 0.55, 6.68]}, {"time": 0.6, "value": 6.68, "curve": [0.65, 6.68, 0.75, -13.91]}, {"time": 0.8, "value": -13.91}]}, "tai2": {"rotate": [{"value": -11.8, "curve": [0.025, -18.03, 0.049, -22.7]}, {"time": 0.0667, "value": -22.7, "curve": [0.117, -22.7, 0.217, 15.72]}, {"time": 0.2667, "value": 15.72, "curve": [0.317, 15.72, 0.417, -22.7]}, {"time": 0.4667, "value": -22.7, "curve": [0.517, -22.7, 0.617, 15.72]}, {"time": 0.6667, "value": 15.72, "curve": [0.699, 15.72, 0.753, -0.79]}, {"time": 0.8, "value": -11.8}]}, "tai3": {"rotate": [{"value": 4.78, "curve": [0.022, -0.32, 0.045, -6.69]}, {"time": 0.0667, "value": -11.8, "curve": [0.092, -18.03, 0.116, -22.7]}, {"time": 0.1333, "value": -22.7, "curve": [0.183, -22.7, 0.283, 15.72]}, {"time": 0.3333, "value": 15.72, "curve": [0.383, 15.72, 0.483, -22.7]}, {"time": 0.5333, "value": -22.7, "curve": [0.583, -22.7, 0.683, 15.72]}, {"time": 0.7333, "value": 15.72, "curve": [0.751, 15.72, 0.775, 11.03]}, {"time": 0.8, "value": 4.78}]}, "tai4": {"rotate": [{"value": 15.72, "curve": [0.018, 15.72, 0.041, 11.03]}, {"time": 0.0667, "value": 4.78, "curve": [0.088, -0.32, 0.112, -6.69]}, {"time": 0.1333, "value": -11.8, "curve": [0.159, -18.03, 0.182, -22.7]}, {"time": 0.2, "value": -22.7, "curve": [0.25, -22.7, 0.35, 15.72]}, {"time": 0.4, "value": 15.72, "curve": [0.45, 15.72, 0.55, -22.7]}, {"time": 0.6, "value": -22.7, "curve": [0.65, -22.7, 0.75, 15.72]}, {"time": 0.8, "value": 15.72}]}, "duoi3": {"rotate": [{"value": 4.49, "curve": [0.063, 6.78, 0.124, 8.74]}, {"time": 0.1667, "value": 8.74, "curve": [0.232, 8.74, 0.34, 4.01]}, {"time": 0.4333, "value": 0.87, "curve": [0.457, 0.08, 0.479, -0.61]}, {"time": 0.5, "value": -1.1, "curve": [0.525, -1.76, 0.547, -2.15]}, {"time": 0.5667, "value": -2.15, "curve": [0.624, -2.15, 0.715, 1.47]}, {"time": 0.8, "value": 4.49}]}, "duoi4": {"rotate": [{"value": -6.81, "curve": [0.085, 0.26, 0.176, 8.74]}, {"time": 0.2333, "value": 8.74, "curve": [0.283, 8.74, 0.358, 2.29]}, {"time": 0.4333, "value": -4.15, "curve": [0.456, -6.06, 0.478, -7.98]}, {"time": 0.5, "value": -9.68, "curve": [0.523, -11.52, 0.546, -13.13]}, {"time": 0.5667, "value": -14.28, "curve": [0.591, -15.82, 0.614, -16.74]}, {"time": 0.6333, "value": -16.74, "curve": [0.676, -16.74, 0.737, -12.15]}, {"time": 0.8, "value": -6.81}]}, "duoi5": {"rotate": [{"value": -12.04, "curve": [0.1, -5.11, 0.227, 8.74]}, {"time": 0.3, "value": 8.74, "curve": [0.335, 8.74, 0.382, 5.62]}, {"time": 0.4333, "value": 1.46, "curve": [0.455, -0.27, 0.478, -2.21]}, {"time": 0.5, "value": -4.15, "curve": [0.523, -6.06, 0.545, -7.98]}, {"time": 0.5667, "value": -9.68, "curve": [0.59, -11.52, 0.613, -13.13]}, {"time": 0.6333, "value": -14.28, "curve": [0.658, -15.82, 0.681, -16.74]}, {"time": 0.7, "value": -16.74, "curve": [0.728, -16.74, 0.762, -14.86]}, {"time": 0.8, "value": -12.04}]}, "duoi6": {"rotate": [{"value": -16, "curve": [0.104, -12.46, 0.277, 8.74]}, {"time": 0.3667, "value": 8.74, "curve": [0.386, 8.74, 0.409, 7.82]}, {"time": 0.4333, "value": 6.3, "curve": [0.454, 5.09, 0.477, 3.4]}, {"time": 0.5, "value": 1.46, "curve": [0.522, -0.27, 0.544, -2.21]}, {"time": 0.5667, "value": -4.15, "curve": [0.589, -6.06, 0.612, -7.98]}, {"time": 0.6333, "value": -9.68, "curve": [0.657, -11.52, 0.679, -13.13]}, {"time": 0.7, "value": -14.28, "curve": [0.725, -15.82, 0.747, -16.74]}, {"time": 0.7667, "value": -16.74, "curve": [0.777, -16.74, 0.788, -16.48]}, {"time": 0.8, "value": -16}]}, "duoi7": {"rotate": [{"value": -16, "curve": [0.012, -16.48, 0.023, -16.74]}, {"time": 0.0333, "value": -16.74, "curve": [0.133, -16.74, 0.333, 8.74]}, {"time": 0.4333, "value": 8.74, "curve": [0.453, 8.74, 0.475, 7.82]}, {"time": 0.5, "value": 6.3, "curve": [0.521, 5.09, 0.543, 3.4]}, {"time": 0.5667, "value": 1.46, "curve": [0.588, -0.27, 0.611, -2.21]}, {"time": 0.6333, "value": -4.15, "curve": [0.656, -6.06, 0.678, -7.98]}, {"time": 0.7, "value": -9.68, "curve": [0.723, -11.52, 0.746, -13.13]}, {"time": 0.7667, "value": -14.28, "curve": [0.778, -15.01, 0.789, -15.6]}, {"time": 0.8, "value": -16}]}, "duoi8": {"rotate": [{"value": -12.17, "curve": [0.011, -12.97, 0.023, -13.68]}, {"time": 0.0333, "value": -14.28, "curve": [0.058, -15.82, 0.081, -16.74]}, {"time": 0.1, "value": -16.74, "curve": [0.181, -16.74, 0.33, 0.53]}, {"time": 0.4333, "value": 6.28, "curve": [0.458, 7.82, 0.481, 8.74]}, {"time": 0.5, "value": 8.74, "curve": [0.519, 8.74, 0.542, 7.82]}, {"time": 0.5667, "value": 6.3, "curve": [0.587, 5.09, 0.61, 3.4]}, {"time": 0.6333, "value": 1.46, "curve": [0.655, -0.27, 0.678, -2.21]}, {"time": 0.7, "value": -4.15, "curve": [0.723, -6.06, 0.745, -7.98]}, {"time": 0.7667, "value": -9.68, "curve": [0.778, -10.57, 0.789, -11.41]}, {"time": 0.8, "value": -12.17}]}, "long duoi": {"rotate": [{"value": -12.17, "curve": [0.011, -12.97, 0.023, -13.68]}, {"time": 0.0333, "value": -14.28, "curve": [0.058, -15.82, 0.081, -16.74]}, {"time": 0.1, "value": -16.74, "curve": [0.181, -16.74, 0.33, 0.53]}, {"time": 0.4333, "value": 6.28, "curve": [0.458, 7.82, 0.481, 8.74]}, {"time": 0.5, "value": 8.74, "curve": [0.519, 8.74, 0.542, 7.82]}, {"time": 0.5667, "value": 6.3, "curve": [0.587, 5.09, 0.61, 3.4]}, {"time": 0.6333, "value": 1.46, "curve": [0.655, -0.27, 0.678, -2.21]}, {"time": 0.7, "value": -4.15, "curve": [0.723, -6.06, 0.745, -7.98]}, {"time": 0.7667, "value": -9.68, "curve": [0.778, -10.57, 0.789, -11.41]}, {"time": 0.8, "value": -12.17}]}, "long duoi2": {"rotate": [{"value": -6.97, "curve": [0.011, -7.91, 0.022, -8.82]}, {"time": 0.0333, "value": -9.68, "curve": [0.057, -11.52, 0.079, -13.13]}, {"time": 0.1, "value": -14.28, "curve": [0.125, -15.82, 0.147, -16.74]}, {"time": 0.1667, "value": -16.74, "curve": [0.232, -16.74, 0.34, -5.69]}, {"time": 0.4333, "value": 1.68, "curve": [0.457, 3.52, 0.479, 5.13]}, {"time": 0.5, "value": 6.28, "curve": [0.525, 7.82, 0.547, 8.74]}, {"time": 0.5667, "value": 8.74, "curve": [0.586, 8.74, 0.609, 7.82]}, {"time": 0.6333, "value": 6.3, "curve": [0.654, 5.09, 0.677, 3.4]}, {"time": 0.7, "value": 1.46, "curve": [0.722, -0.27, 0.744, -2.21]}, {"time": 0.7667, "value": -4.15, "curve": [0.778, -5.1, 0.789, -6.05]}, {"time": 0.8, "value": -6.97}]}, "duoi2": {"rotate": [{"value": 0.73, "curve": [0.038, 1.12, 0.072, 1.38]}, {"time": 0.1, "value": 1.38, "curve": [0.181, 1.38, 0.33, -1.02]}, {"time": 0.4333, "value": -1.81, "curve": [0.458, -2.03, 0.481, -2.15]}, {"time": 0.5, "value": -2.15, "curve": [0.573, -2.15, 0.7, -0.23]}, {"time": 0.8, "value": 0.73}]}, "dui truoc4": {"rotate": [{"value": -15.94, "curve": "stepped"}, {"time": 0.1, "value": -15.94, "curve": [0.17, -15.94, 0.251, 27.89]}, {"time": 0.3333, "value": 27.89, "curve": [0.492, 27.89, 0.662, -15.94]}, {"time": 0.8, "value": -15.94}], "translate": [{"time": 0.3333, "curve": [0.399, 0, 0.467, -15.08, 0.399, 0, 0.467, -13.51]}, {"time": 0.5333, "x": -15.08, "y": -13.51, "curve": [0.579, -15.08, 0.624, -15.98, 0.579, -13.51, 0.624, 6.65]}, {"time": 0.6667, "x": -12.34, "y": 9.91, "curve": [0.714, -8.45, 0.758, 0, 0.714, 13.4, 0.758, 0]}, {"time": 0.8}]}, "con nguoi1": {"translate": [{"x": 5.82, "y": -3.62}]}}}}}