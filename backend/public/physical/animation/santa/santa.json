{"skeleton": {"hash": "y75HoQpQlNs", "spine": "4.2.37", "x": -263.77, "y": 2.04, "width": 535.02, "height": 707.18, "images": "../assets/", "audio": "./audio"}, "bones": [{"name": "root", "rotation": -0.09}, {"name": "bone", "parent": "root", "length": 160.26, "rotation": -92.71, "x": 8.83, "y": 244.47, "color": "9bff00ff"}, {"name": "bone2", "parent": "bone", "length": 181.33, "rotation": -178.01, "x": -2.24, "y": -0.87, "color": "8aff00ff"}, {"name": "left_leg", "parent": "bone", "length": 46.84, "rotation": -8.38, "x": 138.84, "y": -67.24, "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 86.51, "rotation": -138.06, "x": 157.08, "y": -114.94, "color": "00fdfaff"}, {"name": "bone4", "parent": "bone3", "length": 72.93, "rotation": -0.36, "x": 82.14, "y": -0.29, "color": "00fdfaff"}, {"name": "bone5", "parent": "bone4", "length": 50.44, "rotation": -3.56, "x": 74.8, "y": -1.62, "color": "00fdfaff"}, {"name": "bone6", "parent": "bone2", "length": 58.48, "rotation": 130.99, "x": 154.67, "y": 126.11, "color": "00fdfaff"}, {"name": "bone7", "parent": "bone6", "length": 68.54, "rotation": 12.44, "x": 61.1, "y": 0.22, "color": "00fdfaff"}, {"name": "bone8", "parent": "bone7", "length": 76.66, "rotation": 2.82, "x": 70.28, "y": -0.24, "color": "00fdfaff"}, {"name": "head", "parent": "root", "x": -0.73, "y": 400.55}, {"name": "head2", "parent": "bone2", "length": 124.61, "rotation": -2.65, "x": 156.12, "y": 10.77, "color": "9100ffff"}, {"name": "head3", "parent": "head2", "length": 140.46, "rotation": -13.02, "x": 127.65, "y": 0.18, "color": "9100ffff"}, {"name": "bone9", "parent": "head3", "length": 112.85, "rotation": -112.03, "x": 136.95, "y": -1.03, "color": "9100ffff"}, {"name": "bone10", "parent": "bone9", "length": 75.15, "rotation": -9.87, "x": 112.85, "color": "9100ffff"}, {"name": "bone11", "parent": "bone10", "length": 72.99, "rotation": -11.08, "x": 75.15, "color": "9100ffff"}, {"name": "right_leg", "parent": "bone", "length": 56.21, "rotation": 14.74, "x": 144.27, "y": 65.64, "color": "ff0000ff"}, {"name": "right_leg2", "parent": "right_leg", "length": 39.69, "rotation": 27.44, "x": 57.09, "y": -0.19, "color": "ff0000ff"}, {"name": "left_leg2", "parent": "left_leg", "length": 58.58, "rotation": -11.53, "x": 46.84, "color": "ff0000ff"}], "slots": [{"name": "left_leg", "bone": "left_leg", "attachment": "left_leg_white"}, {"name": "right_leg", "bone": "right_leg", "attachment": "right_leg_white"}, {"name": "left_arm", "bone": "root", "attachment": "left_arm_white"}, {"name": "right_arm", "bone": "root", "attachment": "right_arm_white"}, {"name": "body", "bone": "root", "attachment": "body_white"}, {"name": "head", "bone": "head", "attachment": "head_white"}], "skins": [{"name": "default", "attachments": {"body": {"body_white": {"type": "mesh", "uvs": [0.72151, 0.02706, 0.81258, 0, 0.85984, 0.07442, 0.90144, 0.17434, 0.93458, 0.29679, 0.95771, 0.44574, 0.98056, 0.59162, 0.98477, 0.77592, 1, 0.8784, 0.98311, 0.94422, 0.93341, 0.97159, 0.83373, 0.961, 0.73668, 1, 0.63171, 0.98233, 0.50966, 0.98057, 0.38762, 0.97881, 0.27204, 1, 0.16986, 0.97124, 0.07189, 0.97195, 0.01787, 0.9412, 0, 0.8697, 0.01066, 0.80921, 0.01481, 0.58837, 0.04434, 0.46214, 0.0628, 0.33876, 0.08126, 0.21537, 0.13654, 0.07923, 0.18607, 0, 0.29386, 0.03175, 0.40165, 0.06351, 0.47998, 0.15739, 0.51849, 0.1583, 0.557, 0.1592, 0.63044, 0.05411, 0.1932, 0.79442, 0.29841, 0.80952, 0.40609, 0.81457, 0.51154, 0.80571, 0.61655, 0.81528, 0.71641, 0.81951, 0.81556, 0.80977, 0.22015, 0.59021, 0.3284, 0.59279, 0.43111, 0.59207, 0.51384, 0.59143, 0.59644, 0.59379, 0.69091, 0.59244, 0.78955, 0.5933, 0.23838, 0.45206, 0.34801, 0.4511, 0.44661, 0.45415, 0.51534, 0.4515, 0.58321, 0.448, 0.67394, 0.44141, 0.77186, 0.44606, 0.28158, 0.12478, 0.38627, 0.17465, 0.47218, 0.22677, 0.51762, 0.2392, 0.56338, 0.22946, 0.64702, 0.20174, 0.73721, 0.1577, 0.26422, 0.2563, 0.37286, 0.27152, 0.46535, 0.28755, 0.51705, 0.29303, 0.56869, 0.28803, 0.6552, 0.27452, 0.7466, 0.23584, 0.2513, 0.35418, 0.36044, 0.36131, 0.45598, 0.37085, 0.5162, 0.37227, 0.57595, 0.36802, 0.66457, 0.35796, 0.75923, 0.34095], "triangles": [34, 22, 41, 41, 48, 42, 42, 49, 43, 42, 48, 49, 43, 50, 44, 43, 49, 50, 44, 50, 51, 22, 23, 41, 41, 23, 48, 23, 24, 48, 49, 70, 50, 50, 71, 51, 50, 70, 71, 48, 69, 49, 48, 24, 69, 51, 72, 52, 72, 73, 52, 51, 71, 72, 49, 69, 70, 52, 73, 74, 72, 65, 73, 65, 66, 73, 65, 72, 64, 72, 71, 64, 70, 63, 71, 71, 63, 64, 73, 66, 74, 69, 62, 70, 70, 62, 63, 66, 67, 74, 74, 67, 75, 62, 69, 25, 67, 68, 75, 75, 68, 4, 69, 24, 25, 68, 3, 4, 65, 58, 66, 65, 64, 58, 58, 59, 66, 66, 59, 67, 64, 57, 58, 64, 63, 57, 59, 60, 67, 67, 60, 68, 63, 56, 57, 63, 62, 56, 25, 26, 62, 62, 55, 56, 62, 26, 55, 58, 57, 31, 59, 31, 32, 59, 58, 31, 31, 57, 30, 60, 61, 68, 68, 61, 3, 59, 32, 60, 57, 56, 30, 61, 60, 33, 56, 29, 30, 56, 55, 29, 61, 2, 3, 60, 32, 33, 33, 0, 61, 2, 0, 1, 2, 61, 0, 26, 27, 55, 55, 28, 29, 55, 27, 28, 16, 17, 35, 16, 35, 15, 35, 17, 34, 13, 39, 12, 11, 39, 40, 11, 12, 39, 14, 38, 13, 13, 38, 39, 15, 36, 14, 14, 37, 38, 14, 36, 37, 15, 35, 36, 17, 18, 34, 9, 10, 40, 34, 18, 19, 10, 11, 40, 9, 40, 8, 19, 20, 34, 40, 7, 8, 20, 21, 34, 38, 46, 39, 39, 47, 40, 39, 46, 47, 37, 45, 38, 38, 45, 46, 35, 42, 36, 36, 43, 37, 36, 42, 43, 7, 47, 6, 7, 40, 47, 42, 35, 41, 21, 22, 34, 37, 44, 45, 37, 43, 44, 35, 34, 41, 45, 44, 52, 44, 51, 52, 45, 53, 46, 45, 52, 53, 46, 54, 47, 47, 5, 6, 47, 54, 5, 46, 53, 54, 52, 74, 53, 53, 75, 54, 5, 54, 4, 54, 75, 4, 53, 74, 75], "vertices": [2, 1, -169.6, 73.37, 0.13333, 2, 164.68, -80.02, 0.86667, 2, 1, -180.72, 108.59, 0.15952, 2, 174.57, -115.6, 0.84048, 2, 1, -155.66, 128.32, 0.21475, 2, 148.83, -134.45, 0.78525, 2, 1, -121.6, 146.26, 0.33219, 2, 114.17, -151.19, 0.66781, 2, 1, -79.52, 161.26, 0.5296, 2, 71.6, -164.72, 0.4704, 2, 1, -28.03, 172.78, 0.74463, 2, 19.73, -174.44, 0.25537, 2, 1, 22.4, 184.14, 0.91506, 2, -31.06, -184.03, 0.08494, 2, 1, 86.57, 188.83, 0.98306, 2, -95.36, -186.49, 0.01694, 2, 1, 122.02, 196.48, 0.9992, 2, -131.05, -192.91, 0.0008, 2, 1, 145.28, 190.96, 0.99997, 2, -154.1, -186.57, 3e-05, 2, 1, 155.74, 171.95, 0.99997, 2, -163.89, -167.21, 3e-05, 2, 1, 153.9, 132.75, 0.99997, 2, -160.69, -128.1, 3e-05, 1, 1, 169.29, 95.39, 1, 1, 1, 165.08, 53.99, 1, 2, 1, 166.73, 6.18, 0.98885, 2, -169.11, -1.16, 0.01115, 2, 1, 168.38, -41.64, 0.94031, 2, -169.1, 46.69, 0.05969, 2, 1, 177.92, -86.55, 0.85014, 2, -177.06, 91.9, 0.14986, 2, 1, 169.79, -127.03, 0.75844, 2, -167.53, 132.08, 0.24156, 2, 1, 171.85, -165.38, 0.71567, 2, -168.26, 170.48, 0.28433, 2, 1, 162.14, -187.04, 0.68968, 2, -157.79, 191.78, 0.31032, 2, 1, 137.54, -195.22, 0.66855, 2, -132.93, 199.1, 0.33145, 2, 1, 116.25, -192.05, 0.58488, 2, -111.77, 195.19, 0.41512, 2, 1, 39.19, -194.07, 0.38528, 2, -34.68, 194.53, 0.61472, 2, 1, -5.36, -184.59, 0.18385, 2, 9.51, 183.51, 0.81615, 2, 1, -48.72, -179.4, 0.07461, 2, 52.66, 176.81, 0.92539, 2, 1, -92.07, -174.21, 0.02006, 2, 95.81, 170.12, 0.97994, 2, 1, -140.56, -154.82, 0.00294, 2, 143.6, 149.05, 0.99706, 1, 2, 171.49, 129.98, 1, 1, 2, 160.94, 87.59, 1, 1, 2, 150.38, 45.2, 1, 2, 1, -119.68, -19.05, 0.0002, 2, 118.01, 14.08, 0.9998, 2, 1, -120.08, -3.95, 0.00574, 2, 117.88, -1.02, 0.99426, 2, 1, -120.48, 11.14, 0.03143, 2, 117.75, -16.11, 0.96857, 2, 1, -158.48, 38.16, 0.08315, 2, 154.79, -44.44, 0.91685, 2, 1, 107.71, -120.82, 0.66635, 2, -105.71, 123.7, 0.33365, 2, 1, 111.02, -79.37, 0.74534, 2, -110.46, 82.4, 0.25466, 2, 1, 110.79, -37.12, 0.8687, 2, -111.69, 40.17, 0.1313, 2, 1, 105.74, 4.02, 0.98815, 2, -108.08, -1.13, 0.01185, 1, 1, 107.13, 45.3, 1, 2, 1, 106.75, 84.47, 0.99595, 2, -111.89, -81.49, 0.00405, 2, 1, 101.51, 123.13, 0.99332, 2, -108, -120.31, 0.00668, 2, 1, 36.02, -113.64, 0.39162, 2, -34.31, 114.03, 0.60838, 2, 1, 34.91, -71.21, 0.46154, 2, -34.68, 71.59, 0.53846, 2, 1, 32.76, -31, 0.60325, 2, -33.92, 31.34, 0.39675, 2, 1, 31, 1.38, 0.72946, 2, -33.29, -1.09, 0.27054, 2, 1, 30.29, 33.77, 0.90637, 2, -33.71, -33.48, 0.09363, 2, 1, 28.07, 70.73, 0.92389, 2, -32.78, -70.5, 0.07611, 2, 1, 26.53, 109.37, 0.92084, 2, -32.59, -109.17, 0.07916, 2, 1, -12.48, -108.78, 0.15472, 2, 13.99, 107.49, 0.84528, 2, 1, -14.85, -65.87, 0.1256, 2, 14.86, 64.53, 0.8744, 2, 1, -15.61, -27.21, 0.1439, 2, 14.28, 25.86, 0.8561, 2, 1, -17.81, -0.34, 0.31359, 2, 15.54, -1.07, 0.68641, 2, 1, -20.29, 26.18, 0.48405, 2, 17.1, -27.65, 0.51595, 2, 1, -24.27, 61.59, 0.6723, 2, 19.85, -63.19, 0.3277, 2, 1, -24.47, 100.01, 0.72972, 2, 18.71, -101.59, 0.27028, 2, 1, -127.37, -97.27, 0.00071, 2, 128.41, 91.99, 0.99929, 1, 2, 111.52, 50.74, 1, 2, 1, -95.35, -20.95, 0.00015, 2, 93.76, 16.84, 0.99985, 2, 1, -91.86, -2.96, 0.00597, 2, 89.64, -1.03, 0.99403, 2, 1, -96.11, 14.8, 0.04361, 2, 93.27, -18.92, 0.95639, 2, 1, -107.32, 47.09, 0.11971, 2, 103.35, -51.59, 0.88029, 2, 1, -124.35, 81.68, 0.17886, 2, 119.16, -86.74, 0.82114, 2, 1, -81.2, -101.9, 0.00946, 2, 82.43, 98.22, 0.99054, 2, 1, -77.91, -59.1, 0.00161, 2, 77.65, 55.57, 0.99839, 2, 1, -74.04, -22.63, 0.00022, 2, 72.51, 19.25, 0.99978, 2, 1, -73.09, -2.29, 0.01205, 2, 70.86, -1.04, 0.98795, 2, 1, -75.79, 17.85, 0.09152, 2, 72.85, -21.26, 0.90848, 2, 1, -82.1, 51.5, 0.21024, 2, 77.99, -55.11, 0.78976, 2, 1, -97.28, 86.65, 0.29516, 2, 91.94, -90.77, 0.70484, 2, 1, -46.84, -105.34, 0.04527, 2, 48.21, 102.86, 0.95473, 2, 1, -46.38, -62.49, 0.02058, 2, 46.26, 60.05, 0.97942, 2, 1, -44.82, -24.92, 0.00261, 2, 43.4, 22.56, 0.99739, 2, 1, -45.45, -1.32, 0.03645, 2, 43.2, -1.05, 0.96355, 2, 1, -48.04, 22.01, 0.23639, 2, 44.98, -24.46, 0.76361, 2, 1, -53.19, 56.54, 0.39113, 2, 48.92, -59.15, 0.60887, 2, 1, -60.88, 93.33, 0.48726, 2, 55.32, -96.18, 0.51274], "hull": 34, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 22, 24, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 50, 52, 64, 66, 52, 54, 58, 60, 10, 12, 12, 14, 40, 42, 14, 16, 32, 34, 30, 32, 20, 22, 60, 62, 62, 64, 26, 28, 28, 30, 24, 26, 2, 0, 0, 66, 54, 56, 56, 58, 34, 68, 42, 68, 32, 70, 68, 70, 30, 72, 70, 72, 28, 74, 72, 74, 26, 76, 74, 76, 24, 78, 76, 78, 80, 22, 78, 80, 80, 14, 40, 68, 38, 68, 36, 68, 20, 80, 18, 80, 16, 80, 82, 68, 44, 82, 84, 70, 82, 84, 86, 72, 84, 86, 88, 74, 86, 88, 90, 76, 88, 90, 92, 78, 90, 92, 94, 80, 92, 94, 94, 12, 96, 82, 46, 96, 98, 84, 96, 98, 100, 86, 98, 100, 102, 88, 100, 102, 104, 90, 102, 104, 106, 92, 104, 106, 108, 94, 106, 108, 108, 10, 56, 110, 52, 110, 58, 112, 110, 112, 60, 114, 112, 114, 62, 116, 114, 116, 64, 118, 116, 118, 66, 120, 118, 120, 0, 122, 120, 122, 122, 4, 124, 110, 50, 124, 126, 112, 124, 126, 128, 114, 126, 128, 130, 116, 128, 130, 132, 118, 130, 132, 134, 120, 132, 134, 136, 122, 134, 136, 136, 6, 46, 48, 48, 50, 96, 138, 138, 124, 48, 138, 98, 140, 140, 126, 138, 140, 100, 142, 142, 128, 140, 142, 102, 144, 144, 130, 142, 144, 104, 146, 146, 132, 144, 146, 106, 148, 148, 134, 146, 148, 108, 150, 150, 136, 148, 150, 150, 8], "width": 392, "height": 349}}, "head": {"head_white": {"type": "mesh", "uvs": [0.5914, 0, 0.66218, 0.03365, 0.78181, 0.12844, 0.8617, 0.28672, 0.90599, 0.44925, 0.99957, 0.50262, 1, 0.54455, 1, 0.61962, 0.96393, 0.67223, 0.88355, 0.6625, 0.80316, 0.65278, 0.79322, 0.70263, 0.70629, 0.8276, 0.63503, 0.90287, 0.57645, 0.87968, 0.53911, 0.93472, 0.4766, 0.97799, 0.41445, 1, 0.31142, 0.96405, 0.24118, 0.88454, 0.16536, 0.90319, 0.02881, 0.71768, 0, 0.63441, 0.02328, 0.58197, 0.03141, 0.53966, 0.00759, 0.44193, 0.03109, 0.36828, 0.09332, 0.22898, 0.18141, 0.11526, 0.29492, 0.06723, 0.37197, 0.04051, 0.44903, 0.0138, 0.48749, 0.0069, 0.52594, 0, 0.47022, 0.60398, 0.46572, 0.65131, 0.45529, 0.72229, 0.44185, 0.8137, 0.42815, 0.90685, 0.4213, 0.95342, 0.47979, 0.50022, 0.48624, 0.43035, 0.49161, 0.37207, 0.49854, 0.29703, 0.50519, 0.22497, 0.51556, 0.11248, 0.52075, 0.05624, 0.52441, 0.01657, 0.27726, 0.1702, 0.26629, 0.29112, 0.25866, 0.39931, 0.24369, 0.47108, 0.25081, 0.57182, 0.2445, 0.61664, 0.22764, 0.67835, 0.23533, 0.76569, 0.76183, 0.26942, 0.74565, 0.38935, 0.74559, 0.43735, 0.74312, 0.48745, 0.73989, 0.55992, 0.90583, 0.47853, 0.90583, 0.52362, 0.90891, 0.59871, 0.71985, 0.64057, 0.7107, 0.6897, 0.64869, 0.12046, 0.63351, 0.2472, 0.62209, 0.34319, 0.6186, 0.40471, 0.61468, 0.4589, 0.60984, 0.53007, 0.59503, 0.62227, 0.58821, 0.6705, 0.58079, 0.77495], "triangles": [61, 58, 4, 5, 61, 4, 62, 61, 5, 59, 61, 62, 62, 5, 6, 60, 59, 62, 63, 62, 6, 60, 62, 63, 63, 6, 7, 10, 60, 63, 64, 60, 10, 9, 10, 63, 8, 63, 7, 9, 63, 8, 11, 64, 10, 57, 68, 56, 57, 56, 3, 69, 68, 57, 58, 69, 57, 57, 3, 4, 58, 57, 4, 70, 69, 58, 59, 70, 58, 59, 58, 61, 71, 70, 59, 60, 71, 59, 46, 0, 1, 66, 46, 1, 66, 1, 2, 67, 45, 66, 56, 66, 2, 67, 66, 56, 56, 2, 3, 68, 43, 67, 68, 67, 56, 47, 32, 33, 47, 33, 0, 46, 32, 47, 31, 32, 46, 46, 47, 0, 45, 31, 46, 30, 31, 45, 45, 46, 66, 48, 28, 29, 44, 30, 45, 29, 30, 44, 44, 45, 67, 48, 27, 28, 49, 27, 48, 44, 48, 29, 43, 44, 67, 48, 43, 49, 44, 43, 48, 42, 49, 43, 42, 43, 68, 50, 27, 49, 50, 49, 42, 26, 27, 50, 69, 42, 68, 41, 50, 42, 41, 42, 69, 70, 41, 69, 51, 26, 50, 25, 26, 51, 40, 50, 41, 51, 50, 40, 24, 25, 51, 24, 51, 52, 40, 41, 70, 71, 40, 70, 52, 51, 40, 34, 52, 40, 34, 40, 71, 53, 24, 52, 23, 24, 53, 72, 34, 71, 64, 72, 71, 60, 64, 71, 34, 53, 52, 35, 34, 72, 35, 53, 34, 73, 35, 72, 54, 23, 53, 22, 23, 54, 65, 72, 64, 73, 72, 65, 65, 64, 11, 21, 22, 54, 36, 53, 35, 54, 53, 36, 55, 54, 36, 21, 54, 55, 73, 36, 35, 74, 73, 65, 74, 36, 73, 37, 55, 36, 37, 36, 74, 12, 74, 65, 12, 65, 11, 14, 37, 74, 13, 14, 74, 19, 55, 37, 12, 13, 74, 20, 21, 55, 20, 55, 19, 38, 19, 37, 15, 38, 37, 14, 15, 37, 38, 18, 19, 16, 39, 38, 39, 18, 38, 15, 16, 38, 17, 18, 39, 17, 39, 16], "vertices": [2, 12, 191.34, -16.64, 0.42868, 13, -5.93, 56.27, 0.57132, 2, 12, 187.15, -47.09, 0.12091, 13, 23.87, 63.8, 0.87909, 2, 13, 83.08, 65.39, 0.9428, 14, -40.54, 59.32, 0.0572, 2, 13, 144.75, 38.54, 0.12983, 14, 24.82, 43.44, 0.87017, 2, 14, 81.99, 15.98, 0.25459, 15, 3.65, 17, 0.74541, 1, 15, 39.75, 38.69, 1, 1, 15, 53.37, 30.82, 1, 1, 15, 77.59, 16.48, 1, 2, 11, 67.33, -221.23, 0.00065, 15, 87.29, -5.87, 0.99935, 3, 11, 69.1, -189.23, 0.04604, 14, 135.78, -43.86, 0.01309, 15, 67.93, -31.4, 0.94087, 4, 11, 70.87, -157.24, 0.27431, 12, -19.84, -166.16, 2e-05, 14, 111.87, -65.2, 0.11514, 15, 48.57, -56.93, 0.61053, 3, 11, 51.98, -154.41, 0.44557, 14, 123.21, -80.58, 0.1173, 15, 62.65, -69.84, 0.43713, 3, 11, 3.18, -122.8, 0.72605, 14, 135.29, -137.46, 0.06256, 15, 85.43, -123.34, 0.2114, 3, 11, -26.66, -96.28, 0.8229, 14, 137.58, -177.3, 0.0375, 15, 95.33, -162.01, 0.1396, 3, 11, -19.34, -72.61, 0.88419, 14, 115.66, -188.83, 0.02421, 15, 76.03, -177.54, 0.0916, 3, 11, -40.81, -59.07, 0.95467, 14, 121.23, -213.61, 0.00768, 15, 86.25, -200.78, 0.03765, 3, 11, -58.46, -35.31, 0.98623, 14, 116.87, -242.88, 0.00138, 15, 87.61, -230.34, 0.01239, 3, 11, -68.15, -11.22, 0.99668, 14, 106.66, -266.75, 4e-05, 15, 82.16, -255.73, 0.00328, 2, 11, -57.08, 30.3, 0.99896, 12, -186.77, -12.28, 0.00104, 2, 11, -28.96, 59.82, 0.97254, 12, -166.02, 22.81, 0.02746, 2, 11, -37.7, 89.38, 0.94388, 12, -181.2, 49.65, 0.05612, 2, 11, 28.57, 147.44, 0.74751, 12, -129.72, 121.15, 0.25249, 2, 11, 59.07, 160.67, 0.6599, 12, -102.98, 140.91, 0.3401, 2, 11, 79.25, 152.62, 0.58315, 12, -81.51, 137.61, 0.41685, 2, 11, 95.28, 150.34, 0.47935, 12, -65.38, 139, 0.52065, 2, 11, 131.3, 161.9, 0.29204, 12, -32.88, 158.39, 0.70796, 2, 11, 159.42, 154.24, 0.19642, 12, -3.76, 157.26, 0.80358, 2, 11, 213.02, 132.7, 0.04257, 12, 53.31, 148.35, 0.95743, 2, 11, 257.64, 100.39, 0.00093, 12, 104.06, 126.92, 0.99907, 1, 12, 134.03, 88.88, 1, 1, 12, 152.25, 62.43, 1, 2, 12, 170.47, 35.98, 0.99408, 13, -46.89, 17.19, 0.00592, 2, 12, 177.25, 22.1, 0.93293, 13, -36.56, 28.68, 0.06707, 2, 12, 184.03, 8.22, 0.77671, 13, -26.24, 40.17, 0.22329, 4, 11, 81.4, -24.55, 0.9528, 13, 97.21, -151, 0.00304, 14, 10.49, -151.45, 0.02483, 15, -34.36, -161.05, 0.01933, 4, 11, 63.58, -23.81, 0.96061, 13, 106.85, -166.02, 0.00073, 14, 22.55, -164.59, 0.01808, 15, -20, -171.62, 0.02059, 3, 11, 36.76, -21.25, 0.97305, 14, 39.67, -185.38, 0.00914, 15, 0.8, -188.74, 0.0178, 3, 11, 2.23, -17.95, 0.9849, 14, 61.72, -212.16, 0.00323, 15, 27.58, -210.79, 0.01187, 3, 11, -32.96, -14.59, 0.99223, 14, 84.19, -239.45, 0.00076, 15, 54.87, -233.26, 0.007, 3, 11, -50.55, -12.9, 0.99514, 14, 95.42, -253.1, 0.00022, 15, 68.52, -244.49, 0.00464, 5, 11, 120.47, -26.05, 0.73338, 12, -1.09, -27.17, 0.20002, 13, 76.01, -118.16, 0.01917, 14, -16.04, -122.73, 0.03618, 15, -65.91, -137.96, 0.01126, 5, 11, 146.77, -27.06, 0.20746, 12, 24.77, -22.23, 0.71576, 13, 61.72, -96.05, 0.04115, 14, -33.9, -103.39, 0.03097, 15, -87.15, -122.41, 0.00466, 5, 11, 168.71, -27.9, 0.04758, 12, 46.33, -18.1, 0.86822, 13, 49.81, -77.6, 0.06562, 14, -48.8, -87.26, 0.01754, 15, -104.87, -109.44, 0.00103, 4, 11, 196.96, -28.98, 0.00476, 12, 74.1, -12.79, 0.90158, 13, 34.48, -53.85, 0.09052, 14, -67.98, -66.49, 0.00314, 2, 12, 100.77, -7.69, 0.88775, 13, 19.75, -31.04, 0.11225, 1, 12, 142.4, 0.27, 1, 2, 12, 163.21, 4.25, 0.81478, 13, -14.74, 22.36, 0.18522, 2, 12, 177.9, 7.05, 0.78861, 13, -22.85, 34.92, 0.21139, 2, 11, 239.3, 61.28, 6e-05, 12, 95.01, 84.69, 0.99994, 2, 11, 193.78, 62.96, 0.01397, 12, 50.28, 76.07, 0.98603, 2, 11, 153.1, 63.59, 0.11789, 12, 10.51, 67.51, 0.88211, 2, 11, 125.89, 67.93, 0.32055, 12, -16.99, 65.61, 0.67945, 2, 11, 88.34, 62.89, 0.66586, 12, -52.43, 52.24, 0.33414, 2, 11, 71.41, 64.4, 0.7658, 12, -69.26, 49.9, 0.2342, 2, 11, 47.92, 69.71, 0.84615, 12, -93.35, 49.77, 0.15385, 2, 11, 15.4, 64.75, 0.93076, 12, -123.91, 37.61, 0.06924, 2, 13, 109.73, 19.05, 0.64544, 14, -6.33, 18.23, 0.35456, 5, 11, 168.15, -128.7, 0.03089, 12, 68.5, -116.44, 0.01478, 13, 132.66, -20.17, 0.06069, 14, 22.97, -16.47, 0.88433, 15, -48.04, -26.19, 0.00931, 5, 11, 150.18, -129.74, 0.07891, 12, 51.23, -121.5, 0.02096, 13, 143.82, -34.28, 0.04692, 14, 36.4, -28.46, 0.79115, 15, -32.56, -35.38, 0.06205, 5, 11, 131.37, -129.86, 0.15139, 12, 32.93, -125.86, 0.02012, 13, 154.73, -49.61, 0.02896, 14, 49.77, -41.7, 0.60449, 15, -16.89, -45.8, 0.19504, 5, 11, 104.17, -130.19, 0.27232, 12, 6.5, -132.3, 0.01031, 13, 170.62, -71.7, 0.0108, 14, 69.21, -60.73, 0.32927, 15, 5.84, -60.74, 0.3773, 2, 14, 90.14, 8.63, 0.00639, 15, 13.06, 11.35, 0.99361, 1, 15, 27.61, 2.73, 1, 3, 11, 93.57, -197.86, 0.01258, 14, 124.6, -20.45, 0.00234, 15, 52.46, -10.57, 0.98508, 5, 11, 73.51, -124.04, 0.45277, 12, -24.76, -133.22, 0.00232, 13, 183.19, -100.33, 0.00289, 14, 86.5, -86.78, 0.1734, 15, 27.82, -82.98, 0.36863, 5, 11, 54.9, -121.5, 0.54816, 12, -43.46, -134.94, 0.00023, 13, 191.8, -117.01, 0.00066, 14, 97.85, -101.75, 0.12503, 15, 41.82, -95.49, 0.32592, 2, 12, 154.41, -51.15, 0.02287, 13, 39.92, 34.98, 0.97713, 4, 11, 218.76, -81.24, 0.00133, 12, 107.11, -58.8, 0.02541, 13, 64.74, -6, 0.97247, 14, -46.37, -14.15, 0.00078, 5, 11, 182.56, -78.84, 0.06127, 12, 71.3, -64.62, 0.2269, 13, 83.57, -37.01, 0.5938, 14, -22.5, -41.48, 0.11531, 15, -87.86, -59.47, 0.00272, 5, 11, 159.45, -78.82, 0.17212, 12, 48.78, -69.8, 0.24494, 13, 96.82, -55.94, 0.32336, 14, -6.2, -57.86, 0.24016, 15, -68.72, -72.41, 0.01941, 5, 11, 139.07, -78.46, 0.31707, 12, 28.85, -74.04, 0.19104, 13, 108.23, -72.83, 0.16818, 14, 7.94, -72.54, 0.27312, 15, -52.02, -84.1, 0.0506, 5, 11, 112.32, -78.12, 0.5168, 12, 2.7, -79.74, 0.08624, 13, 123.31, -94.93, 0.06177, 14, 26.58, -91.73, 0.23197, 15, -30.04, -99.35, 0.10322, 5, 11, 77.45, -74.29, 0.7095, 12, -32.12, -83.87, 0.01074, 13, 140.2, -125.67, 0.01222, 14, 48.49, -119.12, 0.13173, 15, -3.27, -122.02, 0.13581, 5, 11, 59.24, -72.66, 0.76618, 12, -50.24, -86.38, 0.00112, 13, 149.33, -141.51, 0.00378, 14, 60.2, -133.17, 0.09272, 15, 10.91, -133.56, 0.13621, 3, 11, 19.97, -72.02, 0.83632, 14, 87.48, -161.42, 0.04428, 15, 43.11, -156.04, 0.1194], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 42, 44, 34, 36, 68, 70, 72, 70, 74, 72, 74, 28, 76, 74, 38, 76, 76, 30, 36, 38, 34, 78, 78, 76, 36, 78, 78, 32, 80, 68, 82, 80, 84, 82, 86, 84, 88, 86, 58, 88, 90, 88, 58, 60, 60, 62, 90, 60, 92, 90, 62, 92, 2, 92, 66, 94, 94, 92, 0, 94, 62, 64, 64, 66, 94, 64, 56, 96, 96, 86, 58, 96, 54, 98, 98, 84, 96, 98, 52, 100, 100, 82, 98, 100, 50, 102, 102, 80, 100, 102, 48, 104, 104, 68, 102, 104, 46, 106, 106, 70, 104, 106, 44, 108, 108, 72, 106, 108, 42, 110, 110, 74, 108, 110, 110, 38, 6, 112, 4, 112, 8, 114, 112, 114, 114, 116, 116, 118, 118, 120, 120, 20, 10, 122, 122, 116, 8, 122, 12, 124, 124, 118, 122, 124, 14, 126, 126, 120, 124, 126, 16, 18, 18, 20, 126, 18, 20, 128, 120, 128, 22, 130, 128, 130, 130, 24, 4, 132, 132, 90, 2, 132, 88, 134, 134, 112, 132, 134, 86, 136, 136, 114, 134, 136, 84, 138, 138, 116, 136, 138, 82, 140, 140, 118, 138, 140, 80, 142, 142, 120, 140, 142, 68, 144, 144, 128, 142, 144, 70, 146, 146, 130, 144, 146, 24, 148, 148, 72, 146, 148, 148, 28], "width": 396, "height": 375}}, "left_arm": {"left_arm_white": {"type": "mesh", "uvs": [0.95744, 0, 0.98739, 0.01161, 1, 0.03703, 1, 0.07089, 0.97886, 0.16462, 0.88969, 0.33484, 0.73886, 0.51617, 0.60033, 0.66879, 0.56339, 0.72473, 0.56322, 0.73809, 0.56233, 0.80752, 0.47471, 0.87633, 0.39598, 0.93817, 0.31725, 1, 0.24019, 1, 0.08934, 0.9753, 0.02442, 0.93865, 0, 0.87957, 0, 0.76846, 0.05281, 0.68909, 0.08024, 0.6629, 0.06945, 0.59973, 0.16958, 0.51749, 0.21737, 0.50109, 0.27227, 0.43872, 0.4195, 0.30299, 0.62825, 0.13226, 0.82885, 0.02213, 0.92226, 0, 0.97872, 0.01851, 0.95866, 0.03319, 0.0519, 0.91202, 0.09828, 0.86707, 0.17594, 0.7918, 0.22935, 0.74004, 0.26593, 0.70458, 0.32555, 0.6468, 0.36617, 0.60743, 0.42366, 0.55171, 0.57724, 0.40286, 0.7498, 0.23562, 0.89109, 0.09869], "triangles": [29, 0, 1, 30, 0, 29, 28, 0, 30, 29, 1, 2, 30, 29, 2, 3, 30, 2, 41, 27, 28, 41, 28, 30, 41, 30, 3, 26, 27, 41, 4, 41, 3, 40, 26, 41, 40, 41, 4, 5, 40, 4, 40, 39, 26, 39, 40, 5, 39, 25, 26, 6, 39, 5, 38, 25, 39, 38, 39, 6, 24, 25, 38, 23, 24, 38, 7, 38, 6, 7, 37, 38, 37, 23, 38, 36, 23, 37, 22, 23, 36, 35, 20, 21, 22, 35, 21, 36, 35, 22, 8, 37, 7, 9, 37, 8, 34, 20, 35, 33, 20, 34, 19, 20, 33, 18, 19, 33, 9, 36, 37, 10, 36, 9, 32, 18, 33, 11, 36, 10, 35, 36, 11, 34, 35, 11, 17, 18, 32, 31, 17, 32, 12, 34, 11, 33, 34, 12, 16, 17, 31, 15, 31, 32, 16, 31, 15, 13, 14, 33, 32, 33, 14, 15, 32, 14, 12, 13, 33], "vertices": [1, 7, -20.44, -13.46, 1, 1, 7, -22.66, -8.57, 1, 1, 7, -20.97, -3.41, 1, 1, 7, -16.64, 1.71, 1, 1, 7, -2.04, 13.67, 1, 2, 8, -23.19, 35.68, 0.04563, 7, 30.76, 30.06, 0.95437, 2, 8, 20.17, 38.02, 0.83488, 7, 72.6, 41.69, 0.16512, 3, 9, -10.54, 39.3, 0.20195, 8, 57.81, 38.49, 0.79785, 7, 109.26, 50.26, 0.0002, 2, 9, 1.97, 40.63, 0.49334, 8, 70.25, 40.45, 0.50666, 2, 9, 4.17, 42.11, 0.55818, 8, 72.37, 42.03, 0.44182, 2, 9, 15.58, 49.78, 0.73904, 8, 83.39, 50.25, 0.26096, 2, 9, 34.85, 45.8, 0.89825, 8, 102.83, 47.22, 0.10175, 2, 9, 52.16, 42.22, 0.97964, 8, 120.3, 44.5, 0.02036, 2, 9, 69.48, 38.64, 0.99914, 8, 137.77, 41.78, 0.00086, 1, 9, 76.55, 28.35, 1, 1, 9, 86.36, 5.44, 1, 1, 9, 86.33, -7.34, 1, 1, 9, 78.93, -17.22, 1, 1, 9, 60.8, -29.68, 1, 1, 9, 43, -31.53, 1, 1, 9, 36.21, -30.81, 1, 1, 9, 26.89, -39.33, 1, 2, 9, 4.29, -35.18, 0.96815, 8, 76.29, -35.16, 0.03185, 2, 9, -2.77, -30.64, 0.85487, 8, 69.02, -30.98, 0.14513, 2, 9, -17.99, -30.31, 0.44839, 8, 53.8, -31.39, 0.55161, 3, 9, -53.65, -25.87, 0.01295, 8, 17.97, -28.72, 0.97988, 7, 84.83, -23.95, 0.00717, 2, 8, -29.42, -22.32, 0.05149, 7, 37.18, -27.91, 0.94851, 1, 7, -1.71, -23.57, 1, 1, 7, -16.09, -17.14, 1, 1, 7, -20.71, -8.43, 1, 1, 7, -16.35, -8.31, 1, 1, 9, 79.47, -6.65, 1, 1, 9, 67.87, -5.5, 1, 1, 9, 48.47, -3.57, 1, 1, 9, 35.12, -2.25, 1, 1, 9, 25.98, -1.34, 1, 2, 9, 11.08, 0.14, 0.99975, 8, 81.33, 0.45, 0.00025, 1, 9, 0.93, 1.15, 1, 1, 8, 56.73, 1.68, 1, 2, 8, 18.2, 3.6, 0.99493, 7, 78.1, 7.65, 0.00507, 1, 7, 35.37, 0.43, 1, 1, 7, 0.38, -5.48, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 52, 54, 0, 58, 58, 4, 2, 58, 6, 60, 60, 56, 58, 60, 32, 62, 34, 62, 62, 30, 64, 62, 36, 64, 64, 28, 66, 64, 38, 66, 66, 26, 68, 66, 40, 68, 70, 68, 42, 70, 20, 22, 70, 22, 22, 24, 24, 26, 68, 24, 72, 70, 44, 72, 72, 20, 74, 72, 46, 74, 16, 18, 18, 20, 74, 18, 76, 74, 48, 76, 78, 76, 50, 78, 78, 12, 80, 78, 52, 80, 80, 10, 60, 82, 82, 80, 54, 82, 82, 8], "width": 162, "height": 198}}, "left_leg": {"left_leg_white": {"type": "mesh", "uvs": [0.63535, 0.12997, 0.98942, 0.25994, 0.98303, 0.38188, 0.97664, 0.50382, 0.96837, 0.66159, 0.95949, 0.83079, 0.95506, 0.9154, 0.95062, 1, 0.59169, 1, 0.21659, 1, 0.03551, 1, 0, 0.86736, 0, 0.72317, 0.08078, 0.55751, 0.2554, 0.42866, 0.26833, 0.21433, 0.28127, 0, 0.61602, 0.46624, 0.60741, 0.61926, 0.59687, 0.79012, 0.59734, 0.8974, 0.25309, 0.57772, 0.23463, 0.74949, 0.21565, 0.8782, 0.62568, 0.29811], "triangles": [6, 7, 20, 7, 8, 20, 8, 9, 20, 10, 23, 9, 9, 23, 20, 10, 11, 23, 6, 20, 5, 20, 23, 19, 23, 22, 19, 20, 19, 5, 23, 11, 22, 11, 12, 22, 5, 19, 4, 19, 18, 4, 19, 22, 18, 12, 13, 22, 22, 21, 18, 22, 13, 21, 4, 18, 3, 18, 21, 17, 21, 13, 14, 18, 17, 3, 21, 14, 17, 3, 17, 2, 17, 24, 2, 17, 14, 24, 14, 15, 24, 2, 24, 1, 24, 0, 1, 24, 15, 0, 15, 16, 0], "vertices": [1, 3, 4.68, -0.11, 1, 2, 3, 12.03, 41.38, 0.98566, 18, -42.37, 33.6, 0.01434, 2, 3, 26.17, 43.43, 0.91244, 18, -28.93, 38.43, 0.08756, 2, 3, 40.31, 45.48, 0.70522, 18, -15.48, 43.26, 0.29478, 2, 3, 58.6, 48.13, 0.34941, 18, 1.91, 49.51, 0.65059, 2, 3, 78.22, 50.97, 0.09895, 18, 20.56, 56.22, 0.90105, 2, 3, 88.02, 52.4, 0.04362, 18, 29.89, 59.57, 0.95638, 2, 3, 97.83, 53.82, 0.02515, 18, 39.22, 62.92, 0.97485, 1, 18, 54.54, 26.15, 1, 1, 18, 70.55, -12.29, 1, 1, 18, 78.28, -30.84, 1, 2, 3, 102.91, -52.72, 0.00014, 18, 65.47, -40.45, 0.99986, 2, 3, 86.35, -55.96, 0.01009, 18, 49.9, -46.94, 0.98991, 2, 3, 65.61, -50.89, 0.07698, 18, 28.56, -46.11, 0.92302, 2, 3, 47.08, -34.77, 0.46557, 18, 7.19, -34.02, 0.53443, 2, 3, 22.2, -38.19, 0.9516, 18, -16.51, -42.34, 0.0484, 1, 3, -2.68, -41.61, 1, 2, 3, 43.7, 5.35, 0.94808, 18, -4.15, 4.62, 0.05192, 2, 3, 61.45, 7.86, 0.03689, 18, 12.75, 10.62, 0.96311, 2, 3, 81.29, 10.56, 0.00486, 18, 31.65, 17.23, 0.99514, 2, 3, 93.6, 13.03, 0.00061, 18, 43.22, 22.11, 0.99939, 2, 3, 64.25, -31.67, 0.13349, 18, 23.39, -27.55, 0.86651, 2, 3, 84.36, -29.81, 0.01117, 18, 42.73, -21.71, 0.98883, 2, 3, 99.55, -28.98, 0, 18, 57.44, -17.87, 1, 1, 3, 24.19, 2.62, 1], "hull": 17, "edges": [20, 22, 22, 24, 24, 26, 26, 28, 6, 8, 8, 10, 10, 12, 12, 14, 2, 0, 0, 32, 6, 34, 34, 28, 8, 36, 34, 36, 10, 38, 36, 38, 12, 40, 38, 40, 14, 16, 40, 16, 26, 42, 42, 36, 28, 42, 24, 44, 44, 38, 42, 44, 22, 46, 46, 40, 44, 46, 16, 18, 18, 20, 46, 18, 28, 30, 30, 32, 0, 48, 48, 34, 30, 48, 2, 4, 4, 6, 48, 4], "width": 111, "height": 117}}, "right_arm": {"right_arm_white": {"type": "mesh", "uvs": [0.08058, 0.00014, 0.17868, 0.02658, 0.3269, 0.09738, 0.5011, 0.23482, 0.612, 0.33453, 0.72289, 0.43424, 0.77163, 0.49109, 0.84094, 0.52605, 0.88062, 0.55871, 0.9203, 0.59136, 0.92025, 0.66992, 0.98582, 0.74436, 1, 0.81168, 0.97757, 0.95852, 0.75567, 1, 0.68117, 1, 0.63262, 0.97358, 0.58359, 0.93153, 0.53456, 0.88948, 0.48553, 0.84744, 0.4365, 0.80539, 0.44073, 0.73361, 0.40248, 0.67498, 0.29998, 0.56982, 0.16674, 0.43313, 0.02996, 0.19872, 0, 0.10171, 0, 0.02477, 0.03085, 0, 0.73205, 0.76293, 0.82144, 0.83741, 0.87582, 0.8836, 0.6325, 0.65462, 0.59368, 0.61237, 0.55352, 0.56869, 0.68227, 0.70877, 0.77674, 0.80017, 0.32623, 0.32138, 0.43988, 0.44504, 0.16324, 0.14405, 0.09705, 0.07202, 0.0429, 0.01311], "triangles": [9, 29, 35, 9, 10, 29, 36, 29, 10, 36, 10, 11, 30, 36, 11, 12, 30, 11, 35, 19, 20, 18, 19, 35, 31, 30, 12, 29, 18, 35, 17, 18, 29, 17, 29, 36, 13, 31, 12, 16, 17, 36, 16, 36, 30, 14, 15, 16, 30, 14, 16, 31, 14, 30, 14, 31, 13, 5, 34, 38, 5, 38, 4, 34, 5, 6, 23, 24, 38, 23, 38, 34, 33, 34, 6, 32, 33, 6, 32, 6, 7, 8, 9, 35, 22, 23, 34, 22, 34, 33, 35, 32, 7, 35, 7, 8, 21, 22, 33, 21, 33, 32, 20, 21, 32, 35, 20, 32, 41, 28, 0, 27, 28, 41, 40, 0, 1, 41, 0, 40, 40, 26, 27, 40, 27, 41, 39, 40, 1, 39, 1, 2, 25, 26, 40, 25, 40, 39, 37, 39, 2, 37, 2, 3, 37, 25, 39, 24, 25, 37, 38, 37, 3, 38, 3, 4, 24, 37, 38], "vertices": [1, 4, -18.93, 13.2, 1, 1, 4, -4.49, 21.65, 1, 1, 4, 21.91, 30.37, 1, 1, 4, 60.99, 33.5, 1, 2, 4, 87.68, 33.9, 0.8575, 5, 5.33, 34.22, 0.1425, 2, 4, 114.37, 34.29, 0.27184, 5, 32.02, 34.78, 0.72816, 2, 4, 128.04, 32.76, 0.0882, 5, 45.69, 33.33, 0.9118, 3, 4, 140.66, 36.59, 0.02075, 5, 58.29, 37.24, 0.97309, 6, -19.09, 37.67, 0.00616, 3, 4, 149.76, 37.13, 0.00596, 5, 67.38, 37.84, 0.95175, 6, -10.06, 38.87, 0.04229, 3, 4, 158.86, 37.66, 0.0009, 5, 76.48, 38.43, 0.89331, 6, -1.02, 40.08, 0.10579, 2, 5, 88.2, 28.2, 0.57717, 6, 11.36, 30.66, 0.42283, 2, 5, 106.29, 26.52, 0.16273, 6, 29.52, 30.19, 0.83727, 2, 5, 117.85, 19.49, 0.0497, 6, 41.53, 23.96, 0.9503, 1, 6, 62.47, 3.47, 1, 1, 6, 47.26, -30.12, 1, 1, 6, 39.95, -39.73, 1, 2, 5, 102.88, -46.43, 0.00108, 6, 31.03, -42.82, 0.99892, 2, 5, 91.39, -46.95, 0.02195, 6, 19.59, -44.11, 0.97805, 2, 5, 79.89, -47.46, 0.09627, 6, 8.16, -45.39, 0.90373, 2, 5, 68.4, -47.97, 0.2305, 6, -3.28, -46.68, 0.7695, 2, 5, 56.9, -48.49, 0.36106, 6, -14.71, -47.96, 0.63894, 2, 5, 46.64, -38.63, 0.60188, 6, -25.61, -38.82, 0.39812, 2, 5, 33.82, -35.67, 0.84716, 6, -38.6, -36.73, 0.15284, 3, 4, 89.14, -34.83, 0.0796, 5, 7.21, -34.5, 0.91205, 6, -65.23, -37.35, 0.00835, 2, 4, 54.56, -33.09, 0.79659, 5, -27.37, -32.97, 0.20341, 1, 4, 5.11, -18.97, 1, 1, 4, -12.5, -9.89, 1, 1, 4, -23.92, 0.19, 1, 1, 4, -24.29, 7.18, 1, 1, 6, 7.57, -4.76, 1, 1, 6, 28.07, -2.15, 1, 1, 6, 40.69, -0.68, 1, 2, 5, 55.27, -4.94, 0.94008, 6, -19.26, -4.62, 0.05992, 2, 5, 44.84, -4.18, 0.98151, 6, -29.73, -4.57, 0.01849, 2, 5, 34.04, -3.4, 0.99467, 6, -40.55, -4.51, 0.00533, 2, 5, 68.66, -5.91, 0.59841, 6, -5.85, -4.69, 0.40159, 1, 6, 17.82, -3.46, 1, 1, 4, 55.08, 0.92, 1, 1, 5, 3.49, -1.18, 1, 1, 4, 11.29, 4.38, 1, 1, 4, -6.5, 5.78, 1, 1, 4, -21.05, 6.93, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 22, 24, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 50, 52, 52, 54, 54, 56, 48, 50, 18, 58, 58, 36, 22, 60, 60, 32, 26, 62, 62, 60, 24, 62, 62, 28, 24, 26, 14, 64, 64, 40, 66, 64, 12, 66, 66, 42, 68, 66, 10, 68, 68, 44, 14, 16, 16, 18, 58, 70, 70, 64, 16, 70, 36, 38, 38, 40, 70, 38, 18, 20, 20, 22, 58, 72, 72, 60, 20, 72, 32, 34, 34, 36, 72, 34, 6, 74, 74, 48, 6, 8, 8, 10, 68, 76, 76, 74, 8, 76, 44, 46, 46, 48, 76, 46, 78, 74, 4, 78, 78, 50, 56, 0, 80, 78, 2, 80, 52, 80, 56, 82, 82, 80, 0, 82, 82, 54], "width": 162, "height": 198}}, "right_leg": {"right_leg_white": {"type": "mesh", "uvs": [0.05065, 0.87902, 0.04099, 0.75803, 0.03459, 0.6779, 0.02252, 0.52674, 0.01126, 0.38572, 0, 0.2447, 0.3483, 0.12235, 0.69661, 0, 0.71601, 0.21438, 0.73541, 0.42877, 0.95206, 0.60977, 1, 0.76316, 0.99087, 0.87973, 0.96823, 1, 0.74125, 1, 0.51427, 1, 0.06031, 1, 0.4132, 0.46552, 0.45011, 0.66071, 0.46709, 0.75048, 0.49068, 0.87524, 0.38075, 0.29393, 0.7441, 0.63524, 0.73354, 0.75682, 0.74077, 0.87749], "triangles": [12, 13, 24, 15, 24, 14, 13, 14, 24, 16, 20, 15, 15, 20, 24, 16, 0, 20, 12, 24, 11, 0, 19, 20, 0, 1, 19, 20, 23, 24, 24, 23, 11, 20, 19, 23, 11, 23, 10, 23, 22, 10, 1, 18, 19, 1, 2, 18, 23, 19, 22, 19, 18, 22, 2, 17, 18, 2, 3, 17, 22, 18, 9, 18, 17, 9, 22, 9, 10, 3, 4, 17, 4, 21, 17, 17, 21, 9, 21, 8, 9, 4, 5, 21, 5, 6, 21, 21, 6, 8, 6, 7, 8], "vertices": [2, 16, 73.06, -44.99, 0.07209, 17, -6.47, -47.12, 0.92791, 2, 16, 59, -43.09, 0.26624, 17, -18.08, -38.95, 0.73376, 2, 16, 49.68, -41.83, 0.45641, 17, -25.76, -33.54, 0.54359, 2, 16, 32.1, -39.45, 0.79402, 17, -40.27, -23.33, 0.20598, 2, 16, 15.7, -37.23, 0.95487, 17, -53.8, -13.81, 0.04513, 2, 16, -0.69, -35.02, 0.99638, 17, -67.33, -4.28, 0.00362, 1, 16, -6.63, 5.78, 1, 1, 16, -12.58, 46.58, 1, 2, 16, 12.4, 43.45, 0.99806, 17, -19.54, 59.32, 0.00194, 2, 16, 37.39, 40.33, 0.84308, 17, 1.19, 45.04, 0.15692, 2, 16, 63.11, 59.44, 0.31805, 17, 32.82, 50.14, 0.68195, 2, 16, 81.77, 60.9, 0.1496, 17, 50.06, 42.84, 0.8504, 2, 16, 94.9, 57.07, 0.0641, 17, 59.94, 33.39, 0.9359, 2, 16, 108.14, 51.68, 0.02319, 17, 69.21, 22.5, 0.97681, 2, 16, 102.89, 27.04, 0.00346, 17, 53.19, 3.05, 0.99654, 1, 17, 37.17, -16.39, 1, 2, 16, 87.13, -46.89, 0.01089, 17, 5.14, -55.29, 0.98911, 1, 16, 34.14, 4.46, 1, 2, 16, 57.33, 3.7, 0.74287, 17, 2, 3.34, 0.25713, 1, 17, 11.31, -1.88, 1, 1, 17, 24.24, -9.14, 1, 1, 16, 13.75, 5.12, 1, 2, 16, 61.21, 36.24, 0.44155, 17, 20.45, 30.43, 0.55845, 2, 16, 74.88, 32.13, 0.18094, 17, 30.68, 20.48, 0.81906, 2, 16, 88.86, 29.97, 0.03658, 17, 42.09, 12.13, 0.96342], "hull": 17, "edges": [18, 20, 20, 22, 22, 24, 24, 26, 10, 12, 12, 14, 30, 32, 18, 34, 34, 6, 36, 34, 6, 4, 36, 4, 38, 36, 4, 2, 38, 2, 30, 40, 40, 38, 2, 0, 0, 32, 40, 0, 6, 8, 8, 10, 12, 42, 42, 34, 8, 42, 14, 16, 16, 18, 42, 16, 20, 44, 44, 36, 18, 44, 22, 46, 46, 38, 44, 46, 24, 48, 48, 40, 46, 48, 26, 28, 28, 30, 48, 28], "width": 111, "height": 117}}}}], "animations": {"dance": {"bones": {"bone": {"translate": [{"y": -22.32, "curve": [0.022, 0, 0.045, 0, 0.022, -22.32, 0.045, -7]}, {"time": 0.0667, "y": 1.19, "curve": [0.089, 0, 0.267, 0, 0.089, 9.38, 0.267, 74.74]}, {"time": 0.3333, "y": 74.74, "curve": [0.444, 0, 0.556, 0, 0.444, 74.74, 0.556, 23.76]}, {"time": 0.6667, "y": 0.65, "curve": [0.711, 0, 0.756, 0, 0.711, -8.59, 0.756, -22.32]}, {"time": 0.8, "y": -22.32}]}, "left_leg": {"translate": [{"x": -19.68, "y": -0.93, "curve": [0.033, -19.68, 0.067, 10.1, 0.033, -0.93, 0.067, 0.48]}, {"time": 0.1, "x": 10.1, "y": 0.48, "curve": [0.178, 10.1, 0.256, 5.63, 0.178, 0.48, 0.256, 0.27]}, {"time": 0.3333, "x": 4.69, "y": 0.22, "curve": [0.444, 3.35, 0.556, 4.69, 0.444, 0.16, 0.556, 0.22]}, {"time": 0.6667, "x": 3.24, "y": 0.15, "curve": [0.711, 2.66, 0.756, -19.68, 0.711, 0.13, 0.756, -0.93]}, {"time": 0.8, "x": -19.68, "y": -0.93}]}, "right_leg": {"translate": [{"x": -19.97, "y": -0.95, "curve": [0.033, -19.97, 0.067, 10.08, 0.033, -0.95, 0.067, 0.48]}, {"time": 0.1, "x": 10.08, "y": 0.48, "curve": [0.178, 10.08, 0.256, 5.59, 0.178, 0.48, 0.256, 0.27]}, {"time": 0.3333, "x": 5.3, "y": 0.25, "curve": [0.444, 4.89, 0.556, 5.3, 0.444, 0.23, 0.556, 0.25]}, {"time": 0.6667, "x": 4.89, "y": 0.23, "curve": [0.711, 4.72, 0.756, -19.97, 0.711, 0.22, 0.756, -0.95]}, {"time": 0.8, "x": -19.97, "y": -0.95}]}, "bone2": {"translate": [{"x": -0.77, "y": -0.04, "curve": [0.111, -0.77, 0.222, 3.25, 0.111, -0.04, 0.222, 0.15]}, {"time": 0.3333, "x": 1.19, "y": 0.06, "curve": [0.444, -0.87, 0.556, -1.21, 0.444, -0.04, 0.556, -0.06]}, {"time": 0.6667, "x": -1.21, "y": -0.06, "curve": [0.711, -1.21, 0.756, 11.17, 0.711, -0.06, 0.756, 0.53]}, {"time": 0.8, "x": 11.17, "y": 0.53}]}, "bone10": {"rotate": [{"curve": [0.133, 0, 0.267, 5.2]}, {"time": 0.4, "value": 5.2, "curve": [0.533, 5.2, 0.667, 0]}, {"time": 0.8}]}, "bone6": {"rotate": [{"value": -2.87}, {"time": 0.4, "value": -30.62}, {"time": 0.8, "value": -2.87}]}, "bone3": {"rotate": [{"value": 5.73}, {"time": 0.4, "value": 24.78}, {"time": 0.8, "value": 5.73}]}}}, "entry": {"slots": {"body": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "head": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "left_arm": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "left_leg": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "right_arm": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}, "right_leg": {"rgba": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}]}}}, "walk": {"bones": {"bone": {"translate": [{"y": -15.45, "curve": [0.044, 0, 0.089, 5.09, 0.044, -15.45, 0.089, -10.14]}, {"time": 0.1333, "x": 5.09, "y": -7.32, "curve": [0.178, 5.09, 0.222, 0, 0.178, -4.51, 0.222, 1.44]}, {"time": 0.2667, "y": 1.44, "curve": [0.311, 0, 0.356, 0, 0.311, 1.44, 0.356, 0.71]}, {"time": 0.4, "y": -0.56, "curve": [0.444, 0, 0.489, 0, 0.444, -1.83, 0.489, -6.18]}, {"time": 0.5333, "y": -6.18, "curve": [0.578, 0, 0.622, 0, 0.578, -6.18, 0.622, -2.02]}, {"time": 0.6667, "y": 0.96, "curve": [0.711, 0, 0.756, 0, 0.711, 3.93, 0.756, 11.67]}, {"time": 0.8, "y": 11.67, "curve": [0.844, 0, 0.889, 0, 0.844, 11.67, 0.889, 2.8]}, {"time": 0.9333, "y": -1.72, "curve": [0.978, 0, 1.022, 0, 0.978, -6.24, 1.022, -15.45]}, {"time": 1.0667, "y": -15.45}]}, "right_leg": {"translate": [{"x": 0.37, "y": -0.89, "curve": [0.089, 0.37, 0.178, -0.75, 0.089, -0.89, 0.178, -0.94]}, {"time": 0.2667, "x": -0.75, "y": -0.94, "curve": [0.311, -0.75, 0.356, 9.63, 0.311, -0.94, 0.356, -0.45]}, {"time": 0.4, "x": 9.63, "y": -0.45, "curve": [0.444, 9.63, 0.489, 3.3, 0.444, -0.45, 0.489, -0.75]}, {"time": 0.5333, "x": 1.68, "y": -0.83, "curve": [0.578, 0.06, 0.622, -0.1, 0.578, -0.9, 0.622, -0.91]}, {"time": 0.6667, "x": -0.1, "y": -0.91, "curve": [0.711, -0.1, 0.756, 7.03, 0.711, -0.91, 0.756, -0.57]}, {"time": 0.8, "x": 7.03, "y": -0.57, "curve": [0.844, 7.03, 0.889, -0.99, 0.844, -0.57, 0.889, -0.95]}, {"time": 0.9333, "x": -0.99, "y": -0.95, "curve": [0.978, -0.99, 1.022, 0.37, 0.978, -0.95, 1.022, -0.89]}, {"time": 1.0667, "x": 0.37, "y": -0.89}], "scale": [{"x": 0.8, "y": 0.8, "curve": [0.044, 0.75, 0.089, 0.7, 0.044, 0.75, 0.089, 0.7]}, {"time": 0.1333, "x": 0.7, "y": 0.7, "curve": [0.222, 0.7, 0.311, 0.733, 0.222, 0.7, 0.311, 0.733]}, {"time": 0.4, "x": 0.8, "y": 0.8, "curve": [0.444, 0.833, 0.489, 0.95, 0.444, 0.833, 0.489, 0.95]}, {"time": 0.5333, "curve": [0.578, 1.05, 0.622, 1.1, 0.578, 1.05, 0.622, 1.1]}, {"time": 0.6667, "x": 1.1, "y": 1.1, "curve": [0.756, 1.1, 0.844, 1.067, 0.756, 1.1, 0.844, 1.067]}, {"time": 0.9333, "curve": [0.978, 0.967, 1.022, 0.85, 0.978, 0.967, 1.022, 0.85]}, {"time": 1.0667, "x": 0.8, "y": 0.8}]}, "left_leg": {"translate": [{"x": -6.43, "y": -0.3, "curve": [0.044, -6.43, 0.089, -5.35, 0.044, -0.3, 0.089, -0.25]}, {"time": 0.1333, "x": -3.25, "y": -0.15, "curve": [0.178, -1.15, 0.222, 3.85, 0.178, -0.05, 0.222, 0.18]}, {"time": 0.2667, "x": 6.18, "y": 0.29, "curve": [0.311, 8.52, 0.356, 10.01, 0.311, 0.4, 0.356, 0.47]}, {"time": 0.4, "x": 10.76, "y": 0.51, "curve": [0.444, 11.51, 0.489, 11.51, 0.444, 0.55, 0.489, 0.55]}, {"time": 0.5333, "x": 11.51, "y": 0.55, "curve": [0.578, 11.51, 0.622, 11.51, 0.578, 0.55, 0.622, 0.55]}, {"time": 0.6667, "x": 11.51, "y": 0.55, "curve": [0.711, 11.51, 0.756, 8.99, 0.711, 0.55, 0.756, 0.43]}, {"time": 0.8, "x": 7.95, "y": 0.38, "curve": [0.844, 6.91, 0.889, 7.67, 0.844, 0.33, 0.889, 0.36]}, {"time": 0.9333, "x": 5.27, "y": 0.25, "curve": [0.978, 2.88, 1.022, -6.43, 0.978, 0.14, 1.022, -0.3]}, {"time": 1.0667, "x": -6.43, "y": -0.3}], "scale": [{"time": 0.1333, "curve": [0.178, 0.967, 0.222, 0.933, 0.178, 0.967, 0.222, 0.933]}, {"time": 0.2667, "x": 0.9, "y": 0.9, "curve": [0.311, 0.867, 0.356, 0.833, 0.311, 0.867, 0.356, 0.833]}, {"time": 0.4, "x": 0.8, "y": 0.8, "curve": [0.444, 0.767, 0.489, 0.7, 0.444, 0.767, 0.489, 0.7]}, {"time": 0.5333, "x": 0.7, "y": 0.7, "curve": [0.667, 0.7, 0.8, 0.825, 0.667, 0.7, 0.8, 0.825]}, {"time": 0.9333, "x": 0.9, "y": 0.9, "curve": [0.978, 0.925, 1.022, 0.967, 0.978, 0.925, 1.022, 0.967]}, {"time": 1.0667}]}, "bone3": {"rotate": [{"curve": [0.178, 0, 0.356, 6.95]}, {"time": 0.5333, "value": 6.95, "curve": [0.711, 6.95, 0.889, 0]}, {"time": 1.0667}]}, "bone6": {"rotate": [{"curve": [0.178, 0, 0.356, -6.96]}, {"time": 0.5333, "value": -6.96, "curve": [0.711, -6.96, 0.889, 0]}, {"time": 1.0667}]}}, "attachments": {"default": {"left_leg": {"left_leg_white": {"deform": [{"vertices": [-5.39761, 0.36989, -4.66164, 4.51932, -5.47066, 3.49671, -3.2479, 4.7242, -4.12637, 3.97995, -1.83417, 4.92909, -2.78209, 4.46318, -0.0052, 5.19415, -1.04295, 5.08835, 1.95649, 5.47842, 0.82237, 5.75888, 2.93733, 5.62057, 1.75502, 6.09415, 3.91817, 5.76273, 2.68768, 6.42942, 4.22004, 2.75176, 5.82141, -1.09155, 6.59451, -2.94695, 4.42559, -4.89062, 5.31356, -3.9077, 2.77014, -5.21522, 3.75635, -4.55654, 0.6956, -4.70822, 1.62234, -4.47429, -1.15667, -3.09629, -0.51467, -3.26498, -3.64513, -3.43792, -2.88468, -4.09695, -6.13358, -3.77955, -1.49542, 0.9164, -1.64838, 0.59911, 0.27981, 1.16711, 0.04096, 1.19949, 2.26398, 1.43699, 1.9312, 1.86039, 3.49477, 1.68358, 3.08788, 2.34792, 0.55956, -2.78585, 1.10493, -2.61787, 2.57117, -2.60027, 3.03889, -2.03407, 4.08955, -2.51727, 4.51007, -1.64936, -3.44652, 0.64314]}]}}}}}}}